<template>
	<view class="page-container">
		<navbar title="守护值记录" show-nav-back :on-nav-click="backToPreviousPage"></navbar>
		<view v-if="scoreHistoryList.length === 0" class="guard-score-no-data">
			<text>暂无守护记录</text>
		</view>
		<view
			v-else
			class="guard-score-container"
			:style="`height: calc(100vh - 34rpx - ${navbarInfo.barHeight}px); padding-top:${navbarInfo.barHeight}px;`"
		>
			<view class="guard-score-lists">
				<ScoreCard v-for="(item, index) in scoreHistoryList" :key="index" :guard-history="item" />
			</view>
		</view>
		<view v-if="scoreHistoryList.length !== 0" class="guard-score-bottom">
			<text>仅展示最新前50条</text>
		</view>
	</view>
</template>
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useStore } from 'vuex'
import ScoreCard from './components/ScoreCard.vue'
import GuardService from '@/service/guard'
import { ScoreHistoryItem } from '@/types/guard.d'
import { PageParams } from '@/types/base.d'

const store = useStore()
const navbarInfo = computed(() => store.getters.navbarInfo)

const scoreHistoryList = ref<ScoreHistoryItem[]>([])
const queryParams = ref<PageParams>({
	pageNum: 1,
	pageSize: 50
})

const getScoreHistory = async () => {
	const { data } = await GuardService.getScoreHistory(queryParams.value)
	scoreHistoryList.value = data?.list || []
}

const backToPreviousPage = () => {
	uni.navigateBack({
		delta: 1
	})
}
onMounted(async () => {
	await getScoreHistory()
})
</script>
<style scoped lang="scss">
.page-container {
	height: 100vh;
	display: flex;
	flex-direction: column;
}
.guard-score-no-data {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	color: #acacac;
}
.guard-score-container {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 32rpx 32rpx;
	box-sizing: border-box;

	.guard-score-lists {
		padding-top: 32rpx;
		overflow-y: auto;
		&::-webkit-scrollbar {
			display: none;
			width: 0;
			height: 0;
			color: transparent;
		}
	}
}

.guard-score-bottom {
	height: 34rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	color: #acacac;
	margin-bottom: 68rpx;
}
</style>
