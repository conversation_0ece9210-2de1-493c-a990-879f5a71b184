<template>
	<view class="score-card">
		<view class="score-card-time">
			{{ guardHistory.actionTime }}
		</view>
		<view class="score-card-body">
			<view class="score-card-body-left">
				<view
					class="left-icon"
					:style="{ 'background-image': `url(${typeInfo.typeIconUrl})` }"
				></view>
				<view class="left-text">
					<view class="left-text-title">{{ typeInfo.typeName }}</view>
					<view class="left-text-score">守护总值：{{ guardHistory.currentPoints }}</view>
				</view>
			</view>
			<view class="score-card-body-right">守护值+{{ guardHistory.points }}</view>
		</view>
	</view>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { imgBaseUrl } from '../../../config'
import { ScoreHistoryItem } from '@/types/guard.d'

const props = withDefaults(
	defineProps<{
		guardHistory: ScoreHistoryItem
	}>(),
	{
		guardHistory: () => ({}) as ScoreHistoryItem
	}
)

type ScoreTypeKey = 'SIGN' | 'PUBLISH' | 'GUARD' | 'SHARE' | 'SCAN'
const ScoreType: Record<ScoreTypeKey, number[]> = {
	SIGN: [61],
	PUBLISH: [71],
	GUARD: [21, 31, 41, 51],
	SHARE: [81],
	SCAN: [82]
}

const SCORE_TYPE_NAME: Record<ScoreTypeKey, string> = {
	SIGN: '签到',
	PUBLISH: '发布动态',
	GUARD: '线上守护',
	SHARE: '分享小程序',
	SCAN: '新用户扫码'
}

const SCORE_TYPE_ICON: Record<ScoreTypeKey, string> = {
	SIGN: `${imgBaseUrl}guard/guard-score-history-sign.png`,
	PUBLISH: `${imgBaseUrl}guard/guard-score-history-action.png`,
	GUARD: `${imgBaseUrl}guard/guard-score-history-weeding.png`,
	SHARE: `${imgBaseUrl}guard/guard-score-history-share.png`,
	SCAN: `${imgBaseUrl}guard/guard-score-history-scan.png`
}

type ScoreTypeInfo = {
	typeName: string
	typeIconUrl: string
}
const typeInfo = ref<ScoreTypeInfo>({
	typeName: '',
	typeIconUrl: ''
})

const getScoreTypeInfo = (type: number) => {
	for (const key in ScoreType) {
		if (ScoreType[key as ScoreTypeKey].includes(type)) {
			return {
				typeName: SCORE_TYPE_NAME[key as ScoreTypeKey],
				typeIconUrl: SCORE_TYPE_ICON[key as ScoreTypeKey]
			}
		}
	}
	return {} as ScoreTypeInfo
}

watch(
	() => props.guardHistory,
	(val) => {
		typeInfo.value = getScoreTypeInfo(val.guardType)
	},
	{
		immediate: true
	}
)
</script>
<style scoped lang="scss">
.score-card {
	width: 686rpx;
	height: 134rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	margin-bottom: 48rpx;

	.score-card-time {
		font-size: 14px;
		color: rgba($color: #151515b2, $alpha: 0.7);
	}

	.score-card-body {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 670rpx;
		height: 78rpx;
		.score-card-body-left {
			display: flex;
			height: 78rpx;
			align-items: center;
			.left-icon {
				width: 64rpx;
				height: 64rpx;
				background-repeat: no-repeat;
				background-size: cover;
				margin-right: 7rpx;
			}

			.left-text {
				.left-text-title {
					font-size: 16px;
					font-family: PingFang SC;
				}

				.left-text-score {
					font-size: 12px;
					font-family: PingFang SC;
					color: #737373b2;
				}
			}
		}
		.score-card-body-right {
			font-size: 14px;
			color: #5c9709;
			margin-right: 32rpx;
		}
	}
}
</style>
