<template>
	<view
		:class="[activeListData.length === 0 ? 'page-null-bg' : '', 'page-container']"
		:style="`padding-top: ${navbarInfo.barHeight}px;`"
	>
		<navbar title="云活动" show-nav-back :on-nav-click="backToPreviousPage"></navbar>
		<view v-if="activeListData.length !== 0" class="active-list">
			<scroll-view scroll-y class="scroll-view-container" @scrolltolower="onReachBottom">
				<view class="active-list-container">
					<view v-for="(cardInfo, index) in activeListData" :key="cardInfo.id">
						<activeCard :card-info="cardInfo" :index="index" :card-length="cardLength"></activeCard>
					</view>
				</view>
				<uni-load-more :status="status" :content-text="contentText" />
			</scroll-view>
			<view v-show="isAdministrator" class="plus-records" @click="handleClickPlus">
				<image
					:src="`${imgBaseUrl}guard/guard-records-circle.png`"
					class="plus-records-circle"
				></image>
				<image :src="`${imgBaseUrl}guard/guard-records-plus.png`" class="plus-records-plus"></image>
			</view>
		</view>
		<view v-else class="activity-null">
			<image
				:src="`${imgBaseUrl}activity/activity-page-null.png`"
				mode="scaleToFill"
				class="activity-null-img"
			/>
		</view>
	</view>
</template>
<script setup lang="ts">
import { useStore } from 'vuex'
import { onMounted, computed, ref } from 'vue'
import ActivityService from '@/service/activity'
import activeCard from './components/activeCard.vue'
import { imgBaseUrl } from '@/config.js'
import { ActivityBaseInfo } from '@/types/activity.d'

const store = useStore()

const navbarInfo = computed(() => store.getters.navbarInfo)
const isAdministrator = computed(() => store.getters.ispan)
const pageNum = ref(1)
const pageSize = ref(10)
const activeListData = ref<ActivityBaseInfo[]>([])
const status = ref<'loading' | 'more' | 'noMore'>('more')
const cardLength = ref(0)
const contentText = ref({
	contentdown: '上拉显示更多',
	contentrefresh: '正在加载',
	contentnomore: '没有更多数据了'
})

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}

function onReachBottom() {
	if (status.value !== 'noMore') {
		pageNum.value += 1
		getActiveListData()
	} else {
		return
	}
}

async function getActiveListData() {
	status.value = 'loading'
	const params = {
		pageNum: pageNum.value,
		pageSize: pageSize.value
	}
	try {
		const resData = await ActivityService.getActivityList(params)
		if (resData.code === 1) {
			const newActiveListData = resData.data?.list || []
			if (newActiveListData && newActiveListData.length > 0 && resData.data?.hasNextPage) {
				activeListData.value = [...activeListData.value, ...newActiveListData]
				cardLength.value = activeListData.value.length
				status.value = 'more'
			} else {
				status.value = 'noMore'
			}
		}
	} catch (error) {
		status.value = 'noMore'
		console.error(error)
	}
}

function handleClickPlus() {
	uni.navigateTo({
		url: 'pages/activity/index'
	})
}

onMounted(async () => {
	await getActiveListData()
})
</script>

<style scoped lang="scss">
.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
}

.page-null-bg {
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/activity/activity-page-bg.png');
	background-repeat: no-repeat;
	background-size: contain;
	padding: 0 !important;
}

.activity-null {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 100vh;
	.activity-null-img {
		width: 395rpx;
		height: 440rpx;
	}
}
.active-list {
	height: 100%;
}
.scroll-view-container {
	height: 100%;
	background: #f2f2f2;
}
.active-list-container {
	margin: 8px 18px 0px 18px;
}
.plus-records {
	.plus-records-circle {
		width: 104rpx;
		height: 104rpx;
		bottom: 70rpx;
		right: 18px;
		position: absolute;
	}
	.plus-records-plus {
		width: 64rpx;
		height: 64rpx;
		bottom: 94rpx;
		right: 29px;
		position: absolute;
	}
}
</style>
