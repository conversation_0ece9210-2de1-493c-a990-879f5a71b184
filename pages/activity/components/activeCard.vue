<template>
	<view
		class="card"
		:class="{
			'first-card': index === 0,
			'last-card': index === cardLength - 1
		}"
		@click="handleToDetail"
	>
		<view class="card-left">
			<image class="card-cover" :src="imgUrl"></image>
		</view>
		<view class="card-right">
			<view class="card-right-info">
				<view class="title">{{ cardInfo?.name }}</view>
				<view
					class="register-status"
					:style="{
						backgroundColor:
							isRegistered === 1 ? 'rgba(255, 157, 48, 1)' : `${STATUS_COLOR_MAP[cardInfo?.status]}`
					}"
				>
					<view class="register-status-text">{{ isRegistered === 1 ? '已报名' : status }}</view>
				</view>
			</view>
			<view class="active-time">活动时间：{{ cardInfo?.activityTime }}</view>
			<view class="active-time">报名时间：{{ cardInfo?.registrationDeadline }} 前</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ActivityBaseInfo } from '@/types/activity.d'
import { ACTIVE_STATUS_MAP, STATUS_COLOR_MAP } from '../settings/config'

const props = withDefaults(
	defineProps<{
		cardInfo: ActivityBaseInfo
		index: number
		cardLength: number
	}>(),
	{
		cardInfo: () => ({}) as ActivityBaseInfo,
		index: 0,
		cardLength: 0
	}
)

const status = computed(() => ACTIVE_STATUS_MAP[props.cardInfo?.status])
const isRegistered = computed(() => props.cardInfo?.isRegistered)
const imgUrl = computed(() => props.cardInfo?.attachments?.[0]?.url ?? '')

const handleToDetail = () => {
	uni.navigateTo({
		url: `/pages/activity/apply?id=${props.cardInfo.id}`
	})
}
</script>

<style scoped lang="scss">
.card {
	display: flex;
	border-bottom: 1px solid #e4e4e4;
	box-shadow: 0px 2px 8px 0px #4d1f0005;
	background: #ffffff;
}
.first-card {
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
}
.last-card {
	border-bottom-left-radius: 8px;
	border-bottom-right-radius: 8px;
}
.card-left {
	margin: 12px;
}
.card-cover {
	width: 144rpx;
	height: 144rpx;
	border-radius: 4px;
}
.card-right {
	margin: 12px 4px 12px 0px;
	display: flex;
	flex-direction: column;
	flex: 1;
}
.card-right-info {
	display: flex;
	justify-content: space-between;
	flex-direction: row;
	margin-bottom: 12px;
}
.title {
	color: #000000;
	font-weight: 600;
	font-size: 14px;
	line-height: 19.6px;
	display: flex;
	flex: 1;
}
.active-time {
	font-size: 13px;
	font-weight: 400;
	line-height: 18.2px;
	color: #151515b2;
}
.register-status {
	width: 88rpx;
	height: 36rpx;
	border-radius: 4px;
	margin-right: 8px;
	justify-content: center;
	align-items: center;
	display: flex;
}
.register-status-text {
	font-family: Douyin Sans;
	font-size: 12px;
	font-weight: 700;
	line-height: 14.42px;
	text-align: center;
	color: #ffffff;
}
</style>
