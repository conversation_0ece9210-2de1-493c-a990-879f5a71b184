<template>
	<view class="container">
		<navbar
			title="云活动"
			show-nav-back
			:on-nav-click="onNavBack"
			:opacity="navOpacity"
			:ele-color="navEleColor"
		>
		</navbar>
		<view class="swiper-area">
			<uni-swiper-dot
				class="uni-swiper-dot-box"
				:info="swiperImgs"
				:current="current"
				mode="default"
				:dots-styles="dotsStyles"
				@click-item="clickItem"
			>
				<swiper class="swiper-box" :current="current" @change="change">
					<swiper-item v-for="(item, index) in swiperImgs" :key="index" class="swiper-item">
						<image :src="item.url" mode="aspectFill" class="cover-img" />
					</swiper-item>
				</swiper>
			</uni-swiper-dot>
		</view>
		<view class="bottom-area">
			<view
				class="bottom-area-bc"
				:style="{
					backgroundImage: `url(${currentImageUrl || defaultImageUrl})`
				}"
			/>
			<view class="title-area">
				<text>{{ activityInfo.name }}</text>
				<image
					:src="
						activityInfo.isRegistered == 1 ? registeredStatus : applyStatus[activityInfo.status]
					"
					class="status-icon"
				/>
			</view>
			<view class="detail-area-first">
				<view v-for="item in applyStatusText" :key="item.fields" class="detail-area-first-item">
					<image :src="item.iconUrl" class="item-icon" />
					<text class="item-title">{{ item.title }}</text>
					<view class="item-content">{{
						activityInfo[item.fields as keyof ActivityDetailInfo]
					}}</view>
				</view>
			</view>
			<view class="activity-introduction">
				<view class="activity-introduction-title">活动介绍</view>

				<view class="activity-introduction-content">{{ activityInfo.description }}</view>
			</view>
			<view v-if="isPinned" class="admin-btn-area">
				<view class="admin-btn-registrationCount">
					当前报名人数：
					<text>{{ activityInfo.registrationCount }}/{{ activityInfo.recruitmentQuota }}</text>
				</view>
				<view class="admin-btn-edit-apply">
					<image
						:src="`${imgBaseUrl}activity/activity-apply-edit-icon.png`"
						class="edit-icon"
						@click="onEditActivity"
					/>
					<button
						class="confirm-btn edit-confirm-btn"
						:class="
							activityInfo.isRegistered == 1 || activityInfo.status != 1
								? 'confirm-btn-display'
								: ''
						"
						@click="onOpenApplyPopup"
					>
						我要报名
					</button>
				</view>
			</view>

			<button
				v-else
				class="confirm-btn"
				:class="
					activityInfo.isRegistered == 1 || activityInfo.status != 1 ? 'confirm-btn-display' : ''
				"
				@click="onOpenApplyPopup"
			>
				我要报名
			</button>
		</view>

		<uni-popup
			ref="applyPopup"
			type="bottom"
			background-color="#fff"
			:mask-click="false"
			:is-mask-click="false"
			border-radius="16px 16px 0 0 "
		>
			<view class="apply-popup-content">
				<view class="apply-input-area">
					<text>请填写用于接收活动短信通知的手机号：</text>
					<input
						v-model="phone"
						class="apply-input-phone"
						type="text"
						placeholder=""
						maxlength="13"
						@input="formatPhone"
					/>
				</view>
				<view class="apply-btn-area">
					<button class="apply-btn-item" @click.stop="onCancelApplyPopup">取消</button>
					<button class="apply-btn-item" @click.stop="onSendPhone">确认</button>
				</view>
			</view>
		</uni-popup>

		<uni-popup
			ref="successPopup"
			type="center"
			background-color="#fff"
			border-radius="16px 16px 0 0 "
		>
			<view class="success-popup-content">
				<view
					class="success-content"
					:style="{
						backgroundImage: `url(${imgBaseUrl}activity/activity-apply-success-popup-bc.png)`
					}"
				>
					<text class="success-content-detail">
						您已报名"{{
							activityInfo.name
						}}"活动，报名成功预留手机号将收到短信通知。期待与您在活动现场的美妙邂逅！
					</text>
				</view>
				<button class="success-btn" @click="onCancelSuccessPopup">知道了</button>
				<text class="countdown">{{ countdown }}S</text>
			</view>
		</uni-popup>
	</view>
</template>

<script lang="ts">
export default {
	options: { styleIsolation: 'shared' }
}
</script>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'
import ActivityService from '@/service/activity'
import { ActivityDetailInfo, ActivityStatus } from '@/types/activity.d'
import { applyStatus, applyStatusText, dotsStyles } from './settings/config'
import { imgBaseUrl } from '@/config.js'
import { UniPopupInstance } from '@uni-helper/uni-ui-types'

const store = useStore()
const defaultImageUrl = `${imgBaseUrl}activity/activity-activity-add-bc.png`
const registeredStatus = applyStatus[3]

// data
const isPinned = computed(() => store.state.userInfo.pinned)
const current = ref(0)
const activityId = ref()
const currentImageUrl = ref('')
const activityInfo = ref<ActivityDetailInfo>({
	benefits: '',
	description: '',
	organizer: '',
	notes: '',
	place: '',
	recruitmentQuota: 0,
	registrationCount: 0,
	id: 0,
	name: '',
	activityTime: '',
	isRegistered: 0,
	registrationDeadline: '',
	status: ActivityStatus.UN_START,
	attachments: []
})
// popup
const applyPopup = ref<UniPopupInstance | null>(null)
const successPopup = ref<UniPopupInstance | null>(null)
const phone = ref('')
const rawPhone = ref('')
// timer
let timer: ReturnType<typeof setTimeout> | null = null
const countdown = ref(5)
// api instance
const navEleColor = ref('#FFFFFF')
const navOpacity = ref(0)
const swiperImgs = computed(() => {
	const attachments = activityInfo.value.attachments
	if (!attachments || attachments.length === 0) {
		return [{ url: defaultImageUrl }]
	}
	return attachments
})

const onEditActivity = () => {
	uni.redirectTo({
		url: `/pages/activity/form?id=${activityId.value}`
	})
}
const onOpenApplyPopup = () => {
	if (activityInfo.value.isRegistered == 1 || activityInfo.value.status != 1) {
		return
	}
	if (applyPopup.value) {
		;(applyPopup.value as any).open()
	}
}
const onCancelApplyPopup = () => {
	if (applyPopup.value) {
		;(applyPopup.value as any).close()
	}
	phone.value = ''
	rawPhone.value = ''
}

const onCancelSuccessPopup = async () => {
	await initActivityInfo()
	if (successPopup.value) {
		;(successPopup.value as any).close()
	}
}

const onSendPhone = async () => {
	try {
		const res = await ActivityService.postRegisterActivity({
			activityId: activityId.value,
			phone: rawPhone.value
		})
		if (res.code == 1) {
			if (successPopup.value) {
				;(successPopup.value as any).open()
			}
			startCountdown()
		}
		onCancelApplyPopup()
	} catch (error) {
		console.error(error)
	}
}

const startCountdown = () => {
	countdown.value = 5
	if (timer) clearInterval(timer)
	timer = setInterval(() => {
		if (countdown.value > 1) {
			countdown.value--
		} else {
			if (timer) clearInterval(timer)
			countdown.value = 0
			onCancelSuccessPopup()
		}
	}, 1000)
}

const formatPhone = (event: any) => {
	let value = event.target.value.replace(/\D/g, '')
	rawPhone.value = value
	if (value.length > 3 && value.length <= 7) {
		value = `${value.slice(0, 3)} ${value.slice(3)}`
	} else if (value.length > 7) {
		value = `${value.slice(0, 3)} ${value.slice(3, 7)} ${value.slice(7)}`
	}
	phone.value = value
}

const onNavBack = () => {
	if (successPopup.value) {
		;(successPopup.value as any).close()
	}
	if (timer) clearInterval(timer)
	uni.navigateBack({
		delta: 1
	})
}

const change = (e: any) => {
	if (e == 0) {
		current.value = 0
	} else {
		current.value = e.detail.current
	}
	currentImageUrl.value = activityInfo.value.attachments?.[current.value]?.url || ''
}

const clickItem = (index: number) => {
	current.value = index
}

const initActivityInfo = async () => {
	try {
		const { data } = await ActivityService.getDetailedInfo(activityId.value)
		if (data) {
			activityInfo.value = data
			change(0)
		}
	} catch (error) {
		console.error(error)
	}
}

onLoad(async (options) => {
	activityId.value = options.id
	await initActivityInfo()
})

onPageScroll((e) => {
	if (e.scrollTop <= 22) {
		navOpacity.value = e.scrollTop / 22
		navEleColor.value = '#ffffff'

		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: 'transparent'
		})
	} else {
		navOpacity.value = 1
		navEleColor.value = '#000000'
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: 'transparent'
		})
	}
})
</script>

<style lang="scss" scoped>
@mixin flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

@mixin font-sc-14-500 {
	font-family: PingFang SC;
	font-size: 14px;
	font-weight: 500;
}

.container {
	width: 100%;
	min-height: 100vh;
	margin-bottom: 120rpx;
	overflow-y: scroll;
	&::-webkit-scrollbar {
		display: none;
	}

	.swiper-area {
		width: 100%;
		height: 902rpx;
		border-radius: 0px 0px 20px 20px;
		.swiper-box {
			height: 100%;
			.swiper-item {
				.cover-img {
					width: 100%;
					height: 100%;
					border-radius: 0px 0px 20px 20px;
				}
			}
		}
	}
	.bottom-area {
		width: 100%;
		box-sizing: border-box;
		padding: 24rpx 36rpx 0 36rpx;
		display: flex;
		flex-direction: column;
		gap: 40rpx;
		position: relative;
		.bottom-area-bc {
			background-size: cover;
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: -1;
			filter: blur(20px);
		}

		.title-area {
			position: relative;
			width: 100%;
			text {
				@include font-sc-14-500;
				font-size: 28px;
				color: rgba(255, 255, 255, 1);
			}
			.status-icon {
				height: 36rpx;
				width: 104rpx;
				border-radius: 40rpx;
				position: absolute;
				right: 0;
				bottom: 0;
			}
		}
		.detail-area-first {
			display: flex;
			flex-direction: column;
			width: 100%;
			box-sizing: border-box;
			border-radius: 24rpx;
			padding: 42rpx 0 36rpx 36rpx;
			background-color: rgba(0, 0, 0, 0.25);
			gap: 28rpx;

			.detail-area-first-item {
				@include flex-center;
				align-items: flex-start;
				justify-content: flex-start;
				color: rgba(255, 255, 255, 1);
				.item-icon {
					width: 36rpx;
					height: 36rpx;
					margin-right: 16rpx;
				}
				.item-title {
					@include font-sc-14-500;
					font-weight: 300;
					margin-right: 12rpx;
				}
				.item-content {
					@include font-sc-14-500;
					max-width: 424rpx;
				}
			}
		}
		.activity-introduction {
			width: 100%;
			.activity-introduction-title {
				@include font-sc-14-500;
				color: rgba(255, 255, 255, 1);
				margin-bottom: 16rpx;
			}
			.activity-introduction-content {
				font-family: PingFang SC;
				font-size: 12px;
				font-weight: 300;
				color: rgba(255, 255, 255, 1);
			}
		}
		.admin-btn-area {
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			gap: 24rpx;
			margin-bottom: 120rpx;

			.admin-btn-registrationCount {
				@include flex-center;
				background-color: rgba(0, 0, 0, 0.4);
				width: fit-content;
				padding: 6px 12px;
				height: 64rpx;
				font-family: PingFang SC;
				font-size: 10px;
				font-weight: 300;
				color: rgba(255, 255, 255, 1);
				border-radius: 40rpx;
				text {
					font-family: PingFang SC;
					font-size: 10px;
					font-weight: 500;
				}
			}
			.admin-btn-edit-apply {
				@include flex-center;
				gap: 32rpx;

				.edit-icon {
					width: 88rpx;
					height: 88rpx;
				}
				.edit-confirm-btn {
					width: 560rpx;
					margin: 0;
				}
			}
		}
		.confirm-btn {
			@include flex-center;
			width: 100%;
			height: 88rpx;
			color: rgba(255, 255, 255, 1);
			border-radius: 88rpx;
			margin-bottom: 120rpx;
			margin-top: 8rpx;
			font-family: PingFang SC;
			font-size: 18px;
			font-weight: 600;

			background: linear-gradient(0deg, #ff9d30 1.14%, #ff9d30 52.14%, #ffb555 101.14%);
			border: 1px solid;

			border-image-source: linear-gradient(
				90.77deg,
				rgba(255, 255, 255, 0.6715) 1.24%,
				rgba(255, 255, 255, 0) 52.44%,
				rgba(255, 255, 255, 0.5865) 97.84%
			);
		}
		.confirm-btn-display {
			background: rgba(140, 140, 140, 1);
		}
	}
}

.apply-popup-content {
	height: 468rpx;
	width: 100%;
	box-sizing: border-box;
	padding: 88rpx 60rpx 0 60rpx;
	.apply-input-area {
		text {
			@include font-sc-14-500;
			color: rgba(0, 0, 0, 1);
		}
		.apply-input-phone {
			@include font-sc-14-500;
			font-weight: 400;
			color: rgba(0, 0, 0, 1);
			padding-left: 12px;
			height: 64rpx;
			border-radius: 16rpx;
			margin-top: 36rpx;
			box-shadow:
				-4px 0px 4px 0px rgba(167, 167, 167, 0.05),
				4px 0px 4px 0px rgba(0, 0, 0, 0.05),
				0px -4px 4px 0px rgba(0, 0, 0, 0.05),
				0px 4px 4px 0px rgba(0, 0, 0, 0.05);
		}
	}
	.apply-btn-area {
		@include flex-center;
		gap: 32rpx;
		margin-top: 72rpx;
		.apply-btn-item {
			@include flex-center;
			@include font-sc-14-500;
			font-size: 17px;
			border-radius: 88rpx;
			margin: 0;
			width: 246rpx;
			height: 72rpx;
			&:first-child {
				background-color: rgba(229, 229, 229, 1);
				color: rgba(116, 116, 116, 1);
			}
			&:last-child {
				background: linear-gradient(0deg, #ff9212 42.09%, #ffb763 101.14%);
				color: rgba(255, 255, 255, 1);
			}
		}
	}
}

.success-popup-content {
	width: 590rpx;
	@include flex-center;
	flex-direction: column;
	.success-content {
		width: 100%;
		height: 620rpx;
		position: relative;
		background-size: cover;
		.success-content-detail {
			@include font-sc-14-500;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.55);
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			bottom: 90rpx;
			width: 490rpx;
		}
	}
	.success-btn {
		@include flex-center;
		@include font-sc-14-500;
		font-size: 17px;
		height: 72rpx;
		width: 174rpx;
		border: 1px solid rgba(204, 204, 204, 1);
		background: transparent;
		margin-top: 48rpx;
		border-radius: 88rpx;
		color: rgba(204, 204, 204, 1);
	}
	.countdown {
		@include flex-center;
		margin-top: 48rpx;
		border-radius: 14px;
		height: 56rpx;
		width: 60rpx;
		font-family: PingFang SC;
		font-size: 16px;
		font-weight: 400;
		color: rgba(252, 252, 252, 1);

		background-color: rgba(255, 255, 255, 0.1);
	}
}

.uni-swiper-dot-box :deep(.uni-swiper__warp) {
	height: 100%;
}
.uni-swiper-dot-box :deep(.uni-swiper__dots-box) {
	bottom: 50rpx !important;
}

:deep(.uni-popup__wrapper) {
	background: transparent !important;
}
</style>
