<template>
	<view
		class="container"
		:style="{ backgroundImage: `url(${imgBaseUrl}activity/activity-activity-bc.png)` }"
	>
		<navbar title="云活动" show-nav-back :on-nav-click="onNavBack"></navbar>
		<view
			v-if="isPinned || swiperInfo.length > 0"
			class="swiper-area"
			:style="{ marginTop: `calc(${navbarInfo.barHeight * 2}rpx + 43rpx)` }"
		>
			<uni-swiper-dot
				class="uni-swiper-dot-box"
				:info="swiperInfo"
				:current="current"
				mode="default"
				:dots-styles="dotsStyles"
				@click-item="clickItem"
			>
				<swiper
					class="swiper-box"
					:current="current"
					previous-margin="35px"
					next-margin="35px"
					@change="change"
				>
					<swiper-item v-if="isPinned" class="admin-item">
						<view
							class="admin-item-add-content"
							:class="{ 'active-item': current === 0 }"
							:style="{
								backgroundImage: `url(${imgBaseUrl}activity/activity-activity-add-bc.png)`
							}"
						>
							<img
								:src="`${imgBaseUrl}activity/activity-activity-add-icon.png`"
								class="add-icon"
								@click="onSkipPage('create')"
							/>
							<text>创建一个新活动</text>
						</view>
					</swiper-item>
					<swiper-item v-for="(item, index) in swiperInfo" :key="index" class="swiper-item">
						<view
							class="swiper-item-content"
							:class="{ 'active-item': current === (isPinned ? index + 1 : index) }"
							:style="{
								backgroundImage: `url(${((item.attachments || {})[0] || {}).url || defaultImageUrl})`
							}"
						>
							<image
								:src="`${imgBaseUrl}activity/activity-card-mask.png`"
								class="swiper-item-mask"
							/>
							<img
								:src="
									item.isRegistered == 1 ? registeredActivityStatus : activityStatus[item.status]
								"
								class="swiper-item-status"
							/>
							<view class="swiper-item-bottom-area">
								<view class="swiper-item-text-area">
									<text class="swiper-item-text-item">{{ item.name }}</text>
									<text class="swiper-item-text-item">活动时间：{{ item.activityTime }}</text>
									<text class="swiper-item-text-item"
										>报名时间：{{ item.registrationDeadline }} 前</text
									>
								</view>
								<button class="swiper-item-button" @click="onSkipPage('apply', item.id)">
									进入报名
								</button></view
							>
						</view>
					</swiper-item>
				</swiper>
			</uni-swiper-dot>
		</view>
		<view v-if="!isPinned && swiperInfo.length === 0" class="activity-null">
			<image
				:src="`${imgBaseUrl}activity/activity-page-null.png`"
				mode="scaleToFill"
				class="activity-null-img"
			/>
		</view>
		<view v-if="swiperInfo.length !== 0" class="bottom-area" @click="onSkipPage('more')"
			>更多活动 ></view
		>
	</view>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { ref, computed } from 'vue'
import { dotsStyles, activityStatus, skipPageName } from './settings/config.js'
import { imgBaseUrl } from '@/config.js'
import ActivityService from '@/service/activity'
import { onShow } from '@dcloudio/uni-app'
import { ActivityBaseInfo } from '@/types/activity.d'

const store = useStore()

const defaultImageUrl = `${imgBaseUrl}activity/activity-activity-add-bc.png`
const registeredActivityStatus = activityStatus[3]

const current = ref(0)
const isPinned = computed(() => store.state.userInfo.pinned)
const navbarInfo = computed(() => store.state.navbarInfo)
const swiperInfo = ref<ActivityBaseInfo[]>([])

const onSkipPage = (pageName: keyof typeof skipPageName, id: number | null = null) => {
	if (id) {
		uni.navigateTo({
			url: skipPageName[pageName] + '?id=' + id
		})
	} else {
		uni.navigateTo({
			url: skipPageName[pageName]
		})
	}
}

const onNavBack = () => {
	uni.navigateBack({
		delta: 1
	})
}

const change = (e: any) => {
	current.value = e.detail.current
}

const clickItem = (index: number) => {
	current.value = index
}

onShow(async () => {
	try {
		const res = await ActivityService.getActivityList({
			pageNum: 1,
			pageSize: 10
		})
		swiperInfo.value = res.data?.list || []
	} catch (error) {
		console.error(error)
	}
})
</script>

<style lang="scss" scoped>
@mixin flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

.container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	.swiper-area {
		width: 100%;

		.admin-item {
			@include flex-center;
			.admin-item-add-content {
				@include flex-center;
				width: 500rpx;
				height: 884rpx;
				transition: transform 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
				background-size: cover;
				flex-direction: column;
				gap: 42rpx;
				position: relative;
				&:before {
					content: '';
					position: absolute;
					top: -5px;
					left: -5px;
					right: -5px;
					bottom: -5px;
					border: 5px solid white;
					pointer-events: none;
					z-index: -1;
					border-radius: 50rpx;
				}
				.add-icon {
					height: 116rpx;
					width: 116rpx;
				}
				text {
					font-family: PingFang SC;
					font-size: 18px;
					font-weight: 600;
					color: rgba(255, 157, 48, 1);
				}
			}
		}

		.swiper-item {
			@include flex-center;
			.swiper-item-content {
				position: relative;
				width: 500rpx;
				height: 884rpx;
				border-radius: 40rpx;
				transition: transform 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
				background-repeat: no-repeat;
				background-size: cover;

				.swiper-item-mask {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
				}

				&:before {
					content: '';
					position: absolute;
					top: -5px;
					left: -5px;
					right: -5px;
					bottom: -5px;
					border: 5px solid white;
					pointer-events: none;
					z-index: -1;
					border-radius: 50rpx;
				}
				.swiper-item-status {
					position: absolute;
					left: 32rpx;
					top: 42rpx;
					width: 188rpx;
					height: 36rpx;
					background-size: cover;
				}
				.swiper-item-bottom-area {
					width: 100%;
					box-sizing: border-box;
					padding: 0 32rpx 0 32rpx;
					position: absolute;
					bottom: 62rpx;
					display: flex;
					align-items: flex-end;

					.swiper-item-text-area {
						@include flex-center;
						flex: 1;
						align-items: flex-start;
						flex-direction: column;

						.swiper-item-text-item {
							font-family: PingFang SC;
							font-size: 13px;
							font-weight: 400;
							color: rgba(255, 255, 255, 1);
							&:first-child {
								font-size: 20px;
								font-weight: 600;
							}
						}
					}
					.swiper-item-button {
						@include flex-center;
						width: fit-content;

						background: linear-gradient(0deg, #ff9212 42.09%, #ffb763 101.14%);
						height: 58rpx;
						margin: 0;
						border-radius: 88rpx;
						white-space: nowrap;
						font-family: Douyin Sans;
						font-size: 14px;
						font-weight: 700;
						color: rgba(255, 255, 255, 1);
					}
				}
			}
		}
	}
	.bottom-area {
		@include flex-center;
		margin-top: 100rpx;
		font-family: PingFang SC;
		font-size: 14px;
		font-weight: 500;
		line-height: 14px;
		color: rgba(21, 21, 21, 1);
	}
}

.activity-null {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 100vh;
	.activity-null-img {
		width: 395rpx;
		height: 440rpx;
	}
}

.uni-swiper-dot-box {
	position: relative;
}

.swiper-box {
	@include flex-center;
	width: 100%;
	height: 1100rpx;
	overflow: hidden;
}

.active-item {
	width: 590rpx !important;
	height: 1042rpx !important;
	transition: transform 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
	z-index: 10;
}

.uni-swiper-dot-box :deep(.uni-swiper__dots-box) {
	bottom: 0 !important;
}
</style>
