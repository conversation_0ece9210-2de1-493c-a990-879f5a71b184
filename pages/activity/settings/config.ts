import { imgBaseUrl } from '@/config.js'
import { ActivityStatus } from '@/types/activity.d'

export const dotsStyles = {
	backgroundColor: 'rgba(227, 227, 227, 1)',
	border: '',
	color: 'red',
	selectedBackgroundColor: 'rgba(255, 146, 18, 1)',
	selectedBorder: ''
}

export const activityStatus = {
	0: '',
	1: imgBaseUrl + 'activity/activity-activity-ing.png',
	2: imgBaseUrl + 'activity/activity-activity-end.png',
	3: imgBaseUrl + 'activity/activity-activity-finish.png'
}

export const applyStatus = {
	0: '',
	1: imgBaseUrl + 'activity/activity-apply-ing.png',
	2: imgBaseUrl + 'activity/activity-apply-end.png',
	3: imgBaseUrl + 'activity/activity-apply-finish.png'
}

export const applyStatusText = [
	{
		iconUrl: imgBaseUrl + 'activity/activity-activity-time.png',
		title: '活动时间',
		fields: 'activityTime'
	},
	{
		iconUrl: imgBaseUrl + 'activity/activity-activity-location.png',
		title: '活动地点',
		fields: 'place'
	},
	{
		iconUrl: imgBaseUrl + 'activity/activity-activity-benefit.png',
		title: '参与福利',
		fields: 'benefits'
	},
	{
		iconUrl: imgBaseUrl + 'activity/activity-activity-note.png',
		title: '特别说明',
		fields: 'notes'
	},
	{
		iconUrl: imgBaseUrl + 'activity/activity-activity-deadline.png',
		title: '报名截止',
		fields: 'registrationDeadline'
	},
	{
		iconUrl: imgBaseUrl + 'activity/activity-activity-quota.png',
		title: '招募人数',
		fields: 'recruitmentQuota'
	},
	{
		iconUrl: imgBaseUrl + 'activity/activity-activity-organizer.png',
		title: '主办单位',
		fields: 'organizer'
	}
]

export const skipPageName = {
	create: '/pages/activity/form',
	more: '/pages/activity/list',
	apply: '/pages/activity/apply'
}

export const ACTIVE_STATUS_MAP: Record<ActivityStatus, string> = {
	[ActivityStatus.UN_START]: '未开始',
	[ActivityStatus.IN_PROGRESS]: '报名中',
	[ActivityStatus.ENDED]: '已结束',
	[ActivityStatus.CANCELLED]: '已取消'
}
export const STATUS_COLOR_MAP: Record<ActivityStatus, string> = {
	[ActivityStatus.UN_START]: 'rgba(60, 179, 87, 1)',
	[ActivityStatus.IN_PROGRESS]: 'rgba(95, 95, 95, 1)',
	[ActivityStatus.ENDED]: '',
	[ActivityStatus.CANCELLED]: ''
}
