<template>
	<view class="page-container">
		<navbar
			:title="action === OPT_MAP.CREATE ? '创建活动' : '修改活动'"
			show-nav-back
			:on-nav-click="backToPreviousPage"
		>
		</navbar>
		<view
			class="active-container"
			:style="`height: calc(100vh - 34rpx - ${navbarInfo.barHeight}px); padding-top:${navbarInfo.barHeight}px;`"
		>
			<view class="active-base-content">
				<uni-forms ref="baseFormRef" :model-value="activesForm">
					<view class="active-base-container">
						<view class="base-title">基础信息</view>
						<view class="base-active-texteare base-active-texteare-name">
							<uni-forms-item label="活动名称" required name="name">
								<textarea
									v-model="activesForm.name"
									:disabled="action === OPT_MAP.UPDATE"
									:maxlength="20"
									placeholder="请输入活动名称"
								/>
							</uni-forms-item>
						</view>
						<view class="base-acitive-info">
							<uni-forms-item label="活动时间" required name="activityTime">
								<uni-datetime-picker
									v-model="activesForm.activityTime"
									type="datetime"
									return-type="timestamp"
								/>
							</uni-forms-item>
						</view>
						<view class="base-active-texteare">
							<uni-forms-item label="活动地点：" required name="place">
								<textarea
									v-model="activesForm.place"
									:maxlength="30"
									placeholder="请输入活动地点"
								/>
							</uni-forms-item>
						</view>
						<view class="base-acitive-info">
							<uni-forms-item label="报名截止时间" required name="registrationDeadline">
								<uni-datetime-picker
									v-model="activesForm.registrationDeadline"
									type="datetime"
									return-type="timestamp"
								/>
							</uni-forms-item>
						</view>
						<view class="base-acitive-info base-border-bottom">
							<uni-forms-item label="招募人数" required name="recruitmentQuota">
								<input v-model="activesForm.recruitmentQuota" placeholder="请输入招募人数" />
							</uni-forms-item>
						</view>
					</view>
					<view class="active-detail-container">
						<view class="base-title">详细信息</view>
						<view class="base-active-texteare">
							<uni-forms-item label="参与福利：">
								<textarea
									v-model="activesForm.benefits"
									:maxlength="36"
									placeholder="请输入参与福利"
								/>
							</uni-forms-item>
						</view>
						<view class="base-active-texteare">
							<uni-forms-item label="特别说明：">
								<textarea
									v-model="activesForm.notes"
									:maxlength="36"
									placeholder="请输入特别说明"
								/>
							</uni-forms-item>
						</view>
						<view class="base-acitive-info">
							<uni-forms-item label="主办单位">
								<textarea
									v-model="activesForm.organizer"
									:maxlength="12"
									placeholder="请输入主办单位"
								/>
							</uni-forms-item>
						</view>
						<view class="base-active-texteare-bottom base-active-texteare">
							<uni-forms-item label="活动描述：">
								<textarea v-model="activesForm.description" placeholder="请输入活动描述" />
							</uni-forms-item>
						</view>
					</view>
					<view class="active-images-container">
						<view class="base-title">图片信息</view>
						<view class="base-active-texteare-bottom base-active-texteare base-active-imgs">
							<view class="active-imgs-title">活动海报:</view>
							<view class="active-imgs-upload">
								<uni-file-picker
									v-if="cameraAuthorized || imgUrls.length > 0"
									v-model="imgUrls"
									mode="grid"
									file-media-type="image"
									:limit="3"
									@select="handleImgSelect"
									@delete="handleImgDelete"
								/>
								<view v-else class="active-imgs-unauthorized" @click="handleAuthorizeCamera"></view>
							</view>
						</view>
					</view>
					<view v-if="action === OPT_MAP.UPDATE" class="active-submit-update">
						<view class="active-delete" @click="handleSubmitUpdate(UPDATE_OPT.DELETE)"
							>删除活动</view
						>
						<view class="active-update" @click="handleSubmitUpdate(UPDATE_OPT.UPDATE)"
							>更新发布</view
						>
					</view>
					<view v-else class="active-submit-btn-area">
						<view class="active-submit" @click="handleSubmitCreate">确认发布</view>
					</view>
				</uni-forms>
			</view>
		</view>
		<textPopop
			ref="createPopup"
			title="发布确认"
			content=""
			cancel-text="取消"
			sumbit-text="确认"
			:is-customizable="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:submit-function="handleClickCreatePopup"
		>
			<template #content-info>
				<view>
					<view>发布后活动名称不可修改，其他内容可</view>
					<view>修改。您确认发布活动吗？</view>
				</view>
			</template>
		</textPopop>

		<textPopop
			ref="updatePopup"
			title=""
			content=""
			cancel-text="取消"
			sumbit-text="确认"
			:is-customizable="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}activity/activity-delete-poup.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:submit-function="handleClickUpdatePopup"
		>
			<template #content-info>
				<view v-if="updateOption === UPDATE_OPT.DELETE && action === OPT_MAP.UPDATE">
					<view>若您删除了本活动，已报名用户将收</view>
					<view>到活动取消通知。删除活动后无法修</view>
					<view>改。您确认删除本活动吗？</view>
				</view>
				<view v-if="updateOption === UPDATE_OPT.UPDATE && action === OPT_MAP.UPDATE">
					<view>若您修改活动时间或地点，报名用</view>
					<view>户将收到新通知。您最多能修改3次。</view>
					<view>您确认更新本活动信息吗？</view>
				</view>
			</template>
		</textPopop>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import ActivityService from '@/service/activity'
import { ActivityCreateParams, ActivityDetailInfo, ActivityUpdateParams } from '@/types/activity.d'
import type { UniFormsInstance } from '@uni-helper/uni-ui-types'
import textPopop from '@/components/text-popup/text-popop.vue'
import { BASE_URL, config, imgBaseUrl, uploadBasePath, uploadImgPreviewUrl } from '@/config'

const store = useStore()

const baseFormRef = ref<UniFormsInstance>()
const activityId = ref(0)
const navbarInfo = computed(() => store.getters.navbarInfo)

enum OPT_MAP {
	CREATE = 'create',
	UPDATE = 'update'
}

enum UPDATE_OPT {
	DELETE = 'delete',
	UPDATE = 'update'
}
interface TextPopopInstance {
	openPopup: () => void
	closePopup: () => void
}
const action = ref(OPT_MAP.CREATE)
const updateOption = ref(UPDATE_OPT.DELETE)
const createPopup = ref<TextPopopInstance>()
const updatePopup = ref<TextPopopInstance>()
const activesForm = ref<ActivityUpdateParams | ActivityCreateParams>({
	id: 0,
	name: '',
	activityTime: '',
	place: '',
	registrationDeadline: '',
	recruitmentQuota: 0,
	benefits: '',
	organizer: '',
	description: '',
	notes: '',
	attachments: [],
	registrationCount: 0
})
const cameraAuthorized = ref(false)
const imgUrls = ref<{ name: string; url: string; type: string }[]>([])

const backToPreviousPage = () => {
	uni.navigateBack({
		delta: 1
	})
}

const handleSubmitCreate = () => {
	baseFormRef.value?.validate?.()?.then((res: any) => {
		const nameFlag = handleValidateName(res.name)
		const timeFlag = handleValidateTime(res.activityTime, res.registrationDeadline)
		const placeFlag = handleValidatePlace(res.place)
		const numberFlag = handleValidateNumber(res.recruitmentQuota)
		if (nameFlag && timeFlag && placeFlag && numberFlag) {
			createPopup.value?.openPopup()
		}
	})
}
const handleClickCreatePopup = async () => {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const { id, ...rest } = activesForm.value as ActivityUpdateParams
	const params = {
		...rest,
		attachments: imgUrls.value
			.filter((item) => item.name !== '')
			.map((info) => {
				return {
					...info,
					url: info.url.replace(`${uploadImgPreviewUrl}`, '')
				}
			})
	}

	try {
		const { code, data } = await ActivityService.createActivitiy(params)
		const id = data?.id
		if (code === 1) {
			uni.showToast({
				title: '创建成功',
				icon: 'success'
			})
			activesForm.value = {
				id: null,
				name: '',
				activityTime: '',
				place: '',
				registrationDeadline: '',
				recruitmentQuota: null,
				benefits: '',
				organizer: '',
				description: '',
				notes: '',
				attachments: []
			} as unknown as ActivityCreateParams
			createPopup.value?.closePopup()
			uni.redirectTo({
				url: `/pages/activity/apply?id=${id}`
			})
		} else {
			uni.showToast({
				title: '创建失败',
				icon: 'error'
			})
		}
	} catch (e) {
		console.error('activity create error', e)
	}
}

const handleSubmitUpdate = (flag: UPDATE_OPT) => {
	updateOption.value = flag
	updatePopup.value?.openPopup()
}

const handleClickUpdatePopup = async () => {
	if (updateOption.value === UPDATE_OPT.DELETE && activityId.value) {
		try {
			const { code } = await ActivityService.deleteActivitiy(activityId.value)
			if (code === 1) {
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})
				updatePopup.value?.closePopup()
				uni.redirectTo({
					url: '/pages/activity/list'
				})
			} else {
				uni.showToast({
					title: '删除失败',
					icon: 'error'
				})
			}
		} catch (e) {
			console.error('delete activity error', e)
		}
	} else if (updateOption.value === UPDATE_OPT.UPDATE) {
		const params = {
			...(activesForm.value as ActivityUpdateParams),
			attachments: imgUrls.value.map((info) => {
				return {
					...info,
					url: info.url.replace(`${uploadImgPreviewUrl}`, '')
				}
			})
		}
		try {
			const { code } = await ActivityService.updateActivitiy(params)
			if (code === 1) {
				uni.showToast({
					title: '修改成功',
					icon: 'success'
				})
				updatePopup.value?.closePopup()
				uni.redirectTo({
					url: `/pages/activity/apply?id=${activityId.value}`
				})
			} else {
				uni.showToast({
					title: '修改失败',
					icon: 'error'
				})
			}
		} catch (e) {
			console.error('update activity error', e)
		}
	}
}

const handleImgSelect = (evt: { tempFilePaths: string[] }) => {
	const tempFilePaths = evt.tempFilePaths
	tempFilePaths.forEach((filePath: string) => {
		uni.uploadFile({
			url: `${BASE_URL}${uploadBasePath}multi`,
			filePath,
			name: 'files',
			header: {
				appId: config.appId,
				Authorization: `Bearer ${uni.getStorageSync('AuthTokens')}`
			},
			success(res) {
				try {
					const parsedRes = JSON.parse(res.data)
					const urls = (parsedRes.data || []).map((item: { fileName: string; url: string }) => ({
						name: item.fileName || 'Default Name',
						url: `${uploadImgPreviewUrl}` + item.url,
						type: ''
					}))
					imgUrls.value = [...imgUrls.value, ...urls]
				} catch (e) {
					console.error('解析响应出错:', e)
				}
			},
			fail(err) {
				console.error('上传失败:', err)
			}
		})
	})
}

const handleAuthorizeCamera = () => {
	uni.authorize({
		scope: 'scope.camera',
		success() {
			cameraAuthorized.value = true
		},
		fail(err) {
			console.error('相册/摄像头权限被拒绝', err)
			cameraAuthorized.value = false
		}
	})
}

const handleImgDelete = (evt: { tempFile: { name: string } }) => {
	const imgName = evt.tempFile.name || ''
	imgUrls.value = imgUrls.value.filter((item) => item.name !== imgName)
}

const getActivityDetail = async (id: number) => {
	try {
		const { code, data } = await ActivityService.getDetailedInfo(id)
		if (code === 1) {
			const resultData = data || ([] as unknown as ActivityDetailInfo)
			activesForm.value = resultData as unknown as ActivityUpdateParams
			imgUrls.value = resultData.attachments.map((item) => ({
				name: item.name,
				type: 'jpg',
				url: item.url
			}))
		}
	} catch (e) {
		console.error('getActivityDetail error', e)
	}
}

const handleValidateNumber = (number: string) => {
	const regex = /^[1-9]\d*$/
	if (!regex.test(number)) {
		uni.showToast({
			title: '人数大于0',
			icon: 'none',
			duration: 2000
		})
		return false
	} else {
		return true
	}
}

const handleValidateName = (name: string) => {
	const regex = /^[\u4e00-\u9fa5（）]+$/
	if (!regex.test(name)) {
		uni.showToast({
			title: '活动名称不能为空且为中文',
			icon: 'none',
			duration: 2000
		})
		return false
	} else {
		return true
	}
}

const handleValidatePlace = (place: string) => {
	const regex = /^[\u4e00-\u9fa50-9.,!?，。！？、()（）[\]【】{}《》"“”'‘’\-_\s]+$/
	if (!regex.test(place)) {
		uni.showToast({
			title: '活动地点不能为空且不含特殊字符、英文',
			icon: 'none',
			duration: 2000
		})
		return false
	} else {
		return true
	}
}

const handleValidateTime = (startTime: number, endTime: number) => {
	const currentTime = dayjs().valueOf()
	if (startTime < endTime || endTime < currentTime || startTime < currentTime) {
		uni.showToast({
			title: '报名截止时间需在活动开始时间前且在当前时间后',
			icon: 'none',
			duration: 2000
		})
		return false
	} else {
		return true
	}
}

onLoad((option) => {
	activityId.value = Number(option.id) || 0
	if (option.id) {
		action.value = OPT_MAP.UPDATE
		getActivityDetail(activityId.value)
	} else {
		action.value = OPT_MAP.CREATE
	}
})
</script>
<style scoped lang="scss">
.page-container {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #fafafa;
}

.active-container {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;

	.active-base-content {
		flex: 1;
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		overflow-y: auto;
		background-color: #f3f3f3;
		margin-top: 20rpx;

		&::-webkit-scrollbar {
			display: none;
			width: 0;
			height: 0;
			color: transparent;
		}
	}

	.active-base-container {
		width: 682rpx;
		height: 710rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-sizing: border-box;
	}

	.active-detail-container {
		width: 682rpx;
		height: 808rpx;
		background-color: #fff;
		margin-top: 20rpx;
		border-radius: 16rpx;
		box-sizing: border-box;
	}

	.active-images-container {
		width: 682rpx;
		height: 306rpx;
		background-color: #fff;
		margin-top: 36rpx;
		border-radius: 16rpx;
		box-sizing: border-box;
	}
}

.base-title {
	width: 100%;
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 18rpx;
	padding: 18rpx 20rpx 0 20rpx;
}

.base-acitive-info {
	display: flex;
	align-items: center;
	width: 100%;
	height: 110rpx;
	border-bottom: #fafafa 2px solid;
	padding-left: 20rpx;
	padding-top: 20rpx;
	font-size: 14px;
	box-sizing: border-box;
}

.base-border-bottom {
	border: none;
}

.base-active-texteare {
	width: 100%;
	height: 208rpx;
	padding-top: 20rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	border-bottom: #fafafa 2px solid;
	box-sizing: border-box;
}

.base-active-texteare-name {
	height: 104rpx !important;
}

.base-active-texteare-bottom {
	border: none !important;
}

.active-submit {
	width: 682rpx;
	height: 88rpx;
	line-height: 88rpx;
	border-radius: 88rpx;
	text-align: center;
	color: #fff;
	font-weight: 600;
	border: 1px solid rgba(255, 255, 255, 0.6715);
	background: linear-gradient(90deg, rgba(255, 157, 48, 1), rgba(255, 181, 85, 1));
	margin-top: 36rpx;
	margin-bottom: 200px;
}

.base-active-imgs {
	display: flex;

	.active-imgs-upload {
		display: flex;
		justify-content: space-between;
		flex: 1;

		.active-imgs-unauthorized {
			width: 158rpx;
			height: 158rpx;
			background-size: cover;
			background-repeat: no-repeat;
			background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/activity/active-create-upload-imgs.png');
		}

		.active-img-preview {
			display: flex;
			flex: 1;
			position: relative;
		}
	}
}

.active-submit-update {
	display: flex;
	align-items: end;
	width: 682rpx;
	height: 200rpx;
	display: flex;
	justify-content: space-between;
	margin-top: 240rpx;

	.active-delete {
		width: 320rpx;
		height: 88rpx;
		text-align: center;
		line-height: 88rpx;
		border-radius: 88rpx;
		font-size: 18px;
		font-weight: 600;
		color: rgba(98, 171, 11, 1);
		border: rgba(98, 171, 11, 1) 1px solid;
		box-sizing: border-box;
	}

	.active-update {
		width: 320rpx;
		height: 88rpx;
		text-align: center;
		line-height: 88rpx;
		color: #fff;
		font-size: 18px;
		font-weight: 600;
		background: linear-gradient(90deg, rgba(255, 157, 48, 1), rgba(255, 181, 85, 1));
		border-radius: 88rpx;
	}
}

.active-submit-btn-area {
	width: 100%;
	height: 400rpx;
}

.active-imgs-title {
	width: 142rpx;
	font-size: 14px;
}

:deep(.uni-forms) {
	height: 100%;
	margin-top: 20rpx;
}

:deep(.uni-forms-item) {
	width: 100%;
	height: 100%;
	margin-bottom: 0;

	.uni-forms-item__label {
		height: 100%;
		width: 212rpx !important;
		color: rgba(0, 0, 0, 1) !important;

		text {
			height: 100%;
			text-align: start;
		}
	}
}

:deep(.uni-file-picker .file-picker__box-content) {
	margin: 0 !important;
}

:deep(.file-picker__progress) {
	display: none !important;
}

:deep(.uni-file-picker__container) {
	display: flex;
	margin: 0 !important;
}

:deep(.uni-file-picker .file-picker__box) {
	width: 158rpx !important;
	height: 158rpx !important;
	padding: 0 !important;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/activity/active-create-upload-imgs.png');
	background-size: cover;
	background-repeat: no-repeat;
	margin-right: 8rpx;

	.is-add {
		.icon-add {
			background-color: rgba(255, 255, 255, 0);
		}
	}

	border: none !important;
	box-sizing: border-box !important;
}

:deep(.file-picker__box-content) {
	width: 158rpx !important;
	height: 158rpx !important;
	border: none !important;
	box-sizing: border-box !important;
}

:deep(.uni-forms-item__content) {
	display: flex;

	textarea {
		width: 100%;
		height: 100%;
		overflow-y: auto;
	}
}

:deep(.uni-date-x--border) {
	border: none !important;
}

uni-toast .uni-toast {
	font-size: 11rpx;
}
</style>
