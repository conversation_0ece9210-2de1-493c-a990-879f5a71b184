<template>
	<view class="answer-container">
		<view v-if="content !== ''" class="answer-content">{{
			status === MESSAGE_STATUS.BEING ? disPlayAgentResponse : content
		}}</view>
		<slot name="agent-extra"></slot>
	</view>
</template>

<script setup>
import { defineProps, ref, onMounted, watch } from 'vue'
import { MESSAGE_STATUS } from '../constant'

const agentTyping = ref(false)
const disPlayAgentResponse = ref('')

const props = defineProps({
	id: {
		type: Number,
		default: -1
	},
	content: {
		type: String,
		default: ''
	},
	status: {
		required: false,
		type: String,
		default: 'success'
	}
})
const emits = defineEmits(['handleagentTyped', 'handleIsTyping'])
const isTyping = ref(true)
const interval = ref(null)

const starTyping = () => {
	if (props.status === MESSAGE_STATUS.BEING) {
		disPlayAgentResponse.value = ''
		let display = ''
		let currentIndex = 0
		const fullResponse = props.content
		agentTyping.value = true
		const sentences = fullResponse.match(/[^，。？！; 、： ]+[，。？！; 、：]*/g) || []
		interval.value = setInterval(() => {
			if (currentIndex < sentences.length) {
				display += sentences[currentIndex]
				disPlayAgentResponse.value = display
				emits('handleIsTyping', props.id, disPlayAgentResponse.value, isTyping.value)
				currentIndex++
			} else {
				isTyping.value = false
				emits('handleagentTyped', props.id, MESSAGE_STATUS.SUCCESS)
				clearInterval(interval.value)
			}
		}, 500)
	} else {
		clearInterval(interval.value)
	}
}
watch(
	() => props.content,
	() => {
		isTyping.value = true
		starTyping()
	}
)
onMounted(() => {
	starTyping()
})
</script>

<style scoped lang="scss">
.answer-container {
	max-width: 520rpx;
	margin-bottom: 32rpx;

	.answer-content {
		max-width: 520rpx;
		white-space: normal;
		padding: 21rpx;
		background-color: #fff;
		border-radius: 0 12rpx 12rpx 12rpx;
		font-size: 14px;
		color: rgba(41, 35, 29, 1);
		margin-left: 36rpx;
		box-sizing: border-box;
	}
}
</style>
