<template>
	<uni-popup
		ref="popupRef"
		:safe-area="true"
		mask-background-color="rgba(0, 0, 0, 0.8)"
		:mask-click="false"
		:is-mask-click="false"
	>
		<view class="popup-container">
			<view class="popup-content">
				<image
					class="chat-warn-icon"
					:src="`${imgBaseUrl}chat/chat-sensitive-warn.png`"
					mode="scaleToFill"
				/>
				<view class="chat-warn-title">注意：请避免发送敏感词汇</view>
				<view class="chat-warn-content"
					>避免发送任何敏感或不恰当的词汇。我们鼓励建设性和尊重的对话。感谢您的合作！</view
				>
			</view>
			<view class="close-icon" @click="closePopup"> 知道了 </view>
		</view>
	</uni-popup>
</template>
<script>
export default {
	options: { styleIsolation: 'shared' }
}
</script>
<script setup>
import { ref } from 'vue'
import { imgBaseUrl } from '../../../config'

const popupRef = ref(null)

const openPopup = () => {
	popupRef.value.open('center')
}
const closePopup = () => {
	popupRef.value.close()
}

defineExpose({
	openPopup,
	closePopup
})
</script>
<style scoped lang="scss">
:deep(.uni-popup__wrapper) {
	width: fit-content;
	height: fit-content;
}

.popup-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	left: 50%;
	transform: translateX(-50%);
	width: 100%;
	height: 100%;

	.popup-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 480rpx;
		height: 306rpx;
		background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/chat/chat-sensitive-bg.png');
		background-repeat: no-repeat;
		background-size: cover;
		.chat-warn-icon {
			position: absolute;
			top: -90rpx;
			width: 192rpx;
			height: 158rpx;
		}

		.chat-warn-title {
			font-size: 14px;
			width: 358rpx;
			margin-top: 52rpx;
			margin-bottom: 24rpx;
		}

		.chat-warn-content {
			font-size: 12px;
			width: 358rpx;
			color: rgba(0, 0, 0, 0.7);
		}
	}

	.close-icon {
		width: 230rpx;
		height: 74rpx;
		line-height: 74rpx;
		margin-top: 68rpx;
		text-align: center;
		color: #fff;
		font-size: 18px;
		font-weight: 600;
		background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/chat/chat-sensitive-confirm-bg.png');
		background-repeat: no-repeat;
		background-size: cover;
	}
}
</style>
