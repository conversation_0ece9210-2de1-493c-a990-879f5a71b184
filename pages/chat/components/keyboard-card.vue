<template>
	<view class="chat-bottom" :style="`height: ${chatBottomHeight}px`">
		<view
			class="chat-bottom-msg"
			:style="{ bottom: `${keyboardHeight}px`, paddingBottom: `${keyboardHeight ? 8 : 42}px` }"
		>
			<view class="chat-bottom-textarea">
				<textarea
					v-model="chatMsg"
					placeholder-style="color: rgba(179, 179, 179, 1);font-size: 16px; line-height: 1.5"
					placeholder="输入问题"
					confirm-type="send"
					maxlength="300"
					:show-confirm-bar="false"
					:adjust-position="false"
					auto-height
					:disable-default-padding="true"
					@linechange="handleSendHeight"
					@confirm="handleSendQuestion(chatMsg, '')"
				/>
			</view>
			<image
				class="chat-bottom-icon"
				:src="`${imgBaseUrl}chat/chat-send-icon.png`"
				mode="scaleToFill"
				@click="handleSendQuestion(chatMsg, '')"
			/>
		</view>
	</view>
</template>

<script setup>
import { ref, defineEmits, computed, onMounted } from 'vue'
import { imgBaseUrl } from '../../../config'

const emits = defineEmits(['handleSendQuestion'])

const chatMsg = ref('')
const bottomHeight = ref(0)
const keyboardHeight = ref(0)
const chatBottomHeight = computed(() => bottomHeight.value + keyboardHeight.value)

const handleSendQuestion = (msg, type) => {
	chatMsg.value = ''
	emits('handleSendQuestion', msg, type)
}

const handleSendHeight = () => {
	setTimeout(() => {
		const query = uni.createSelectorQuery()
		query.select('.chat-bottom-msg').boundingClientRect()
		query.exec((res) => {
			bottomHeight.value = res[0]?.height
		})
	}, 10)
}

onMounted(() => {
	uni.onKeyboardHeightChange((res) => {
		if (res.height >= 0) {
			keyboardHeight.value = res.height
		}
	})
})
</script>
<style scoped lang="scss">
.chat-bottom {
	width: 100%;
	position: absolute;
	bottom: 0;

	.chat-bottom-msg {
		display: flex;
		justify-content: space-around;
		align-items: center;
		padding: 16rpx 48rpx;
		padding-bottom: 68rpx;
		position: fixed;
		bottom: 0;
		width: 100%;
		background-color: #fff;
		box-sizing: border-box;
	}

	.chat-bottom-textarea {
		textarea {
			width: 288px;
			background-color: #ffffff;
			border: 1px solid rgba(77, 31, 0, 1);
			color: rgba(179, 179, 179, 1);
			font-size: 16px;
			line-height: 22px;
			border-radius: 4px;
			padding: 20rpx 16rpx;
			min-height: 36rpx;
			box-sizing: border-box;
		}
	}

	.chat-bottom-icon {
		width: 48rpx;
		height: 48rpx;
	}
}
</style>
