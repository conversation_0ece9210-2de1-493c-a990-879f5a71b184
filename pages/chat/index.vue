<template>
	<view class="page-container">
		<navbar
			title="骡小西带你玩古道"
			showNavBack
			:onNavClick="backToPreviousPage"
			:opacity="navOpacity"
			:eleColor="navEleColor"
		></navbar>
		<view class="chat-top">
			<view class="chat-body" ref="chatBodyRef" :style="`margin-top:${navbarInfo.barHeight}px;`">
				<view v-if="clearChat" class="chat-clear-text">聊聊新话题吧~</view>
				<view class="chat-agent-user" v-for="(chat, index) in chatList" :key="index">
					<view v-if="chat.userChat.content !== ''" class="chat-user-content">
						<view class="chat-time">{{ chat.userChat.create }}</view>
						<view class="chat-user-body">
							<image
								v-if="chat.userChat.status === MESSAGE_STATUS.FAIL"
								class="chat-message-fail"
								:src="`${imgBaseUrl}chat/message-fail.png`"
								mode="scaleToFill"
							/>
							<view class="chat-user">{{ chat.userChat.content }}</view>
						</view>
					</view>
					<view
						class="chat-agent-content"
						v-if="
							chat.agentAnswer.content !== '' ||
							Object.entries(chat.agentAnswer.knowledgeList).length > 0
						"
					>
						<view v-if="chat.agentAnswer.create !== ''" class="chat-time">{{
							chat.agentAnswer.create
						}}</view>
						<view class="chat-agent-body">
							<answerCard
								:content="chat.agentAnswer.content"
								:status="chat.agentAnswer?.status"
								:id="chat.agentAnswer.id"
								@handleagentTyped="handleagentTyped"
								@handleIsTyping="handleIsTyping"
							>
								<template v-slot:agent-extra>
									<view
										class="chat-recomend chat-common-question"
										v-for="recomend in chat.agentAnswer?.recomendList"
										:key="recomend.question"
										@click="handleSendQuestion(recomend.question, '')"
									>
										{{ recomend.question }}</view
									>

									<view
										v-if="Object.entries(chat.agentAnswer.knowledgeList).length > 0"
										class="chat-question-choose chat-question-common"
									>
										<view class="chat-choose-text"
											>{{ chat.agentAnswer.knowledgeList.question }}
										</view>
										<view class="chat-choose-list">
											<view
												class="chat-question chat-common-question"
												v-for="choose in chat.agentAnswer.knowledgeList.choices"
												:key="choose"
												@click="handleSendQuestion(chat.agentAnswer.knowledgeList, choose)"
											>
												{{ choose }}</view
											>
										</view>
									</view>
									<view
										v-if="chat.agentAnswer?.relatedList?.length > 0"
										class="chat-question-images"
									>
										<image
											class="chat-question-image-item"
											v-for="related in chat.agentAnswer?.relatedList"
											:key="related.thumbnail"
											:src="related.thumbnail"
											mode="scaleToFill"
											@click="handlePreviewImage(related.resource, chat.agentAnswer?.relatedList)"
										/>
									</view>
								</template>
							</answerCard>
						</view>
					</view>
					<view v-else class="chat-question-being chat-question-common">小西正在思考...</view>
				</view>
			</view>
			<view>
				<keyboardCard @handleSendQuestion="handleSendQuestion"> </keyboardCard>
			</view>
		</view>
		<image
			class="chat-clear"
			:src="`${imgBaseUrl}chat/chat-clear-icon.png`"
			mode="scaleToFill"
			@click="handleClearChat"
		/>
		<image class="chat-enter" :src="`${imgBaseUrl}chat/chat-luoxiaoxi.png`" mode="scaleToFill" />
		<SensitivePopup ref="senstivePopupRef"></SensitivePopup>
	</view>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { imgBaseUrl } from '../../config'
import dayjs from 'dayjs'
import { AgentApi } from '@/api/agent'
import { onUnload, onPageScroll } from '@dcloudio/uni-app'
import { waitingChat, MESSAGE_STATUS } from './constant'
import keyboardCard from '../chat/components/keyboard-card.vue'
import answerCard from '../chat/components/answer-card.vue'
import SensitivePopup from './components/sensitive-popup.vue'

const senstivePopupRef = ref(null)
const chatBodyRef = ref(null)
const store = useStore()
const agentApi = new AgentApi()
const userId = computed(() => store.state.userInfo.userId)
const userName = computed(() => store.state.userInfo.userName)
const navbarInfo = computed(() => store.getters.navbarInfo)
const isRein = ref(false)
const actionTimer = ref(null)
const messageStatus = ref(MESSAGE_STATUS.SUCCESS)
const agentResponse = ref({
	content: '',
	create: ''
})
const chatList = ref([])
const currentUserChat = ref({})
const clearChat = ref(false)
const navOpacity = ref(0)
const navEleColor = ref('#000000')
const userInputContext = ref('')
const isTypingAgentId = ref(null)
const isTypingContent = ref('')
const isTyping = ref(false)
const resStream = ref('')
const currentQuestion = ref('')
const debounceTimer = ref(null)
const currentAnswer = ref('')
const isSendMessage = ref(false)

const getRecomendationList = async () => {
	let params = {
		user_id: userId.value,
		user_name: userName.value,
		...(isRein.value ? {} : { query: userInputContext.value })
	}
	try {
		const res = await agentApi.getRecommendQuestion(params)
		if (isRein.value) {
			return res
		} else {
			chatList.value.push({
				userChat: {
					content: ''
				},
				agentAnswer: {
					id: dayjs().valueOf(),
					content: waitingChat,
					create: dayjs().format('YYYY-MM-DD HH:mm'),
					status: MESSAGE_STATUS.BEING,
					recomendList: res.data || [],
					knowledgeList: {}
				}
			})
		}
	} catch (error) {
		console.error('getRecomendationList error', error)
	}
}

const getKnowledgeQuestionList = async () => {
	try {
		const res = await agentApi.getKnowledgeQuestion({
			user_id: userId.value,
			user_name: userName.value,
			query: userInputContext.value
		})
		const knowledgeData = res.data || []
		const knowledge = knowledgeData[dayjs().valueOf() % knowledgeData.length]
		Object.assign(knowledge, {
			...knowledge,
			choices: knowledge.choices.split('；')
		})

		chatList.value.push({
			userChat: {
				content: ''
			},
			agentAnswer: {
				id: dayjs().valueOf(),
				content: '',
				create: dayjs().format('YYYY-MM-DD HH:mm'),
				status: MESSAGE_STATUS.BEING,
				knowledgeList: knowledge || {},
				recomendList: [],
				relatedList: []
			}
		})
	} catch (error) {
		console.error('getKnowledgeQuestionList error', error)
	}
}

const backToPreviousPage = () => {
	uni.navigateBack({
		delta: 1
	})
}
const handleParseStream = (resText) => {
	const formatText = `[${resText.replaceAll(/}\s*{/g, '},{')}]`
	let jsonData = []
	try {
		jsonData = JSON.parse(formatText)
	} catch (error) {
		console.error('handleParseStream error', error)
	}
	return jsonData
}
const handleClearChat = async () => {
	try {
		await agentApi.clearDialogService({ user_id: userId.value })
	} catch (error) {
		console.error('clear chat err', error)
	}
	clearChat.value = true
	chatList.value = []
}

const getRelatedContent = async () => {
	const params = {
		user_id: userId.value,
		user_name: userName.value,
		query: currentQuestion.value,
		answer: currentAnswer.value
	}
	let result = []
	try {
		const res = await agentApi.getRelatedContent(params)
		result = res.data || []
	} catch (error) {
		console.error('getRelatedContent error', error)
	}
	return result
}
const onChunkReceived = async (data) => {
	const res = resStream.value + data
	resStream.value = res
	if (resStream.value.includes('"finish_reason": "Done"')) {
		const jsonData = handleParseStream(resStream.value)
		if (jsonData.length === 0) return
		let newContent = ''
		agentResponse.value.content = ''
		jsonData.forEach((item) => {
			newContent += item.content
			if (item.index === 1) {
				const length = chatList.value.length - 1
				chatList.value[length].userChat.status = MESSAGE_STATUS.SUCCESS
			}
			if (item.finish_reason === 'Done') {
				currentAnswer.value = newContent
				agentResponse.value.content = newContent
				Object.assign(chatList.value[chatList.value.length - 1].agentAnswer, {
					id: dayjs().valueOf(),
					content: newContent,
					create: dayjs().format('YYYY-MM-DD HH:mm'),
					status: MESSAGE_STATUS.BEING,
					recomendList: [],
					knowledgeList: {},
					relatedList: []
				})
			}
		})
		const sensitive = jsonData.find((item) => item.intent === 'review')
		if (sensitive) {
			senstivePopupRef.value.openPopup()
			chatList.value.pop()
			resStream.value = ''
			return
		}
		const related = jsonData.find((item) => item.intent === 'qa')
		if (related) {
			const result = await getRelatedContent()
			if (result.length > 0) {
				chatList.value[chatList.value.length - 1].agentAnswer.relatedList = result
			}
		}
		resStream.value = ''
	} else {
		return
	}
}
const onEntranceReceived = async (data) => {
	const res = resStream.value + data
	resStream.value = res
	if (resStream.value.includes('"finish_reason": "Done"')) {
		const jsonData = handleParseStream(resStream.value)
		if (jsonData.length === 0) return
		let newContent = ''
		agentResponse.value.content = ''
		jsonData.forEach((item) => {
			newContent += item.content
			if (item.finish_reason === 'Done') {
				agentResponse.value.content = newContent
			}
		})
		if (chatList.value.length) {
			chatList.value[0].agentAnswer = {
				...chatList.value[0].agentAnswer,
				content: newContent,
				status: MESSAGE_STATUS.BEING
			}
		}
		if (isRein.value) {
			const res = await getRecomendationList()
			chatList.value[0].agentAnswer.recomendList = res.data || []
			isRein.value = false
		}
		resStream.value = ''
	} else {
		return
	}
}

const handleagentTyped = (id, type) => {
	isTyping.value = false
	if (type === MESSAGE_STATUS.SUCCESS) {
		const agent = chatList.value.find((item) => item.agentAnswer.id === id)
		agent.agentAnswer.status = MESSAGE_STATUS.SUCCESS
	}
}

const handleSendQuestion = (question, choose) => {
	if (debounceTimer.value) {
		clearTimeout(debounceTimer.value)
	}
	debounceTimer.value = setTimeout(async () => {
		if (question === '') {
			uni.showToast({
				title: '请输入问题',
				icon: 'none'
			})
			return
		}
		isSendMessage.value = false
		startTimer()
		currentQuestion.value = question
		const isReapeatingAgent = chatList.value.find(
			(item) => item.agentAnswer.status === MESSAGE_STATUS.BEING
		)
		if (isReapeatingAgent) {
			isReapeatingAgent.agentAnswer.status = MESSAGE_STATUS.SUCCESS
			isReapeatingAgent.agentAnswer.content = '回复取消'
		}
		if (isTyping.value) {
			const agent = chatList.value.find((item) => item.agentAnswer.id === isTypingAgentId.value)
			agent.agentAnswer.content = isTypingContent.value
			agent.agentAnswer.status = MESSAGE_STATUS.SUCCESS
		}

		userInputContext.value += `${question}，`
		let inputValue = question
		if (choose !== '') {
			inputValue =
				`${question.question}，` +
				`${question.description}` +
				'用户选择：' +
				`${choose}` +
				'，请判断用户选择是否正确'
		}

		if (question) {
			currentUserChat.value = {
				content: choose === '' ? question : choose,
				create: dayjs().format('YYYY-MM-DD HH:mm'),
				status: MESSAGE_STATUS.BEING
			}
			chatList.value.push({
				userChat: currentUserChat.value,
				agentAnswer: {
					id: dayjs().valueOf(),
					content: '',
					create: '',
					status: MESSAGE_STATUS.BEING,
					recomendList: [],
					knowledgeList: {},
					relatedList: []
				}
			})

			clearChat.value = false
			messageStatus.value = MESSAGE_STATUS.BEING
			const params = {
				user_name: userName.value,
				user_id: userId.value,
				input: inputValue
			}
			const res = await agentApi.chatStream(params, onChunkReceived)
			const code = res.statusCode
			if (code !== 200) {
				chatList.value[chatList.value.length - 1].agentAnswer.content = '服务器繁忙，请重试！'
			}
		}
	}, 800)
}

const startTimer = () => {
	clearTimeout(actionTimer.value)
	actionTimer.value = setTimeout(async () => {
		if (!isSendMessage.value) {
			await checkAction()
			isSendMessage.value = true
		}
		startTimer()
	}, 180000)
}

const checkAction = async () => {
	const currentTime = dayjs().valueOf()
	currentTime % 2 === 0 ? await getRecomendationList() : await getKnowledgeQuestionList()
	isRein.value = false
}

const getIntroduceData = async () => {
	const params = {
		user_name: userName.value,
		user_id: userId.value,
		instruction: 'entrance'
	}
	const id = dayjs().valueOf()
	chatList.value.push({
		userChat: {
			content: ''
		},
		agentAnswer: {
			id,
			content: '正在加载开场白...',
			create: dayjs().format('YYYY-MM-DD HH:mm'),
			status: MESSAGE_STATUS.BEING,
			recomendList: [],
			knowledgeList: {}
		}
	})
	const res = await agentApi.orderStream(params, onEntranceReceived)
	const code = res.statusCode
	if (code !== 200) {
		uni.showToast({
			title: '引导语获取失败，稍后重试',
			icon: 'none'
		})
	}
}

const handleIsTyping = (id, content, type) => {
	isTyping.value = type
	isTypingAgentId.value = id
	isTypingContent.value = content
}

const handlePreviewImage = (url, list) => {
	const urls = list.map((item) => item.resource)
	uni.previewImage({
		current: url,
		urls,
		indicator: 'default'
	})
}

onMounted(async () => {
	isRein.value = true
	await getIntroduceData()
	startTimer()
})

onPageScroll((e) => {
	if (e.scrollTop <= 44) {
		navOpacity.value = e.scrollTop / 44
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: 'transprant'
		})
	} else {
		navOpacity.value = 1
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: 'transprant'
		})
	}
})
onUnload(() => {
	isRein.value = false
	clearInterval(actionTimer.value)
	actionTimer.value = null
})
</script>
<style scoped lang="scss">
.page-container {
	position: relative;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.chat-top {
	display: flex;
	flex-direction: column;
	width: 100vw;
	height: 100vh;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/chat/chat-bg.png');
	background-repeat: no-repeat;
	background-attachment: fixed;
	background-size: cover;
}

.chat-body {
	min-height: 100vh;
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	overflow-y: auto;
	padding-bottom: 200rpx;
	box-sizing: border-box;
}

.chat-container {
	display: flex;
	flex: 1;
	width: 100vw;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

.chat-clear {
	width: 80rpx;
	height: 80rpx;
	position: fixed;
	top: 210rpx;
	left: 634rpx;
}

.chat-enter {
	position: fixed;
	width: 110rpx;
	height: 120rpx;
	left: 606rpx;
	bottom: 175rpx;
}

.chat-user-content {
	display: flex;
	width: 100vw;
	flex-direction: column;
	align-items: center;
	margin-bottom: 32rpx;

	.chat-user-body {
		width: 100%;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		margin-right: 50rpx;

		.chat-user {
			max-width: 520rpx;
			white-space: normal;
			padding: 21rpx;
			background-color: rgba(0, 170, 255, 1);
			border-radius: 12rpx 0 12rpx 12rpx;
			font-size: 14px;
			color: rgba(255, 255, 255, 1);
			box-sizing: border-box;
		}
	}
}

.chat-agent-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 32rpx;
	width: 100vw;

	.chat-agent-body {
		width: 100%;
		display: flex;
		justify-content: flex-start;
	}
}

.chat-clear-text {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	position: absolute;
	top: 200rpx;
	width: 680rpx;
	height: 42rpx;
	color: rgba(199, 199, 199, 1);
	font-size: 12rpx;
	border-top: 2rpx solid rgba(199, 199, 199, 1);
	box-sizing: border-box;
}

.chat-time {
	height: 24rpx;
	color: rgba(93, 93, 93, 1);
	border-radius: 6rpx;
	background-color: rgba(255, 255, 255, 0.6);
	padding: 0 14rpx 0 14rpx;
	font-size: 10px;
	margin-bottom: 20rpx;
	box-sizing: border-box;
}

.chat-message-fail {
	width: 32rpx;
	height: 32rpx;
	margin-right: 24rpx;
	background-repeat: no-repeat;
	background-size: contain;
}

.chat-common-question {
	max-width: 520rpx;
	display: inline-block;
	word-wrap: break-word;
	word-break: break-word;
	white-space: normal;
	padding: 21rpx;
	background-color: rgba(255, 213, 165, 1);
	border-radius: 86rpx;
	font-size: 12px;
	color: rgba(41, 35, 29, 1);
	margin-top: 20rpx;
	box-sizing: border-box;
}

.chat-recomend {
	margin-left: 36rpx;
	padding: 21rpx;
}

.chat-question {
	margin-right: 10rpx;
}

.chat-question-common {
	white-space: normal;
	padding: 21rpx;
	background-color: #fff;
	border-radius: 0 12rpx 12rpx 12rpx;
	font-size: 14px;
	color: rgba(41, 35, 29, 1);
	margin-left: 36rpx;
	box-sizing: border-box;
}

.chat-question-choose {
	max-width: 520rpx;
}

.chat-question-being {
	max-width: 300rpx;
	margin-bottom: 20rpx;
}

.chat-choose-list {
	display: flex;
	flex-wrap: wrap;
}

.chat-question-images {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
	margin-left: 36rpx;
	margin-top: 20rpx;

	.chat-question-image-item {
		flex-shrink: 0;
		width: 184rpx;
		height: 184rpx;
		margin-right: 28rpx;
		margin-bottom: 10rpx;
		box-sizing: border-box;
	}
}
</style>
