<template>
	<view :class="`page-container bg-type-${eventType}`">
		<navbar
			title="云守护"
			show-nav-back
			:on-nav-click="backToPreviousPage"
			ele-color="white"
			:nav-back-icon="`${imgBaseUrl}nav/nav-bar-back-white-svg.svg`"
		></navbar>
		<view class="content">
			<view v-show="isPlayingGif" :class="`gif bg-type-${eventType}`">
				<view class="gif-container">
					<image class="background-gif" :src="gifUrl" />
				</view>
			</view>
			<view class="default">
				<image :src="backUrl" class="default-image"></image>
			</view>
			<view class="content-top">
				<view class="content-top-three">
					<view class="content-top-three-member1">
						<image
							class="content-top-three-member1-image"
							:src="`${imgBaseUrl}guard/guard-my.png`"
						></image>
						<image class="content-top-three-member1-picture" :src="titleUrl"></image>
						<view class="content-top-three-member1-text" @click="handleGoScoreHistory">
							<text class="content-top-three-member1-text-name">
								{{ name.length > 6 ? name?.slice(0, 6) + '...' : name }}
							</text>
							<view class="content-top-three-member1-text-content">
								<text class="content-top-three-member1-text-content-guard">守护值</text>
								<text class="content-top-three-member1-text-content-number">{{ points }}</text>
							</view>
						</view>
					</view>
					<image
						class="content-top-three-member"
						:src="`${imgBaseUrl}guard/guard-member.png`"
						@click="handleClickDialog"
					></image>
					<image
						class="content-top-three-record"
						:src="`${imgBaseUrl}guard/guard-record.png`"
						@click="handleHistoryDialog"
					></image>
				</view>
				<view class="content-top-four">
					<image
						class="content-top-four-sign"
						:src="`${imgBaseUrl}guard/guard-sign.png`"
						@click="handleClickSign"
					></image>
					<image
						class="content-top-four-dynamic"
						:src="`${imgBaseUrl}guard/guard-dynamic.png`"
						@click="handleClickDynamic"
					></image>
					<image
						class="content-top-four-monitor"
						:src="`${imgBaseUrl}guard/guard-monitor.png`"
						@click="handleClickMonitor"
					></image>
					<!-- <image
						@click="handleClickReward"
						class="content-top-four-reward"
						:src="`${imgBaseUrl}guard-reward.png`"
					></image> -->
					<image
						v-if="isVolunteer ? false : true"
						class="content-top-four-volunteer"
						:src="`${imgBaseUrl}guard/guard-volunteer.png`"
						@click="handleClickVolunteer"
					></image>
				</view>
				<view v-show="showEventImage" class="content-top-event">
					<image class="content-top-event-image" :src="typeUrl" @click="handleClickEvent" />
				</view>
			</view>
			<GuardCard :dialog="dialog" :img-url="imgUrl" @update:dialog="updateDialog"></GuardCard>
			<HistoryDialog
				:show-history-dialog="showHistoryDialog"
				:historyu-guard-list="historyuGuardList"
				:today-guard-list="todayGuardList"
				@update:show-history-dialog="historyDialog"
			></HistoryDialog>
			<MemberDialog
				:my-id="myId"
				:show-member-dialog="showMemberDialog"
				:guard-member-list="guardMemberList"
				@update:show-member-dialog="memberDialog"
			></MemberDialog>
			<view class="bottom"></view>
		</view>

		<textPopop
			ref="inquirePopup"
			title="是否报名成功 "
			cancel-text="稍后再试"
			sumbit-text="已报名"
			:is-customizable="true"
			:is-close-icon="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:cancel-funtion="handleFailApply"
			:submit-function="handleSuccessApply"
		/>
	</view>
</template>

<script setup>
import { ref, nextTick, computed } from 'vue'
import { GuardApi } from '../../api/guard'
import { imgBaseUrl } from '../../config.js'
import { onLoad, onShow, onHide } from '@dcloudio/uni-app'
import GuardCard from './component/GuardCard.vue'
import {
	GUARD_TYPE_MAP,
	BACKGROUND_TYPE_MAP,
	EVENT_IMGURL_MAP,
	EVENT_GIF_MAP,
	EVENT_SUCCESS_MAP,
	EVENT_EVENT_MAP,
	BACKGROUND_DEFAULT_MAP
} from './config.js'
import { HistoryDialog } from './historyDialog.vue'
import { MemberDialog } from './memberDialog.vue'
import { useStore } from 'vuex'
import { UserApi } from '../../api/user.js'
import textPopop from '../../components/text-popup/text-popop.vue'
const userApi = new UserApi()
const inquirePopup = ref(null)
const isVolunteer = ref('true')
const store = useStore()
const guardApi = new GuardApi()
const showHistoryDialog = ref(false)
const showMemberDialog = ref(false)
const guardMemberList = ref([])
const eventId = ref(-1)
const eventType = ref(0)
const dialog = ref(false)
const imgUrl = ref('')
const isPlayingGif = ref(false)
const gifUrl = ref('')
const typeUrl = ref('')
const backUrl = ref('')
const showEventImage = ref(true)
const guardHistoryList = ref([
	{
		guardType: '',
		userId: '',
		guardName: '',
		points: '',
		actionTime: ''
	}
])
const historyuGuardList = ref([])
const todayGuardList = ref([])
const currentDate = new Date().toISOString().split('T')[0]
const name = computed(() => store.state.userInfo.userName)
const points = computed(() => store.state.userInfo.points)
const titleUrl = computed(() => store.state.userInfo.avatarPic)
const myId = computed(() => store.state.userInfo.userId)
const hoofprintId = computed(() => store.state.hoofPrintId)
const currentState = ref({})
const ifSign = ref(false)
function backToPreviousPage() {
	uni.switchTab({
		url: '/pages/index/index'
	})
}
async function handleHistoryDialog() {
	guardHistoryList.value = []
	todayGuardList.value = []
	historyuGuardList.value = []
	showHistoryDialog.value = true
	const historyRes = await guardApi.getGuardRecordList()
	guardHistoryList.value = historyRes.data.map((item) => ({
		...item,
		guardName: GUARD_TYPE_MAP[item.guardType]
	}))
	guardHistoryList.value.forEach((item) => {
		if (item.actionTime.split(' ')[0] === currentDate) {
			todayGuardList.value.push(item)
		} else {
			historyuGuardList.value.push(item)
		}
	})
}
async function handleClickDialog() {
	showMemberDialog.value = true
	const res = await guardApi.getGuardMemberList(hoofprintId.value)
	guardMemberList.value = res.data.list
}
async function getUserProfile() {
	const { data } = await userApi.getUserProfile()
	store.dispatch('setUserInfo', data)
}
async function handleClickSign() {
	try {
		const resData = await guardApi.getGuardSignin(hoofprintId.value)
		if (resData.code == 1) {
			imgUrl.value = `${imgBaseUrl}guard/guard-sign-card.png`
			dialog.value = true
			getUserProfile()
			ifSign.value = true
			const signdDate = {
				date: new Date().toISOString().split('T')[0],
				ifSign: true
			}
			uni.setStorageSync('signInfo', signdDate)
			store.dispatch('setSignInfo', signdDate)
		}
	} catch (error) {
		console.error('error', error)
	}
}
async function getGuardEvent() {
	//TODO：需要复查 请求的数据没有存储
	const eventRes = await guardApi.getGuardHoofprint()
	currentState.value = eventRes.data
	eventId.value = eventRes.data.eventId
	eventType.value = eventRes.data.eventType
	backUrl.value = BACKGROUND_TYPE_MAP[eventType.value]
	if (eventType.value === 1) {
		isPlayingGif.value = false
		showEventImage.value = false
	} else {
		typeUrl.value = EVENT_IMGURL_MAP[eventType.value]
	}
}

function handleClickDynamic() {
	uni.removeStorageSync('isVolunteerPage')
	uni.navigateTo({
		url: '/pages/guard/records?from=guard'
	})
}
function handleClickMonitor() {
	uni.navigateTo({
		url: '/pages/guard/monitor'
	})
}

function handleClickVolunteer() {
	uni.navigateToMiniProgram({
		appId: 'wx8493c68ed5fd2cf6',
		path: 'pages-index/index/opp-detail?oppId=WJit7KjbF8abN',
		success() {
			const isVolunteerRegisted = uni.getStorageSync('isVolunteerPage')
			if (isVolunteerRegisted === '' || isVolunteerRegisted === undefined) {
				inquirePopup.value.openPopup()
			}
		}
	})
}

// TODO:根据gif时间来调整时间
function handleClickEvent() {
	showEventImage.value = false
	nextTick(async () => {
		gifUrl.value = EVENT_GIF_MAP[eventType.value]
		isPlayingGif.value = true
		backUrl.value = BACKGROUND_DEFAULT_MAP[eventType.value]
		// currentState
		try {
			const params = {
				eventId: eventId.value,
				guardType: EVENT_EVENT_MAP[currentState.value.eventType],
				hoofprintId: hoofprintId.value
			}
			const resData = await guardApi.getGuardMaintain(params)
			typeUrl.value = EVENT_IMGURL_MAP[currentState.value.eventType]

			if (resData.code == 1) {
				getUserProfile()
			}
		} catch (error) {
			console.error('error:', error)
		}

		setTimeout(() => {
			isPlayingGif.value = false
			dialog.value = true
			imgUrl.value = EVENT_SUCCESS_MAP[eventType.value]
		}, 3500)
	})
}
function updateDialog(value) {
	dialog.value = value
	getUserProfile()
}
function historyDialog(value) {
	showHistoryDialog.value = value
}
function memberDialog(value) {
	showMemberDialog.value = value
}

// Callback function：No volunteer
const handleFailApply = () => {
	uni.setStorageSync('isVolunteer', false)
	inquirePopup.value?.closePopup()
}

// Callback function：Success volunteer
const handleSuccessApply = async () => {
	const resData = await guardApi.getCheckVolunteer()
	if (resData.code == 1) {
		uni.setStorageSync('isVolunteer', true)
		isVolunteer.value = true
		inquirePopup.value?.closePopup()
	}
}

const handleGoScoreHistory = () => {
	uni.navigateTo({
		url: '/pages/score/index'
	})
}

onLoad(() => {
	getGuardEvent()
	getUserProfile()
	// 判断有没有这个属性
	if (uni.getStorageSync('isVolunteer') === '' || uni.getStorageSync('isVolunteer') === undefined) {
		isVolunteer.value = true
	} else {
		isVolunteer.value = uni.getStorageSync('isVolunteer')
	}
})

onShow(() => {
	getUserProfile()
	if (uni.getStorageSync('isVolunteer') === '' || uni.getStorageSync('isVolunteer') === undefined) {
		isVolunteer.value = true
	} else {
		isVolunteer.value = uni.getStorageSync('isVolunteer')
	}
	if (
		!(
			uni.getStorageSync('isVolunteerPage') === '' ||
			uni.getStorageSync('isVolunteerPage') === undefined
		)
	) {
		inquirePopup.value.openPopup()
	}
})

onHide(() => {
	uni.removeStorageSync('isVolunteerPage')
})
</script>

<!-- TODO: CSS收敛，定义过多 -->
<style scoped lang="scss">
.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
	z-index: 0;
}
.bg-type-1,
.bg-type-3 {
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-background-normal.png');
}
.bg-type-2 {
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-background-rain.png');
}
//TODO:后期背景图片需要根据设计来重新设置
.bg-type-4 {
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-background-normal.png');
}
.bg-type-5 {
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-background-normal.png');
}
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: fixed;
	width: 100%;
	height: 100%;
	z-index: 0;
	.default {
		width: 100%;
		height: 100%;
		z-index: 1;
		position: absolute;
	}
	.default-image {
		width: 100%;
		height: 100%;
		position: absolute;
	}
}

.guard-title {
	margin-top: 50px;
}

.text-area {
	display: flex;
	justify-content: center;
}

.bottom {
	width: 100%;
	height: 54px;
	position: absolute;
	flex-shrink: 0;
	bottom: 0;
	background: linear-gradient(
		180deg,
		rgba(255, 248, 232, 0) 0%,
		rgba(255, 248, 232, 0.74) 15.4%,
		#fff8e8 77.78%
	);
}

.content-top {
	position: fixed;
	background-size: cover;
	width: 100%;
	height: 100%;
	z-index: 3;
}
.content-top-three {
	display: flex;
	flex-direction: column;
	gap: 48rpx;
	margin-left: 36rpx;
	margin-top: 200rpx;
	position: absolute;
	&-member {
		height: 116rpx;
		width: 106rpx;
	}
	&-member1 {
		height: 116rpx;
		width: 96rpx;
		&-image {
			width: 210rpx;
			height: 100rpx;
			position: absolute;
		}
		&-picture {
			width: 64rpx;
			height: 64rpx;
			position: relative;
			top: 35rpx;
			border-radius: 50%;
		}
		&-text {
			position: relative;
			display: flex;
			flex-direction: column;
			left: 70rpx;
			top: -35rpx;
			&-name {
				font-size: 10px;
				line-height: 14px;
				color: rgba(21, 21, 21, 1);
			}
			&-content {
				display: flex;
				flex-direction: row;
				white-space: nowrap;
				&-guard {
					font-size: 10px;
					line-height: 14px;
					margin-right: 5px;
					color: rgba(21, 21, 21, 0.6);
				}
				&-number {
					font-weight: 600;
					font-size: 10px;
					line-height: 14px;
					color: rgba(123, 188, 18, 1);
				}
			}
		}
	}
	&-record {
		height: 102rpx;
		width: 96rpx;
	}
}
.content-top-four {
	display: flex;
	flex-direction: column;
	right: 36rpx;
	gap: 48rpx;
	position: absolute;
	margin-top: 200rpx;
	&-sign {
		height: 102rpx;
		width: 96rpx;
	}
	&-dynamic {
		height: 102rpx;
		width: 96rpx;
	}
	&-monitor {
		height: 102rpx;
		width: 96rpx;
	}
	&-reward {
		height: 102rpx;
		width: 96rpx;
	}
	&-volunteer {
		height: 124rpx;
		width: 100rpx;
	}
}
.content-top-event {
	&-image {
		width: 260rpx;
		height: 312rpx;
		margin-left: 108rpx;
		margin-top: 678rpx;
		z-index: 1;
	}
}
.background-gif {
	background-size: cover;
	background-position: center;
	width: 100%;
	height: 100%;
	z-index: 2;
	position: absolute;
}
.gif {
	width: 100%;
	height: 100%;
}
.gif-container {
	width: 100%;
	height: 100%;
}
</style>
