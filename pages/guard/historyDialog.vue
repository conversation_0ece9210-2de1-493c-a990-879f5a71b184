<template>
	<view class="content">
		<view v-if="showHistoryDialog" class="mask">
			<view class="history-dialog">
				<image
					:src="`${imgBaseUrl}guard/guard-guard-mask-close.png`"
					class="dialog-close"
					@click="closeDialog"
				></image>
				<scroll-view scroll-y class="history-dialog-scrollview">
					<view class="history-dialog-top">
						<view class="history-dialog-image">
							<image
								:src="`${imgBaseUrl}guard/guard-history-today.png`"
								class="history-dialog-image-today"
							>
							</image>
							<view class="history-dialog-image-line"></view>
						</view>
						<view v-if="todayGuardList.length !== 0" class="history-dialog-history">
							<view
								v-for="index in todayGuardList"
								:key="index.userId"
								class="history-dialog-history-right"
							>
								<view>
									<view class="history-dialog-history-right-actiontime">{{
										index.actionTime
									}}</view>
									<view class="history-dialog-history-right-bottom">
										<view class="history-dialog-history-right-bottom-guardtype">
											<text class="history-dialog-history-right-bottom-guardtype-user">
												{{
													index.userName?.length > 6
														? index.userName?.slice(0, 6) + '...'
														: index.userName
												}}
											</text>
											<text class="history-dialog-history-right-bottom-guardtype-text">{{
												index.guardName
											}}</text>
										</view>
										<text class="history-dialog-history-right-bottom-text"
											>守护值+{{ index.points }}</text
										>
									</view>
								</view>
							</view>
						</view>
						<view v-else class="history-dialog-history">
							<text class="history-dialog-history-text">今日暂无守护动态</text>
						</view>
					</view>
					<view class="history-dialog-bottom">
						<view class="history-dialog-image">
							<image
								:src="`${imgBaseUrl}guard/guard-history-history.png`"
								class="history-dialog-image-history"
							></image>
							<view class="history-dialog-image-line"></view>
						</view>
						<view v-if="historyuGuardList.length !== 0" class="history-dialog-history">
							<view
								v-for="index in historyuGuardList"
								:key="index.userId"
								class="history-dialog-history-right"
							>
								<view>
									<view class="history-dialog-history-right-actiontime">{{
										index.actionTime
									}}</view>
									<view class="history-dialog-history-right-bottom">
										<view class="history-dialog-history-right-bottom-guardtype">
											<text class="history-dialog-history-right-bottom-guardtype-user">
												{{
													index.userName?.length > 6
														? index.userName?.slice(0, 6) + '...'
														: index.userName
												}}
											</text>
											<text class="history-dialog-history-right-bottom-guardtype-text">{{
												index.guardName
											}}</text>
										</view>
										<text class="history-dialog-history-right-bottom-text"
											>守护值+{{ index.points }}</text
										>
									</view>
								</view>
							</view>
						</view>
						<view v-else class="history-dialog-history">
							<text class="history-dialog-history-text">暂无守护动态</text>
						</view>
					</view>
					<view class="history-dialog-history-message">
						<text class="history-dialog-history-message-text">没有更多消息了</text>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>
<script setup lang="ts">
import { defineProps, defineEmits, withDefaults } from 'vue'
import { imgBaseUrl } from '@/config.js'

interface GuardHistoryItem {
	actionTime: string
	points: number
	guardType: number
	userId: string
	userName: string
	guardName: string
}

const props = withDefaults(
	defineProps<{
		showHistoryDialog: boolean
		todayGuardList: GuardHistoryItem[]
		historyuGuardList: GuardHistoryItem[]
	}>(),
	{
		showHistoryDialog: false,
		todayGuardList: () => [],
		historyuGuardList: () => []
	}
)

const emit = defineEmits(['update:showHistoryDialog'])

const closeDialog = () => {
	emit('update:showHistoryDialog', false)
}
</script>
<style scoped>
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.mask {
	position: fixed;
	z-index: 1000;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	transition: opacity 0.3s;
}
.history-dialog {
	box-sizing: border-box;
	width: 100%;
	height: 50vh;
	background-size: 100%;
	background-repeat: no-repeat;
	padding: 10px 0;
	position: fixed;
	bottom: 0;
	z-index: 9999999;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-guard-records.png');
}
.history-dialog-scrollview {
	height: 85%;
	margin-top: 46px;
}
.history-dialog-image {
	display: flex;
	flex-direction: column;
}
.history-dialog-image-today {
	margin-bottom: 12px;
	width: 48rpx;
	max-height: 48rpx;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
.history-dialog-image-history {
	margin-bottom: 12px;
	width: 48rpx;
	max-height: 48rpx;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
.history-dialog-image-line {
	width: 10px;
	height: 100%;
	border-right: solid 2px #ffdc9e;
}
.history-dialog-bottom {
	display: flex;
	margin: 16px 24px 8px 24px;
}
.history-dialog-history-message {
	display: flex;
	justify-content: center;
}
.history-dialog-history-right {
	margin-bottom: 16px;
}
.history-dialog-history-right-actiontime {
	color: rgba(21, 21, 21, 0.7);
	align-self: stretch;
	font-size: 14px;
}
.history-dialog-history-right-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
	align-self: stretch;
}
.history-dialog-history-right-bottom-guardtype {
	display: flex;
	align-items: center;
	gap: 4px;
}
.history-dialog-history-right-bottom-guardtype-user {
	color: #793807;
	font-size: 14px;
	text-decoration-line: underline;
}
.history-dialog-top {
	display: flex;
	margin: 0px 24px 0 24px;
}
.history-dialog-history {
	flex: 1;
	margin: 28px 0px 25px 16px;
}
.history-dialog-history-text {
	color: rgba(21, 21, 21, 0.7);
	font-size: 14px;
	text-decoration-line: underline;
}
.history-dialog-history-right-bottom-guardtype-text {
	color: #151515;
	font-size: 14px;
}
.history-dialog-history-right-bottom-text {
	color: #4d1f00;
	font-size: 14px;
}
.history-dialog-history-message-text {
	color: rgba(21, 21, 21, 0.3);
	font-size: 12px;
}
.dialog-close {
	position: absolute;
	right: 56.44rpx;
	top: 42rpx;
	width: 15.68px;
	height: 15.68px;
}
</style>
