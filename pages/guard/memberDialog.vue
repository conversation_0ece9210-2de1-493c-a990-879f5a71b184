<template>
	<view class="content">
		<view v-if="showMemberDialog" class="mask">
			<view class="dialog">
				<image
					:src="`${imgBaseUrl}guard/guard-guard-mask-close.png`"
					class="dialog-close"
					@click="closeDialog"
				></image>
				<scroll-view scroll-y class="dialog-scrollview">
					<view v-if="mySelf" class="dialog-member">
						<view class="dialog-member-left">
							<view class="dialog-member-index">
								<text>{{ mySelfIndex + 1 }}</text>
							</view>
							<image :src="mySelf.avatarPic" class="dialog-member-img"></image>
							<view class="dialog-member-info">
								<text class="dialog-member-info-name">
									{{ formatUserName(mySelf.userName) }}(我自己)
								</text>
								<view class="dialog-member-info-level">
									<view class="dialog-member-info-level-left">
										<text class="dialog-member-info-level-left-text">用户等级</text>
									</view>
									<text class="dialog-member-info-level-right">已守护{{ mySelf.guardDays }}天</text>
								</view>
							</view>
						</view>
						<text class="dialog-member-right-text">守护值{{ mySelf.points }}</text>
					</view>
					<view
						v-for="(item, idx) in guardMemberList"
						:key="item.userId"
						class="dialog-member-list"
					>
						<view class="dialog-member-left">
							<view class="dialog-member-index">
								<text>{{ idx + 1 }}</text>
							</view>
							<image :src="item.avatarPic" class="dialog-member-img"></image>
							<view class="dialog-member-info">
								<text class="dialog-member-info-name">
									{{ formatUserName(item.userName) }}
								</text>
								<view class="dialog-member-info-level">
									<view class="dialog-member-info-level-left">
										<text class="dialog-member-info-level-left-text">用户等级</text>
									</view>
									<text class="dialog-member-info-level-right">已守护{{ item.guardDays }}天</text>
								</view>
							</view>
						</view>
						<text class="dialog-member-right-text">守护值{{ item.points }}</text>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>
<script setup lang="ts">
import { GuardMemberResponseData } from '@/types/guard.d'
import { imgBaseUrl } from '@/config'
import { defineProps, defineEmits, computed, withDefaults } from 'vue'

const props = withDefaults(
	defineProps<{
		showMemberDialog: boolean
		guardMemberList: GuardMemberResponseData[]
		myId: number
	}>(),
	{
		showMemberDialog: false,
		guardMemberList: () => [],
		myId: 0
	}
)
const emit = defineEmits(['update:showMemberDialog'])

const closeDialog = () => {
	emit('update:showMemberDialog', false)
}

const formatUserName = (name: string) => {
	return name?.length > 6 ? `${name.slice(0, 6)}...` : name
}

const myIdStr = computed(() => String(props.myId))
const mySelfIndex = computed(() =>
	props.guardMemberList.findIndex((item) => item.userId === myIdStr.value)
)
const mySelf = computed(() => props.guardMemberList[mySelfIndex.value] || null)
</script>
<style scoped>
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.mask {
	position: fixed;
	z-index: 1000;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	transition: opacity 0.3s;
}
.dialog-scrollview {
	height: 100%;
	margin-top: 30px;
}
.dialog-close {
	position: absolute;
	right: 56.44rpx;
	top: 42rpx;
	width: 15.68px;
	height: 15.68px;
}
.dialog {
	box-sizing: border-box;
	width: 100%;
	height: 50vh;
	background-size: 100%;
	background-repeat: no-repeat;
	padding: 10px 0;
	position: fixed;
	bottom: 0;
	z-index: 9999999;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-guard-member.png');
}
.dialog-scrollview {
	height: 100%;
	margin-top: 30px;
}

.dialog-member {
	display: flex;
	height: 60px;
	padding: 3px 8px;
	justify-content: space-between;
	align-items: center;
	gap: 10px;
	flex-shrink: 0;
	margin: 16px 24px 16px 24px;
	border-radius: 8px;
	background: rgba(255, 255, 255, 0.7);
	box-shadow: 0px 1px 3px 0px rgba(255, 157, 48, 0.16);
}

.dialog-member-list {
	display: flex;
	height: 60px;
	padding: 3px 8px;
	justify-content: space-between;
	align-items: center;
	gap: 10px;
	flex-shrink: 0;
	margin-left: 24px;
	margin-right: 24px;
	border-radius: 8px;
	box-shadow: 0px 1px 3px 0px rgba(255, 157, 48, 0.16);
}
.dialog-member-list:last-child {
	padding-bottom: 120rpx;
}

.dialog-member-left {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 16px;
}

.dialog-member-right-text {
	color: #4d1f00;
	text-align: center;
	font-size: 14px;
}

.dialog-member-index {
	display: flex;
	width: 16px;
	height: 16px;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	color: #793807;
	text-align: center;
	font-size: 14px;
}
.dialog-member-index image {
	width: 16px;
	height: 16px;
}

.dialog-member-img {
	width: 32px;
	height: 32px;
	border-radius: 32px;
	background: lightgray 50% / cover no-repeat;
}

.dialog-member-info {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: flex-start;
	gap: 4px;
}

.dialog-member-info-name {
	color: #151515;
	text-align: center;
	font-size: 14px;
}

.dialog-member-info-level {
	display: flex;
	align-items: center;
	gap: 4px;
}

.dialog-member-info-level-left {
	display: flex;
	width: 53px;
	height: 14px;
	justify-content: center;
	align-items: center;
	gap: 10px;
	border-radius: 48px;
	background: rgba(255, 157, 48, 1);
}

.dialog-member-info-level-left-text {
	color: #fff;
	font-size: 10px;
}

.dialog-member-info-level-right {
	color: #793807;
	font-size: 10px;
}
</style>
