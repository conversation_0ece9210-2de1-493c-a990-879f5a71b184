<template>
	<view class="comment-bottom" :style="`height: ${chatBottomHeight}px`">
		<view
			class="comment-bottom-msg"
			:style="{ bottom: `${keyboardHeight}px`, paddingBottom: `${keyboardHeight ? 8 : 42}px` }"
		>
			<view
				:class="[
					'common-bottom-area',
					isPopup ? 'comment-bottom-textarea-active' : 'comment-bottom-textarea',
					isIOS ? 'ios' : ''
				]"
			>
				<textarea
					v-model="comment"
					placeholder="留下你的友善评论吧"
					placeholder-style="line-height: 60rpx"
					confirm-type="send"
					maxlength="30"
					:show-confirm-bar="false"
					:adjust-position="false"
					:disable-default-padding="true"
					@linechange="handleSendHeight"
					@focus="handlePopup"
					@blur="closePopup"
				/>
			</view>
			<view
				v-if="isPopup"
				class="comment-popup-send"
				:class="{ disabled: comment.trim().length === 0 }"
				:disabled="comment.trim().length === 0"
				@click="sendComment"
			>
				发送
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

const emits = defineEmits(['send-comment'])

const bottomHeight = ref(0)
const keyboardHeight = ref(0)
const isPopup = ref(false)
const comment = ref('')
const isIOS = ref(false)

const chatBottomHeight = computed(() => bottomHeight.value + keyboardHeight.value)

const handlePopup = () => {
	isPopup.value = true
}
const closePopup = () => {
	isPopup.value = false
}
const sendComment = () => {
	emits('send-comment', comment.value)
	comment.value = ''
	isPopup.value = false
}

const handleSendHeight = () => {
	setTimeout(() => {
		const query = uni.createSelectorQuery()
		query.select('.comment-bottom-msg').boundingClientRect()
		query.exec((res) => {
			console.log('res', res)
			bottomHeight.value = res[0]?.height || 0
		})
	}, 200)
}

onMounted(() => {
	uni.onKeyboardHeightChange((res) => {
		if (res.height >= 0) {
			keyboardHeight.value = res.height
		}
	})
	const systemInfo = uni.getSystemInfoSync()
	isIOS.value = systemInfo.platform === 'ios'
})
</script>
<style scoped lang="scss">
.comment-bottom {
	width: 100%;
	position: absolute;
	bottom: 0;
	.comment-bottom-msg {
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		align-items: center;
		width: 100%;
		position: fixed;
		bottom: 0;
		padding: 8px 16px;
		padding-bottom: 68rpx;
		border-top: 0.5px solid #e5e5e5;
		background-color: #ffffff;
		box-sizing: border-box;
	}

	.comment-bottom-textarea {
		width: 686rpx;
		background-color: #e5e5e5;
		height: 60rpx;
		line-height: 60rpx;
		border-radius: 4px;
		box-sizing: border-box;
		textarea {
			padding: 0rpx 32rpx;
			height: 100%;
			width: 100%;
			color: rgba(0, 0, 0, 0.3);
			font-size: 14px;
			font-weight: 400;
			box-sizing: border-box;
		}
	}
	.comment-bottom-textarea-active {
		width: 686rpx;
		height: 120rpx;
		background-color: #e5e5e5;
		border-radius: 4px;
		box-sizing: border-box;
		textarea {
			padding: 16rpx 32rpx;
			width: 100%;
			height: 100%;
			color: rgba(0, 0, 0, 0.9);
			font-size: 14px;
			font-weight: 500;
			box-sizing: border-box;
		}
	}
	.comment-popup-send {
		align-self: flex-end;
		display: flex;
		justify-content: center;
		height: 48rpx;
		line-height: 48rpx;
		width: 96rpx;
		margin-top: 20rpx;
		margin-right: 0rpx;
		font-size: 14px;
		color: #fff;
		background-color: #ff9d30;
		border-radius: 31px;
		transition: opacity 0.3s;
	}

	.comment-popup-send.disabled {
		opacity: 0.5;
	}
}

.common-bottom-area {
	display: flex;
	flex-direction: column;
	justify-content: center;
}
.ios {
	padding-top: 16rpx;
}
</style>
