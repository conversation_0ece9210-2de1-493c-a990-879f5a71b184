<template>
	<view :class="['comment-popup', isExpand ? 'comment-expand' : '']">
		<view
			class="comment-content"
			:style="{ height: isExpand ? `calc(100vh - ${navbarInfo.barHeight}px)` : '1064rpx' }"
		>
			<view class="comment-top">
				<text class="comment-top-text">{{ pageInfo.total }}条评论</text>
				<view class="comment-top-right">
					<image
						:src="
							isExpand
								? `${imgBaseUrl}guard/guard-record-contract.png`
								: `${imgBaseUrl}guard/guard-record-expand.png`
						"
						class="comment-top-icon"
						mode="aspectFit"
						@click="handleExpand"
					/>
					<image
						:src="`${imgBaseUrl}guard/guard-record-close.png`"
						class="comment-top-icon"
						mode="aspectFit"
						@click="handleClose"
					/>
				</view>
			</view>
			<scroll-view
				scroll-y
				class="scroll-view"
				:style="{ height: isExpand ? '1272rpx' : '800rpx' }"
				@scrolltolower="onReachBottom"
			>
				<view class="comment-body">
					<CommentCard
						v-for="comment in commentList"
						:key="comment.id"
						:comment-info="comment"
						@like-comment="handleLikeComment"
					/>
					<uni-load-more :status="dataStatus" :content-text="contentText" />
				</view>
			</scroll-view>
			<KeyboardCard @send-comment="handleSendComment"></KeyboardCard>
			<!-- <test /> -->
		</view>
	</view>
</template>
<script setup lang="ts">
import CommentCard from './CommentCard.vue'
import KeyboardCard from './KeyboardCard.vue'
import { imgBaseUrl } from '@/config'
import { ref, onMounted, reactive, computed } from 'vue'
import CommentService from '@/service/comment'
import type { ICommentItem } from '@/types/comment.d'
import type { GuardRecordItem } from '@/types/guard.d'
import { DataStatus } from '@/types/base.d'
import { useStore } from 'vuex'

const store = useStore()
const navbarInfo = computed(() => store.getters.navbarInfo)
const props = withDefaults(
	defineProps<{
		recordInfo: GuardRecordItem
	}>(),
	{
		recordInfo: () => ({}) as GuardRecordItem
	}
)

const dataStatus = ref(DataStatus.MORE)
const contentText = ref({
	contentdown: '上拉显示更多',
	contentrefresh: '正在加载',
	contentnomore: '暂无更多评论'
})
const commentList = ref<ICommentItem[]>([])
const emits = defineEmits(['close-comment'])
const isExpand = ref(false)
const pageInfo = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0
})

const handleExpand = () => {
	isExpand.value = !isExpand.value
}

const handleClose = () => {
	isExpand.value = false
	emits('close-comment')
}

const getComments = async () => {
	dataStatus.value = DataStatus.LOADING
	const params = {
		articleId: props.recordInfo.id,
		pageNum: pageInfo.pageNum,
		pageSize: pageInfo.pageSize
	}
	try {
		const { data } = await CommentService.getComentList(params)
		const newComments = data?.list || ([] as ICommentItem[])
		if (newComments && newComments.length > 0) {
			commentList.value = [...commentList.value, ...newComments]
			pageInfo.total = data?.total || 0
			dataStatus.value = data?.isLastPage ? DataStatus.NOMORE : DataStatus.MORE
		} else {
			dataStatus.value = DataStatus.NOMORE
		}
	} catch (error) {
		dataStatus.value = DataStatus.NOMORE
		console.warn('get commentList error', error)
	}
}

const handleSendComment = async (comment: string) => {
	const params = {
		articleId: props.recordInfo.id,
		content: comment
	}

	try {
		const { code } = await CommentService.addComments(params)
		if (code === 1) {
			commentList.value = []
			await getComments()
		}
	} catch (err) {
		console.warn('send-coment error', err)
	}
}

const handleLikeComment = async (commentInfo: ICommentItem) => {
	console.log('commentInfo', commentInfo)
	try {
		const comment = commentList.value.find((item) => item.id === commentInfo.id)
		if (comment) {
			console.log('comment', comment.id)
			if (comment.liked === 1) {
				await CommentService.unlikesComment({ id: comment.id })
			} else {
				await CommentService.likesComment({ id: comment.id })
			}
			comment.liked = comment.liked === 1 ? 0 : 1
			comment.likes = comment.liked === 1 ? comment.likes + 1 : comment.likes - 1
		}
	} catch (error) {
		console.warn('like comment error', error)
	}
}

const onReachBottom = async () => {
	console.log('pageInfo.pageNum11', pageInfo.pageNum)
	if (dataStatus.value !== DataStatus.NOMORE) {
		pageInfo.pageNum += 1
		console.log('pageInfo.pageNum', pageInfo.pageNum)
		await getComments()
	} else {
		return
	}
}

onMounted(async () => {
	await getComments()
})
</script>
<style scoped lang="scss">
.comment-popup {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.6);
	overflow-y: hidden;
	box-sizing: border-box;
}

.comment-expand {
	z-index: 1000;
}

.scroll-view {
	height: 800rpx;
}
.comment-content {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #fff;
	border-radius: 8px 8px 0 0;

	.comment-top {
		height: 104rpx;
		display: flex;
		padding: 32rpx;
		justify-content: space-between;
		box-sizing: border-box;

		.comment-top-text {
			font-size: 14px;
			color: rgba(0, 0, 0, 0.7);
		}

		.comment-top-right {
			display: flex;
			width: 104rpx;
			gap: 32rpx;

			.comment-top-icon {
				width: 36rpx;
				height: 32rpx;
			}
		}
	}

	.comment-body {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 32rpx;

		.no-more-comment {
			font-size: 12px;
			color: rgba(0, 0, 0, 0.7);
		}
	}
}
</style>
