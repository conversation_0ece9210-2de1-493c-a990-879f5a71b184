<template>
	<view class="comment-card">
		<view class="comment-top">
			<image class="user-avatar" :src="commentInfo.avatarPic" mode="aspectFit" />
			<view class="comment-detail">
				<view class="user-name-area">
					<text class="user-name">{{ commentInfo.nickName }}</text>
					<text v-if="isMy" class="user-my-comment">我的</text>
				</view>
				<text class="user-comment">
					{{ commentInfo.content }}
				</text>
			</view>
		</view>
		<view class="comment-bottom">
			<text>{{ commentTime }}</text>
			<view class="like-area">
				<image
					class="like-icon"
					:src="
						commentInfo.liked === 1
							? `${imgBaseUrl}guard/guard-record-heart-color.png`
							: `${imgBaseUrl}guard/guard-records-heart.png`
					"
					@click="handleLiked"
				></image>
				<text>{{ commentInfo.likes }}</text>
			</view>
		</view>
	</view>
</template>
<script setup lang="ts">
import { imgBaseUrl } from '@/config'
import { computed } from 'vue'
import { useStore } from 'vuex'
import type { ICommentItem } from '@/types/comment.ts'
import dayjs from 'dayjs'

const props = withDefaults(
	defineProps<{
		commentInfo: ICommentItem
	}>(),
	{
		commentInfo: () => ({}) as ICommentItem
	}
)

const emits = defineEmits(['like-comment'])
const store = useStore()
const isMy = computed(() => store.state.userInfo.userName === props.commentInfo.nickName)
const commentTime = computed(() => {
	return props.commentInfo.postTime
		? dayjs(props.commentInfo.postTime).format('YYYY年MM月DD日')
		: ''
})

const handleLiked = () => {
	emits('like-comment', props.commentInfo)
}
</script>
<style scoped lang="scss">
.comment-card {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	width: 100%;
	height: 164rpx;
}

.comment-top {
	display: flex;
	padding: 0 32rpx 0 32rpx;
	gap: 16rpx;
	box-sizing: border-box;

	.user-avatar {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
	}

	.comment-detail {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 8rpx;

		.user-name-area {
			display: flex;
			justify-content: start;
			align-items: center;
			gap: 8rpx;

			.user-name {
				font-size: 12px;
				color: rgba(0, 0, 0, 0.5);
			}

			.user-my-comment {
				width: 56rpx;
				height: 32rpx;
				background-color: rgba(21, 21, 21, 0.3);
				border-radius: 4px;
				line-height: 32rpx;
				text-align: center;
				font-size: 10px;
				color: #ffffff;
			}
		}

		.user-comment {
			font-size: 14px;
			color: #000000;
		}
	}
}

.comment-bottom {
	display: flex;
	height: 34rpx;
	padding-left: 112rpx;
	padding-right: 32rpx;
	justify-content: space-between;
	align-items: center;
	font-size: 10px;
	color: rgba(0, 0, 0, 0.7);
	box-sizing: border-box;

	.like-area {
		display: flex;
		height: 34rpx;
		justify-content: space-between;
		align-items: center;

		.like-icon {
			width: 32rpx;
			height: 32rpx;
		}
	}
}
</style>
