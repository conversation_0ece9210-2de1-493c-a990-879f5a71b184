<template>
	<view class="content">
		<view v-if="dialog" class="mask">
			<view class="sign-dialog">
				<image class="image" :src="imgUrl"></image>
				<image
					class="close"
					:src="`${imgBaseUrl}guard/guard-sign-cancel.png`"
					@click="closeDialog"
				/>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, withDefaults } from 'vue'
import { imgBaseUrl } from '@/config.js'

const props = withDefaults(
	defineProps<{
		dialog: boolean
		imgUrl: string
	}>(),
	{
		dialog: false,
		imgUrl: ''
	}
)
const emit = defineEmits<{ (e: 'update:dialog', value: boolean): void }>()

const closeDialog = () => {
	emit('update:dialog', false)
}
</script>

<style scoped>
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.mask {
	display: flex;
	justify-content: center;
	align-items: center;
	position: fixed;
	z-index: 1000;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	transition: opacity 0.3s;
}
.sign-dialog {
	display: flex;
	flex-direction: column;
	gap: 10px;
	position: relative;
	z-index: 9999999;
}
.sign-dialog .image {
	width: 434rpx;
	height: 408rpx;
}
.sign-dialog .close {
	position: relative;
	width: 56rpx;
	height: 56rpx;
	left: 50%;
	transform: translateX(-50%);
}
</style>
