<template>
	<view class="section">
		<view v-show="recordLine" class="top-line"></view>
		<view :class="`section-content-${marginClass}`">
			<view class="top">
				<view class="body">
					<image v-if="record.avatarPic" :src="record.avatarPic" class="image"></image>
					<image v-else :src="`${imgBaseUrl}guard/guard-record-default.png`" class="image"></image>
					<view class="right">
						<view class="name">{{ record.userName }}</view>
						<view class="time">{{ record.postTime }}发布了守护</view>
					</view>
				</view>
				<view class="top-body-right">
					<view
						v-if="pinned && record.userName === name"
						class="top-cancel-top"
						:class="{
							'cancel-top': record.isTop === 0,
							'in-top': record.isTop === 1,
							'pinned-top-click': !pinned,
							'hidden-top': handleShowTop()
						}"
						@click="handleClickIsToTop(record.id, record.isTop)"
					></view>
					<view
						v-if="pinned"
						class="featured-cancel-featured"
						:class="{
							'cancel-featured': record.isFeatured === 0,
							'in-featured': record.isFeatured === 1,
							'hidden-featured': handleShowFeatured()
						}"
						@click="handleClickIsToFeatured(record.id, record.isFeatured)"
					></view>
					<view
						v-if="
							(!pinned && record.isTop === 1) ||
							(pinned && record.userName !== name && record.isTop === 1)
						"
						class="not-pinned-top"
					></view>
				</view>
			</view>
			<view class="middle">
				<text class="title">{{ record.title }}</text>
				<text class="text">{{ record.content }}</text>
			</view>
			<view class="bottom" :style="`padding:0px ${leftWidth}px`">
				<view
					:class="{
						'content-one': record.urls.length === 1,
						'content-multiple': record.urls.length > 1
					}"
					style="display: flex; justify-content: flex-start; gap: 5px"
				>
					<view v-for="(item, index) in record.urls" :key="index">
						<image
							:src="item"
							class="image guard-records-image"
							:style="imageStyle"
							mode="aspectFill"
							@click="handlePreview(item)"
						></image>
					</view>
				</view>
			</view>
			<view class="bottom-right">
				<view v-if="showDelete" class="slot-box" @click="handleClickDelete(record.id)">
					<image :src="`${imgBaseUrl}guard/guard-records-delete.png`" class="image"></image>
					<text class="text">删除</text>
				</view>
				<view v-if="showComment" class="slot-box" @click="handleShowComment">
					<image :src="`${imgBaseUrl}guard/guard-records-comment.png`" class="image" />
					<text class="text">{{ record.comments }}</text>
				</view>
				<view class="slot-box">
					<image
						:src="
							record.liked === 1
								? `${imgBaseUrl}guard/guard-record-heart-color.png`
								: `${imgBaseUrl}guard/guard-records-heart.png`
						"
						class="image"
						@click="handleClickLikes(record)"
					></image>
					<text class="text">{{ record.likes }}</text>
				</view>
			</view>
		</view>

		<textPopop
			ref="deletePopup"
			title="确认删除动态"
			content="动态删除后不可恢复"
			cancel-text="取消"
			sumbit-text="删除"
			:is-customizable="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:submit-function="handleClickDeletePopup"
		></textPopop>

		<textPopop
			ref="toTopPopup"
			title="动态置顶"
			content=""
			cancel-text="暂不"
			sumbit-text="确认置顶"
			:is-customizable="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:position-margin-data="{
				popupContent: toTopPopupContentPosition,
				popupFooter: toTopPopupFooterPosition
			}"
			:submit-function="handleClickToTopPopup"
			:slot-style-data="{
				popupContent: slotFontStyleData
			}"
		>
			<template #content-info>
				<view>
					<view>您确认要将本条动态置顶吗？</view>
					<view>若当前有其他置顶状态，将会被替换</view>
				</view>
			</template>
		</textPopop>

		<textPopop
			ref="cancleTopPopup"
			title="取消置顶"
			content="您确认取消本条动态置顶吗？"
			cancel-text="暂不"
			sumbit-text="确认取消"
			:is-customizable="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:submit-function="handleClickCancleToTopPopup"
			:slot-style-data="{
				popupContent: slotFontStyleData
			}"
		>
		</textPopop>

		<textPopop
			ref="toFeaturedPopup"
			title="动态加精"
			content=""
			cancel-text="暂不"
			sumbit-text="确认加精"
			:is-customizable="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:position-margin-data="{
				popupContent: toFeaturedPopupContentPosition,
				popupFooter: toFeaturedPopupFooterPosition
			}"
			:submit-function="handleClickToFeaturedPopup"
			:slot-style-data="{
				popupContent: slotFontStyleData
			}"
		>
			<template #content-info>
				<view>
					<view>您确认本动态加精吗？</view>
					<view>精选动态将在首页展示</view>
				</view>
			</template>
		</textPopop>

		<textPopop
			ref="cancleFeaturedPopup"
			title="取消加精"
			content="您确认要取消本动态加精吗？"
			cancel-text="暂不"
			sumbit-text="确认取消"
			:is-customizable="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:submit-function="handleClickCancleToFeaturedPopup"
		></textPopop>
	</view>
</template>

<script setup lang="ts">
import { ref, defineProps, computed } from 'vue'
import { useStore } from 'vuex'
import { imgBaseUrl } from '@/config.js'
import GuardService from '@/service/guard'
import textPopop from '@/components/text-popup/text-popop.vue'
import {
	slotFontStyleData,
	toTopPopupContentPosition,
	toTopPopupFooterPosition,
	toFeaturedPopupContentPosition,
	toFeaturedPopupFooterPosition
} from './config'
import type { GuardIssueRecordItem } from '@/types/guard.d'
import { handleGuestMode } from '@/utils/common'

const deletePopup = ref<InstanceType<typeof textPopop> | null>(null)
const toTopPopup = ref<InstanceType<typeof textPopop> | null>(null)
const cancleTopPopup = ref<InstanceType<typeof textPopop> | null>(null)
const toFeaturedPopup = ref<InstanceType<typeof textPopop> | null>(null)
const cancleFeaturedPopup = ref<InstanceType<typeof textPopop> | null>(null)
const deleteId = ref<number | null>(null)
const toTopId = ref<number | null>(null)
const toFeaturedId = ref<number | null>(null)

const store = useStore()
const userLogin = computed(() => store.getters.userLogin)
const pinned = computed(() => store.state.userInfo.pinned)
const props = withDefaults(
	defineProps<{
		record: GuardIssueRecordItem
		leftWidth: number
		marginClass: string
		name: string
		goToLogin?: () => void
		recordLine: boolean
	}>(),
	{
		record: () => ({}) as GuardIssueRecordItem,
		leftWidth: 16,
		marginClass: '',
		name: '',
		goToLogin: () => ({}),
		recordLine: false
	}
)
const imageStyle = computed(() => {
	const screenWidth = uni.getSystemInfoSync().windowWidth
	// TODO: 临时方案，样式需要借助flex和gap自适应
	const marginWidth = props.leftWidth === 16 ? 18 : 8
	if (props.record.urls.length === 1) {
		const calculatedWidth = screenWidth - marginWidth * 2 - (props.leftWidth || 0) * 2
		return `width: ${calculatedWidth}px; height:${calculatedWidth}px;`
	} else {
		const calculatedWidth = screenWidth - marginWidth * 2 - props.leftWidth * 2 - 10
		return `width: ${calculatedWidth / 3}px; height: ${calculatedWidth / 3}px;`
	}
})

const showComment = computed(() => {
	const pages = getCurrentPages()
	const currentPage = pages[pages.length - 1]
	return currentPage.route === 'pages/guard/records'
})

const showDelete = computed(() => {
	return props.name === props.record.userName
})

const handleShowTop = () => {
	if (props.record.isTop === 1) {
		return false // 展示置顶
	}
	const show = props.name === props.record.userName && pinned.value && props.record.isTop === 0
	return !show
}

const handleShowFeatured = () => {
	if (props.record.isFeatured === 1) {
		return false // 展示加精
	}
	const show = pinned.value && props.record.isFeatured === 0
	return !show
}
const emit = defineEmits(['recordDeleted', 'initTrends', 'initRecords', 'show-comment'])
const handleClickDelete = async (id: number) => {
	deletePopup.value?.openPopup()
	deleteId.value = id
}

const handleClickIsToTop = async (id: number, isTop: number) => {
	if (!pinned.value) {
		return
	}
	if (isTop) {
		cancleTopPopup.value?.openPopup()
	} else {
		toTopPopup.value?.openPopup()
	}
	toTopId.value = id
}

const handleClickIsToFeatured = (id: number, isFeatured: number) => {
	if (!pinned.value) {
		return
	}
	if (isFeatured) {
		cancleFeaturedPopup.value?.openPopup()
	} else {
		toFeaturedPopup.value?.openPopup()
	}
	toFeaturedId.value = id
}
const handleClickDeletePopup = async () => {
	if (!deleteId.value) return
	await GuardService.deleteGuardRecord({ id: deleteId.value })
	emit('recordDeleted', deleteId.value)
}

const handleClickLikes = handleGuestMode(async (record: GuardIssueRecordItem) => {
	if (userLogin.value === false) {
		props.goToLogin()
	} else {
		const id = record.id
		if (record.liked === 0) {
			const response = await GuardService.guardLikes({ id })
			record.likes = response.data || 1
		} else {
			const response = await GuardService.guardUnLikes({ id })
			record.likes = response.data || 0
		}
		record.liked = record.liked === 1 ? 0 : 1
	}
})

const handlePreview = (imgurl: string) => {
	uni.previewImage({
		showmenu: true,
		current: imgurl,
		urls: [imgurl],
		longPressActions: {
			itemList: ['保存图片'],
			success() {
				handleSaveImage(imgurl)
			},
			fail: function (err) {
				console.error(err.errMsg)
			}
		}
	})
}

const handleSaveImage = (imgurl: string) => {
	uni.downloadFile({
		url: imgurl,
		success(res) {
			let url = res.tempFilePath
			uni.saveImageToPhotosAlbum({
				filePath: url,
				success() {
					uni.showToast({
						title: '保存成功',
						icon: 'none'
					})
				},
				fail(err) {
					console.error(err.errMsg)
					uni.showToast({
						title: '保存失败',
						icon: 'none'
					})
				}
			})
		}
	})
}

const handleClickToTopPopup = async () => {
	if (!toTopId.value) return
	const { code } = await GuardService.setGuardArticleToTop({ id: toTopId.value })
	if (code === 1) {
		emit('initRecords')
		emit('initTrends')
		toTopPopup.value?.closePopup()
	}
}

const handleClickCancleToTopPopup = async () => {
	if (!toTopId.value) return
	const { code } = await GuardService.cancleGuardArticleToTop({ id: toTopId.value })
	if (code === 1) {
		emit('initRecords')
		emit('initTrends')
		cancleTopPopup.value?.closePopup()
	}
}

const handleClickToFeaturedPopup = async () => {
	if (!toFeaturedId.value) return
	const { code } = await GuardService.setGuardArticleFeatured({ id: toFeaturedId.value })
	if (code === 1) {
		props.record.isFeatured = 1
		toFeaturedPopup.value?.closePopup()
	}
}

const handleClickCancleToFeaturedPopup = async () => {
	if (!toFeaturedId.value) return
	const { code } = await GuardService.cancelGuardArticleFeatured({ id: toFeaturedId.value })
	if (code === 1) {
		props.record.isFeatured = 0
		cancleFeaturedPopup.value?.closePopup()
	}
}

const handleShowComment = () => {
	emit('show-comment', props.record)
}
</script>

<style lang="scss" scoped>
.section {
	&-content-normal {
		flex-direction: column;
		display: flex;
		padding: 16px 0px;
		background: white;
		margin: 0px 0px;
		gap: 8px;
	}

	.top {
		margin: 0px 16px;
		display: flex;
		justify-content: space-between;
		position: relative;

		.top-body-right {
			display: flex;
			gap: 16rpx;
		}

		.body {
			display: flex;
			align-items: center;
			gap: 8px;
			align-self: stretch;

			.image {
				width: 32px;
				height: 32px;
				border-radius: 32px;
			}

			.right {
				display: flex;
				flex-direction: column;
				align-items: flex-start;

				.name {
					font-family: PingFang SC;
					font-weight: 400;
					line-height: 16.8px;
					text-align: left;
					color: rgba(0, 0, 0, 1);
					font-size: 12px;
				}

				.time {
					font-weight: 400;
					color: rgba(21, 21, 21, 0.7);
					font-size: 10px;
					line-height: 14px;
				}
			}
		}

		.top-cancel-top,
		.featured-cancel-featured {
			width: 94rpx;
			height: 40rpx;
			background-size: cover;
			background-repeat: no-repeat;
		}

		.cancel-top {
			background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-record-not-top.png');
		}

		.in-top {
			background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-record-top.png');
		}
		.cancel-featured {
			background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-selected-deactive.png');
		}
		.in-featured {
			background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/guard/guard-selected-active.png');
		}

		.pinned-top-click {
			cursor: none;
		}

		.hidden-top {
			display: none;
		}

		.hidden-featured {
			display: none;
		}

		.not-pinned-top {
			width: 57.14rpx;
			height: 59.42rpx;
			background-image: url('http://125.208.24.134:39090/virtual-village-mini-static/guard-record-not-pinned-top.png');
			background-size: cover;
			background-repeat: no-repeat;
		}
	}

	.middle {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		padding: 0px 16px;
		font-family: PingFang SC;
		font-size: 14px;

		.title {
			color: rgba(0, 0, 0, 1);
			font-weight: 400;
			line-height: 19.6px;
			text-align: left;
		}

		.text {
			color: rgba(21, 21, 21, 0.7);
			word-break: break-all;
			font-weight: 400;
			line-height: 19.6px;
			letter-spacing: 0.10000000149011612px;
			text-align: left;
		}
	}

	.bottom {
		display: flex;
		padding: 0px 16px;

		.content-multiple {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: space-between;
		}

		.content-one {
			.image {
				width: 280rpx !important;
				height: 280rpx !important;
			}
		}

		.guard-records-image {
			border-radius: 10px;
		}
	}

	.bottom-right {
		display: flex;
		padding: 0px 16px;
		justify-content: flex-end;
		gap: 16px;
		position: relative;

		.slot-box {
			display: flex;
			justify-content: center;
			align-items: center;

			.image {
				width: 16px;
				height: 16px;
			}

			.text {
				color: rgba(21, 21, 21, 0.5);
				font-size: 12px;
				letter-spacing: 0.1px;
			}
		}
	}

	&-content-height {
		flex-direction: column;
		display: flex;
		border-radius: 8px;
		padding: 16px 0px;
		background: white;
		margin: 8px 0px;
		gap: 8px;
	}
}

.top-line {
	position: absolute;
	margin-left: 16px;
	left: 16px;
	right: 16px;
	height: 0.5px;
	background-color: rgba(21, 21, 21, 0.08);
	margin-right: 16px;
}
</style>
