<template>
	<view class="page-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar title="守护动态" show-nav-back :on-nav-click="backToPreviousPage"></navbar>
		<view class="filter-top">
			<view
				v-if="selectedTypeFeature === 'featured'"
				class="filter-top-item"
				@click="handleClickTopFeature('featured')"
			>
				<image
					class="filter-top-image-tree"
					:src="`${imgBaseUrl}guard/guard-record-tree-icon.png`"
				></image>
				<text class="filter-top-text">精选动态</text>
			</view>
			<button
				v-if="selectedTypeFeature !== 'featured'"
				class="filter-top-button"
				@click="handleClickTopFeature('featured')"
			>
				<text class="text">精选</text>
			</button>
			<view
				v-if="selectedTypeLatest === 'latest'"
				class="filter-top-item"
				@click="handleClickTopLatest('latest')"
			>
				<image
					class="filter-top-image-sun"
					:src="`${imgBaseUrl}guard/guard-record-sun-icon.png`"
				></image>
				<text class="filter-top-text">最新动态</text>
			</view>
			<button
				v-if="selectedTypeLatest !== 'latest'"
				class="filter-top-button"
				@click="handleClickTopLatest('latest')"
			>
				<text class="text">最新</text>
			</button>
			<button
				class="filter-top-button"
				:class="{ active: selectedType === 'mine' }"
				@click="handleClickTopType('mine')"
			>
				<text class="text">只看我的</text>
			</button>
			<button class="filter-top-button" @click="handleClickDialog">
				<image :src="`${imgBaseUrl}guard/guard-records-filter.png`" class="image"></image>
				<text class="text">筛选</text>
			</button>
		</view>
		<view
			v-if="showFilterDialog"
			class="filter-mask"
			:style="`margin-top: ${navbarInfo.barHeight}px;`"
		>
			<view class="filter-dialog">
				<view class="filter-dialog-content">
					<view class="filter-dialog-content-title">
						<button class="button">
							<image :src="`${imgBaseUrl}guard/guard-records-filter.png`" class="image"></image>
							<text class="text">筛选</text>
						</button>
					</view>
					<view class="filter-dialog-content-body">
						<view class="top">
							<text class="text selete-title">类型选择</text>
							<view class="buttons">
								<button
									class="button"
									:class="{ active: selectedType === 'allType' }"
									@click="handleClickType('allType')"
								>
									<text class="text">不限查看</text>
								</button>
								<button
									class="button"
									:class="{ active: selectedType === 'mine' }"
									@click="handleClickType('mine')"
								>
									<text class="text">只看我的</text>
								</button>
							</view>
						</view>
						<view class="top">
							<text class="text selete-title">时间选择</text>
							<view class="buttons">
								<button
									class="button"
									:class="{ active: selectedTime === 'allTime' }"
									@click="handleClickTime('allTime')"
								>
									<text class="text">不限时间</text>
								</button>
								<button
									class="button"
									:class="{ active: selectedTime === 'week' }"
									@click="handleClickTime('week')"
								>
									<text class="text">一周内</text>
								</button>
								<button
									class="button"
									:class="{ active: selectedTime === 'month' }"
									@click="handleClickTime('month')"
								>
									<text class="text">一月内</text>
								</button>
								<button
									class="button"
									:class="{ active: selectedTime === 'year' }"
									@click="handleClickTime('year')"
								>
									<text class="text">一年内</text>
								</button>
							</view>
						</view>
					</view>
					<button
						class="filter-dialog-content-button"
						:style="{ backgroundImage: `url(${imgBaseUrl}guard/guard-confirm-btn-bc.png)` }"
						@click="handleClickConfirm"
					>
						确认
					</button>
				</view>
			</view>
		</view>
		<scroll-view
			scroll-y
			class="scroll-view"
			@scrolltolower="onReachBottom"
			@scrolltoupper="onPullRefresh"
		>
			<view class="content">
				<view v-for="record in recordsList" :key="record.id" class="section">
					<RecordCard
						:record="record"
						:left-width="leftWidth"
						:margin-class="marginClass"
						:name="name"
						:record-line="false"
						@record-deleted="refreshRecords"
						@init-records="handleInitRecords"
						@show-comment="handleShowComment"
					></RecordCard>
				</view>
			</view>
			<uni-load-more :status="dataStatus" :content-text="contentText" />
		</scroll-view>
		<view class="plus-records" @click="handleClickPlus">
			<image
				:src="`${imgBaseUrl}guard/guard-records-circle.png`"
				class="plus-records-circle"
			></image>
			<image :src="`${imgBaseUrl}guard/guard-records-plus.png`" class="plus-records-plus"></image>
		</view>
		<!-- state-popup：发布问询是否报名-->
		<textPopop
			ref="postEnrollPopup"
			title="发布审核中"
			cancel-text="稍后"
			sumbit-text="开始报名"
			:is-customizable="true"
			:is-close-icon="true"
			:is-mask-click="false"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '696rpx',
				height: '418rpx'
			}"
			:position-data="{
				popupTop: '340rpx',
				popupContent: '140rpx',
				popupFooter: '35rpx'
			}"
			:submit-function="handleClickCard"
		>
			<template #content-top>
				<view class="content-top">通过审核后刷新可见该动态</view>
			</template>

			<template #content-info>
				<view class="content-info">未通过审核的动态将不予展示</view>
				<view class="content-info">您可报名蹄窝守护活动领取志愿北京服务时长</view>
			</template>

			<template #content-bottom>
				<view class="content-bottom">每天只有前三条动态可获得守护值奖励</view>
			</template>
		</textPopop>

		<!-- state-popup：已报名仅发布 -->
		<view v-if="showVoluteerAlready" class="card-mask">
			<view class="card">
				<image :src="`${imgBaseUrl}guard/guard-publish-dynamics-already.png`" class="img"></image>
				<image
					:src="`${imgBaseUrl}guard/guard-monitor-close.png`"
					class="close"
					@click="handleCancelPopup"
				></image>
			</view>
		</view>
		<!-- state-popup：报名询问结果-->
		<textPopop
			ref="inquirePopup"
			title="是否报名成功 "
			cancel-text="稍后再试"
			sumbit-text="已报名"
			:is-customizable="true"
			:is-close-icon="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:cancel-funtion="handleFailApply"
			:submit-function="handleSuccessApply"
		></textPopop>
		<CommentPopup
			v-if="isShowComment"
			:record-info="currentRecord"
			@close-comment="handleCloseComment"
		/>
	</view>
</template>

<script setup lang="ts">
import { onLoad, onReady, onShow, onHide, onUnload } from '@dcloudio/uni-app'
import { ref, computed, onMounted, reactive } from 'vue'
import GuardService from '@/service/guard'
import { useStore } from 'vuex'
import RecordCard from './component/RecordCard.vue'
import { imgBaseUrl } from '@/config.js'
import textPopop from '@/components/text-popup/text-popop.vue'
import CommentPopup from './component/comment/CommentPopup.vue'
import type { GuardRecordItem } from '@/types/guard.d'
import { type IPopupMethods, DataStatus } from '@/types/base.d'

enum GROUP_TYPE_MAP {
	allType = 0,
	mine = 1
}
enum GROUP_TIME_MAP {
	allTime = 0,
	week = 1,
	month = 2,
	year = 3
}
const dataStatus = ref(DataStatus.MORE)
const isShowComment = ref(false)
const postEnrollPopup = ref<IPopupMethods | null>(null)
const inquirePopup = ref<IPopupMethods | null>(null)
const store = useStore()
const navbarInfo = computed(() => store.getters.navbarInfo)
const showFilterDialog = ref(false)
const fromPage = ref('index')
const recordsList = ref<GuardRecordItem[]>([])
const selectedTime = ref('allTime')
const selectedType = ref('allType')
const selectedTypeFeature = ref('featured')
const selectedTypeLatest = ref('null')
const leftWidth = ref(8)
const marginClass = ref('height')
const name = computed(() => store.state.userInfo.userName)
const isFeaturedSelected = ref(true)
const isTypeSelected = ref(false)
const isLatestSelected = ref(false)
const queryParams = reactive({
	pageNum: 1,
	pageSize: 5,
	onlyMe: 0,
	timeLimit: 0,
	isFeatured: 1,
	isLatest: 0
})

/**
 * showVoluteer：是否发布成功
 * showVoluteerFirst：是否是第一次发动态
 * showVoluteerAlready：控制弹窗显示
 */
const showVoluteer = ref(false)
const showVoluteerAlready = ref(false)
const showVoluteerFirst = ref(true)
const currentRecord = ref<GuardRecordItem>()

const contentText = ref({
	contentdown: '上拉显示更多',
	contentrefresh: '正在加载',
	contentnomore: '没有更多数据了'
})
const backToPreviousPage = () => {
	if (fromPage.value === 'index') {
		uni.switchTab({
			url: '/pages/index/index'
		})
	} else {
		uni.navigateTo({ url: `/pages/${fromPage.value}/index` })
	}
}
const onReachBottom = async () => {
	if (dataStatus.value !== DataStatus.LOADING) {
		queryParams.pageNum += 1
		await getRecordList()
	} else {
		return
	}
}
const onPullRefresh = async () => {
	queryParams.pageNum = 1
	recordsList.value = []
	dataStatus.value = DataStatus.LOADING
	await getRecordList()
}
const handleClickPlus = () => {
	uni.redirectTo({
		url: `/pages/guard/submit?from=${fromPage.value}`
	})
}
const handleClickDialog = () => {
	showFilterDialog.value = true
}
const handleClickTopType = async (type: string) => {
	isTypeSelected.value = !isTypeSelected.value
	isFeaturedSelected.value = false
	recordsList.value = []
	if (isTypeSelected.value) {
		selectedType.value = type
		selectedTypeFeature.value = 'null'
		queryParams.onlyMe = GROUP_TYPE_MAP[type as keyof typeof GROUP_TYPE_MAP]
		queryParams.pageNum = 1
		queryParams.isFeatured = 0
	} else {
		selectedType.value = 'allType'
		queryParams.onlyMe = 0
	}
	await getRecordList()
}
const handleClickTopFeature = async (type: string) => {
	isFeaturedSelected.value = !isFeaturedSelected.value
	isTypeSelected.value = false
	isLatestSelected.value = false
	recordsList.value = []
	if (isFeaturedSelected.value) {
		selectedTypeFeature.value = type
		selectedTypeLatest.value = 'null'
		selectedType.value = 'allType'
		queryParams.isFeatured = 1
		queryParams.onlyMe = 0
		queryParams.isLatest = 0
		queryParams.pageNum = 1
	} else {
		selectedTypeFeature.value = 'null'
		queryParams.isFeatured = 0
	}
	await getRecordList()
}
const handleClickTopLatest = async (type: string) => {
	isLatestSelected.value = !isLatestSelected.value
	isFeaturedSelected.value = false
	recordsList.value = []
	if (isLatestSelected.value) {
		selectedTypeLatest.value = type
		selectedTypeFeature.value = 'null'
		queryParams.isLatest = 1
		queryParams.isFeatured = 0
		queryParams.pageNum = 1
	} else {
		selectedTypeLatest.value = 'null'
		queryParams.isLatest = 0
	}
	await getRecordList()
}
const handleClickTime = (time: string) => {
	selectedTime.value = time
	queryParams.timeLimit = GROUP_TIME_MAP[time as keyof typeof GROUP_TIME_MAP]
}
const handleClickType = (type: string) => {
	selectedType.value = type
	queryParams.onlyMe = GROUP_TYPE_MAP[type as keyof typeof GROUP_TYPE_MAP]
}
const handleClickConfirm = async () => {
	showFilterDialog.value = false
	recordsList.value = []
	queryParams.pageNum = 1
	await getRecordList()
}
const getRecordList = async ({ isRefresh = false } = {}) => {
	dataStatus.value = DataStatus.LOADING
	try {
		const { data } = await GuardService.getGuardIssueRecordList({ ...queryParams })
		const { list: newRecords, isLastPage } = data || {}
		if (isRefresh) {
			recordsList.value = recordsList.value.map((record) => {
				const newRecord = (newRecords || []).find((newRecord) => newRecord.id === record.id)
				return newRecord || record
			})
		} else {
			recordsList.value = [...recordsList.value, ...(newRecords || [])]
			dataStatus.value = isLastPage ? DataStatus.NOMORE : DataStatus.MORE
		}
	} catch (error) {
		dataStatus.value = DataStatus.NOMORE
	}
}

const handleInitRecords = async () => {
	queryParams.pageNum = 1
	queryParams.onlyMe = 0
	queryParams.isFeatured = 1
	queryParams.isLatest = 0
	queryParams.timeLimit = 0
	recordsList.value = [] // 清空旧数据（可选）
	await getRecordList()
}
const removeRecordById = (id: number) => {
	recordsList.value = recordsList.value.filter((record) => record.id !== id)
}
const refreshRecords = async (id: number) => {
	await removeRecordById(id)
}
const handleClickCard = () => {
	postEnrollPopup.value?.closePopup()
	uni.navigateToMiniProgram({
		appId: 'wx8493c68ed5fd2cf6',
		path: 'pages-index/index/opp-detail?oppId=WJit7KjbF8abN',
		success() {
			const isVolunteerRegisted = uni.getStorageSync('isVolunteerPage')
			if (isVolunteerRegisted === '' || isVolunteerRegisted === undefined) {
				inquirePopup.value?.openPopup()
			}
		}
	})
}

// Callback function：Already apply
const handleCancelPopup = () => {
	showVoluteerAlready.value = false
}

// Callback function：No volunteer
const handleFailApply = () => {
	uni.setStorageSync('isVolunteer', false)
	inquirePopup.value?.closePopup()
}

// Callback function：Success volunteer
const handleSuccessApply = async () => {
	uni.setStorageSync('isVolunteer', true)
	const resData = await GuardService.getCheckVolunteer()
	if (resData.code == 1) {
		inquirePopup.value?.closePopup()
	}
}

const handleShowComment = async (recordInfo: GuardRecordItem) => {
	currentRecord.value = recordInfo
	isShowComment.value = true
}

const handleCloseComment = async () => {
	await getRecordList({ isRefresh: true })
	isShowComment.value = false
}

onMounted(async () => {
	await getRecordList()
})

onLoad((options) => {
	const from = options.from
	fromPage.value = from || 'index'
	showVoluteer.value = Boolean(options.showVoluteer)
	showVoluteerFirst.value = uni.getStorageSync('isVolunteer')
})

onShow(() => {
	// inquire popup
	if (uni.getStorageSync('isVolunteerPage') && inquirePopup.value) {
		inquirePopup.value?.openPopup()
	}
})

onReady(() => {
	if (uni.getStorageSync('isVolunteerPage')) {
		inquirePopup.value?.openPopup()
	}
	// successful release
	if (showVoluteer.value) {
		if (!showVoluteerFirst.value) {
			// First time
			postEnrollPopup.value?.openPopup()
		} else {
			// More time
			showVoluteerAlready.value = true
		}
	}
})

onHide(() => {
	uni.removeStorageSync('isVolunteerPage')
})

onUnload(() => {
	uni.removeStorageSync('isVolunteerPage')
})
</script>

<style lang="scss" scoped>
.text-area {
	margin-top: 50px;
}
.card-mask {
	display: flex;
	justify-content: center;
	align-items: center;
	position: fixed;
	z-index: 1000;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	transition: opacity 0.3s;
	.card {
		display: flex;
		flex-direction: column;
		gap: 16px;
		position: relative;
		z-index: 9999999;
		.img {
			width: 432rpx;
			height: 380rpx;
		}
		.close {
			position: relative;
			width: 28px;
			height: 28px;
			left: 50%;
			transform: translateX(-50%);
		}
	}
}
.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
	.filter-top-button::after {
		border: none;
	}
	.filter-top {
		height: 30px;
		padding: 8px 9px 8px 9px;
		display: flex;
		flex-direction: row;
		background: #fff8e8;
		justify-content: space-between;
		&-button {
			width: 172rpx;
			border-radius: 4px;
			background: #fff;
			display: flex;
			height: 24px;
			justify-content: center;
			align-items: center;
			margin-top: 6px;
			margin-left: 0px;
			margin-right: 0px;
			.image {
				width: 16px;
				height: 16px;
			}
			.text {
				color: rgba(21, 21, 21, 0.4);
				font-size: 12px;
				letter-spacing: 0.1px;
			}
		}
		&-button.active {
			background: #ffebc2;
			.text {
				color: #ff9d30;
			}
		}
	}
	.filter-mask {
		position: fixed;
		z-index: 1000;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		transition: opacity 0.3s;
	}
	.filter-dialog {
		box-sizing: border-box;
		width: 100%;
		background-size: cover;
		position: fixed;
		z-index: 9999999;
		&-content {
			border-radius: 0px 0px 16px 16px;
			background: #fff;
			padding-bottom: 24px;
			&-title {
				display: flex;
				flex-direction: row;
				justify-content: flex-end;

				.button::after {
					border: none;
				}
				.button {
					display: flex;
					border-radius: 4px;
					background: #fff;
					width: 87px;
					height: 24px;
					padding: 3px 15px;
					justify-content: center;
					align-items: center;
					flex-shrink: 0;
					margin-top: 8px;
					margin-right: 8px;
					.image {
						width: 16px;
						height: 16px;
					}
					.text {
						color: rgba(21, 21, 21, 0.7);
						font-size: 12px;
						letter-spacing: 0.1px;
					}
				}
			}
			&-body {
				display: flex;
				width: 375px;
				padding: 0px 23.5px;
				flex-direction: column;
				justify-content: center;
				align-items: flex-start;
				gap: 16px;
				.top {
					display: flex;
					width: 160px;
					flex-direction: column;
					align-items: flex-start;
					gap: 8px;
					.text {
						color: rgba(21, 21, 21, 0.4);
						letter-spacing: 0.1px;
						font-family: PingFang SC;
						font-size: 12px;
						font-weight: 400;
						line-height: 16.8px;
					}
					.buttons {
						display: flex;
						align-items: center;
						gap: 8px;
						.button {
							display: flex;
							height: 24px;
							flex-shrink: 0;
							padding: 0px 12px;
							justify-content: center;
							align-items: center;
							border-radius: 4px;
							box-shadow: 0px 0px 4.727px 0px rgba(210, 120, 31, 0.04);
							.text {
								font-size: 12px;
								font-weight: 500;
								letter-spacing: 0.1px;
							}
						}
						.button.active {
							background: #ffebc2;
							.text {
								color: #ff9d30;
							}
						}
						.button::after {
							border: none;
						}
					}
				}
			}
			&-button {
				display: flex;
				width: 80px;
				height: 36px;
				padding: 0px;
				margin-right: 24px;
				margin-top: 32px;
				justify-content: center;
				align-items: center;
				border-radius: 44px;
				border: 1px rgba(255, 255, 255, 0.79);
				background: linear-gradient(0deg, #ff9d30 1.14%, #ff9d30 52.14%, #ffb555 101.14%);
				background-size: contain;
				background-repeat: no-repeat;

				color: #fff;
				text-shadow: 0.5px 0.5px 0px #d2781f;
				font-weight: 600;
				line-height: 20px;
				font-family: PingFang SC;
				font-size: 16px;
			}
		}
	}
	.filter-content {
		display: flex;
	}
	.scroll-view {
		height: 95%;
		background: #fff8e8;
	}
	.content {
		margin: -8px 8px 0px 8px;
		.collapse-item {
			border-radius: 8px;
			display: flex;
			padding: 12px 0px 12px 16px;
			justify-content: space-between;
			align-items: center;
			align-self: stretch;
			.collapse-title {
				&-text {
					color: #d2781f;
					font-size: 14px;
					font-weight: 500;
					letter-spacing: 0.1px;
				}
			}
		}
	}
	.plus-records {
		.plus-records-circle {
			width: 52px;
			height: 52px;
			bottom: 74px;
			right: 16px;
			position: absolute;
		}
		.plus-records-plus {
			width: 32px;
			height: 32px;
			bottom: 86px;
			right: 26px;
			position: absolute;
		}
	}
	.content-top {
		font-size: 14px;
		font-weight: 500;
		line-height: 19.6px;
		text-align: center;
		color: rgba(98, 171, 11, 1);
		margin-bottom: 16rpx;
	}
	.content-bottom {
		font-size: 10px;
		font-weight: 500;
		line-height: 14px;
		text-align: center;
		color: rgba(64, 114, 4, 1);
		margin-top: 16rpx;
	}
}
.filter-top-image-tree {
	position: absolute;
	width: 32px;
	height: 30px;
	left: 2.27px;
	bottom: 0;
}
.filter-top-image-sun {
	width: 25px;
	height: 30px;
	position: absolute;
	left: 3px;
	bottom: 0;
}
.filter-top-item {
	width: 172rpx;
	height: 22.67px;
	background: linear-gradient(0deg, #ffb763 0%, #ffbd30 100%);
	box-shadow: 0px 0px 4.73px 0px #d2781f0a;
	border-radius: 4px;
	border: 0.5px solid;
	border-image-source: linear-gradient(90deg, #feb159 0%, rgba(255, 221, 182, 0.98) 100%);
	margin-top: 6px;
	position: relative;
	display: flex;
	align-items: center;
	padding-left: 29.5px;
	box-sizing: border-box;
}
.filter-top-text {
	font-size: 12px;
	font-weight: 500;
	color: #ffffff;
}

.selete-title {
	font-size: 14px !important;
	font-weight: 500 !important;
	line-height: 19.6px !important;
	color: rgba(21, 21, 21, 1) !important;
}
</style>
