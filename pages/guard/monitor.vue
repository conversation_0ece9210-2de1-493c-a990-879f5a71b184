<template>
	<landing v-if="sharePageFlag"></landing>
	<view v-else class="hoof">
		<navbar
			title="蹄窝"
			show-nav-back
			:on-nav-click="backToPreviousPage"
			ele-color="white"
			:nav-back-icon="`${imgBaseUrl}nav/nav-bar-back-white-svg.svg`"
		></navbar>
		<view class="background">
			<image
				v-if="showHoofGuide"
				class="monitor-mask"
				:src="`${imgBaseUrl}monitor/monitor-background-img.png`"
			></image>
			<image class="image" :src="hoofGlobalImage"></image>
			<view class="hoof-number">
				<image class="image" :src="`${imgBaseUrl}guard/guard-hoof.png`"></image>
				<view class="number">
					<text class="text">蹄窝编号</text>
					<text class="content">{{ hoofNumber }}</text>
				</view>
				<image
					class="card"
					:src="`${imgBaseUrl}guard/guard-hoof-card.png`"
					@click="handleClickCard"
				></image>
				<image class="seal" :src="`${imgBaseUrl}monitor/monitor-seal.png`"></image>
			</view>
		</view>
		<view v-if="showHoofGuide" class="mask">
			<view class="hoof-guide">
				<view class="text">
					<text class="title">云村民你好</text>
					<text class="content">你守护的蹄窝在这里</text>
				</view>
				<view class="number">
					<image class="line" :src="`${imgBaseUrl}guard/guard-black.png`"></image>
					<view class="content">
						<text class="text">蹄窝编号</text>
						<text class="number">{{ hoofNumber }}</text>
					</view>
					<image class="line" :src="`${imgBaseUrl}guard/guard-black.png`"></image>
				</view>
				<view class="hoof-image"> </view>
				<view class="button">
					<button class="content" @click="handleClickClose">
						<span class="text">知道了</span>
					</button>
				</view>
			</view>
			<image class="hoof-line" :src="`${imgBaseUrl}guard/guard-hoof-line.png`"></image>
		</view>
		<view v-if="showHoofCard" class="mask" @click="showHoofCard = false">
			<view class="hoof-card-title">
				<view class="claim-title"> <text class="claim-title-green"> 感谢 </text>您的支持！ </view>
				<img :src="`${imgBaseUrl}home/home-claim-line.png`" class="line" />
				<view class="claim-text">
					<text class="claim-text-first">
						您是第
						<text class="claim-text-number">{{ claimData.guardMember }}</text>
						位编号
					</text>
					<text class="claim-text-first"> {{ claimData.hoofprintCode }}蹄窝的守护者 </text>
				</view>
				<img :src="`${imgBaseUrl}home/home-claim-line.png`" class="line" />
			</view>
			<view class="hoof-card" @click.stop>
				<detailsCard :claim-data="claimData" :is-claim-skip-flag="isClaimSkipFlag"></detailsCard>
			</view>
			<button class="share" @click.stop="handleClickShareImage">
				<text class="text">分享</text>
			</button>
		</view>
		<SharePopup ref="sharePopup"></SharePopup>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { imgBaseUrl } from '@/config.js'
import GuardService from '@/service/guard'
import detailsCard from '@/components/details-card/details-card.vue'
import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import SharePopup from '@/components/share-popup/share-popup.vue'
import { useStore } from 'vuex'
import IndexService from '@/service/index'

const store = useStore()
const sharePageFlag = computed(() => store.getters.sharePageFlag)
const sharePopup = ref<InstanceType<typeof SharePopup> | null>(null)
const hoofGlobalImage = ref<string>('')
const hoofImage = ref<string>('')
const hoofNumber = ref<string>('')
const showHoofCard = ref<boolean>(false)
const showHoofGuide = ref<boolean>(true)
const isClaimSkipFlag = ref<boolean>(false)
const topHeight = ref<number>(320)
const leftWidth = ref<number>(72)
const imgHeight = ref<number>(0)
const imgWidth = ref<number>(0)
const claimData = ref<{
	claimTime: string
	hoofprintCode: string
	hoofprintId: number
	imageUrl: string
	guardMember: number
	userName: string
}>({
	claimTime: '',
	hoofprintCode: '',
	hoofprintId: 0,
	imageUrl: '',
	guardMember: 0,
	userName: ''
})

const getMonitorImage = async () => {
	const monitorImageData = await IndexService.getUserCardInfo()
	if (monitorImageData.data) {
		hoofGlobalImage.value = monitorImageData.data.globalUrl
		hoofImage.value = monitorImageData.data.imageUrl
		hoofNumber.value = monitorImageData.data.hoofprintCode
		claimData.value.claimTime = monitorImageData.data.claimTime
		claimData.value.hoofprintCode = monitorImageData.data.hoofprintCode
		claimData.value.hoofprintId = monitorImageData.data.hoofprintId
		claimData.value.imageUrl = monitorImageData.data.imageUrl
	}
}

const handleClickClose = () => {
	showHoofGuide.value = false
}

const handleClickCard = () => {
	showHoofCard.value = true
}

const backToPreviousPage = () => {
	uni.navigateBack({
		delta: 1
	})
}

const handleClickShareImage = () => {
	uni.showToast({
		title: '请点击右上角的“...”进行分享',
		icon: 'none'
	})
}

const getGuardMember = async () => {
	const eventRes = await GuardService.getGuardHoofprint()
	claimData.value.guardMember = eventRes.data?.guardMember || 0
}

onShareAppMessage(async () => {
	sharePopup.value?.openPopup()
	showHoofCard.value = false
	await GuardService.getGuardShare()
})

onShareTimeline(async () => {
	sharePopup.value?.openPopup()
	await GuardService.getGuardShare()
	return {
		title: '京西水峪嘴云村民'
	}
})

onMounted(async () => {
	if (!sharePageFlag.value) {
		await getGuardMember()
		await getMonitorImage()
		const windowInfo = uni.getSystemInfoSync()
		topHeight.value = windowInfo.windowHeight / 4
		leftWidth.value = (windowInfo.windowWidth - 240) / 2
		imgHeight.value = topHeight.value + 60
		imgWidth.value = leftWidth.value + 210
	}
})
</script>
<style scoped>
.monitor-mask {
	width: 100%;
	height: 100%;
	position: fixed;
	z-index: 3;
}
.mask {
	width: 100%;
	height: 100%;
	position: fixed;
	z-index: 3;
	position: fixed;
	z-index: 1000;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.4);
	transition: opacity 0.3s;
}
.share {
	border-radius: 44px;
	border: 1px solid white;
	padding: 6px 16px 6px 16px;
	color: rgba(255, 255, 255, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: transparent;
	position: relative;
	width: 77px;
	height: 32px;
	top: 48rpx;
}
.share .text {
	font-size: 18px;
	line-height: 21.64px;
	font-family: Douyin Sans;
}
.hoof .background .image {
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: 1;
}
.hoof .background .hoof-number .image {
	width: 228rpx;
	height: 108rpx;
	position: fixed;
	left: 16rpx;
	top: 224rpx;
}
.hoof .background .hoof-number .number {
	display: flex;
	flex-direction: column;
	position: relative;
	left: 120rpx;
	top: 240rpx;
}
.hoof .background .hoof-number .number .text {
	font-size: 10px;
	line-height: 14px;
	font-weight: 400;
	z-index: 2;
}
.hoof .background .hoof-number .number .content {
	font-weight: 500;
	font-size: 12px;
	line-height: 16.8px;
	color: rgba(255, 157, 48, 1);
	z-index: 2;
}
.hoof .background .hoof-number .card {
	width: 162rpx;
	height: 162rpx;
	position: fixed;
	bottom: 110rpx;
	right: 52rpx;
	z-index: 2;
}
.hoof .background .hoof-number .seal {
	width: 153px;
	height: 148.4px;
	bottom: 0px;
	position: absolute;
	z-index: 2;
}
.hoof-guide {
	display: flex;
	flex-direction: column;
	position: fixed;
	gap: 20px;
	top: 640rpx;
	left: 134rpx;
}
.hoof-guide .text {
	display: flex;
	flex-direction: column;
	gap: 4px;
	z-index: 1;
}
.hoof-guide .text .title {
	font-size: 20px;
	line-height: 24px;
	color: rgba(157, 225, 28, 1);
	font-family: Douyin Sans;
}
.hoof-guide .text .content {
	font-size: 24px;
	line-height: 28px;
	color: rgba(255, 255, 255, 1);
	font-family: Douyin Sans;
}
.hoof-guide .number {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-weight: 400;
	font-size: 14px;
	line-height: 19.6px;
	z-index: 1;
}
.hoof-guide .number .line {
	width: 460rpx;
	height: 12rpx;
}
.hoof-guide .number .content {
	display: flex;
}
.hoof-guide .number .content .text {
	color: rgba(230, 230, 230, 1);
}
.hoof-guide .number .content .number {
	color: rgba(157, 225, 28, 1);
}
.hoof-guide .hoof-image {
	width: 480rpx;
	height: 480rpx;
}
.hoof-guide .button .content {
	width: fit-content;
	height: 64rpx;
	border-radius: 44px;
	border: 1px solid white;
	padding: 6px 16px 6px 16px;
	color: rgba(255, 255, 255, 0.7);
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: transparent;
}
.hoof-guide .button .content .text {
	font-weight: 400;
	font-size: 14px;
	line-height: 25px;
}
.hoof-line {
	width: 108rpx;
	height: 400rpx;
	position: fixed;
	top: 728rpx;
	right: 100rpx;
}
.hoof-card {
	margin-top: 64rpx;
}
.close {
	width: 56rpx;
	height: 56rpx;
	position: relative;
	top: 40rpx;
	left: 340rpx;
}
.hoof-card-title {
	margin-top: 246rpx;
}
.hoof-card-title .claim-title {
	display: flex;
	justify-content: center;
	align-items: flex-end;
	font-family: Douyin Sans;
	font-size: 26px;
	line-height: 32px;
	letter-spacing: 3px;
	color: #fff;
	margin-bottom: 32rpx;
}
.hoof-card-title .claim-title .claim-title-green {
	width: fit-content;
	font-size: 32px;
	font-family: Douyin Sans;
	line-height: 38.46px;
	text-align: center;
	color: #b4ef3d;
}
.hoof-card-title .claim-text {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	color: rgba(230, 230, 230, 1);
}
.hoof-card-title .claim-text .claim-text-first {
	font-size: 14px;
	line-height: 19.6px;
}
.hoof-card-title .claim-text .claim-text-first .claim-text-number {
	font-size: 24px;
	font-weight: 600;
	line-height: 33.6px;
	color: #a7ec22;
	width: fit-content;
}
.hoof-card-title .line {
	width: 100%;
	height: 12rpx;
}
</style>
