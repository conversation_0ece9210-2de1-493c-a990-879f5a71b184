<template>
	<view class="page-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar title="发布守护" show-nav-back :on-nav-click="backToPreviousPage"></navbar>
		<view class="submit-container">
			<view class="submit-imgs-container">
				<uni-file-picker
					v-if="cameraAuthorized"
					ref="filePickerRef"
					file-media-type="image"
					mode="grid"
					:limit="3"
					:max-size="10"
					:image-styles="imageStyles"
					@select="handleImgSelect"
					@delete="handleImgDelete"
				/>
				<view v-else class="submit-imgs-unauthorized" @click="handleAuthorizeCamera">
					<view class="unauthorized-is-add">
						<view class="unauthorized-icon-add"></view>
						<view class="unauthorized-icon-add unauthorized-icon-add-rotate"></view>
					</view>
				</view>
			</view>
			<view class="submit-texts-title">
				<textarea
					v-model="submitTitle"
					:maxlength="20"
					placeholder="填写标题"
					placeholder-style="color: rgba(21, 21, 21, 0.20);font-size: 16px;"
					auto-height
				/>
			</view>
			<view class="submit-texts-container">
				<textarea
					v-model="submitTexts"
					:maxlength="100"
					placeholder="填写内容"
					placeholder-style="color: rgba(21, 21, 21, 0.20);font-size: 14px;"
					auto-height
				/>
			</view>
			<view class="submit-location-container">
				<view class="location-btn">
					<image
						:src="`${imgBaseUrl}submit/submit-location-icon.svg`"
						class="location-btn-icon"
					></image>
					<text>地点定位</text>
				</view>
				<view v-if="locationSuccessed !== LOCATION_STATUS.SUCCESSED" class="location-status-failed">
					<view :class="['location-status-tag', locationSuccessed]">当前定位不在景区</view>
					<image
						:src="`${imgBaseUrl}submit/location-failed-refresh.svg`"
						class="location-reload-icon"
						@click="getCurrentLocationStatus"
					></image>
				</view>
				<view
					v-if="locationSuccessed === LOCATION_STATUS.SUCCESSED"
					class="location-status-successed"
				>
					<view class="location-status-tag successed">当前定位在景区内</view>
					<image
						:src="`${imgBaseUrl}submit/location-success-refresh.svg`"
						class="location-reload-icon"
						@click="getCurrentLocationStatus"
					></image>
				</view>
			</view>
			<view class="submit-notice-container">
				<image
					:src="`${imgBaseUrl}submit/location-score-notice-icon.svg`"
					class="location-notice-icon"
				></image>
				<text>定位在景区内发布的动态，可以获取更高守护值哦！</text>
			</view>
			<view class="submit-btn-container">
				<button class="submit-btn" @click="handleSubmit">
					<text>发布</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
// package api
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { onLoad } from '@dcloudio/uni-app'
import GuardService from '@/service/guard'
// constants
import { BASE_URL, config, imgBaseUrl, uploadBasePath } from '@/config.js'
import { isPointInPolygon } from '@/utils/count'
import type { GuardIssueParams } from '@/types/guard.d'

const polygon: [number, number][] = [
	[39.965376, 116.045541],
	[39.966257, 116.04658],
	[39.967129, 116.047619],
	[39.967137, 116.047619],
	[39.967305, 116.047589],
	[39.967436, 116.047559],
	[39.967772, 116.048168],
	[39.967971, 116.048608],
	[39.968117, 116.049087],
	[39.968193, 116.049287],
	[39.968507, 116.049487],
	[39.968722, 116.04964],
	[39.968619, 116.05017],
	[39.968505, 116.050742],
	[39.968499, 116.051248],
	[39.968492, 116.051765],
	[39.968316, 116.051952],
	[39.968137, 116.052134],
	[39.967971, 116.05204],
	[39.967798, 116.051941],
	[39.967636, 116.0519],
	[39.967484, 116.051856],
	[39.96741, 116.051927],
	[39.967325, 116.05201],
	[39.967285, 116.052155],
	[39.967243, 116.052305],
	[39.967143, 116.052285],
	[39.967063, 116.052268],
	[39.966998, 116.052237],
	[39.966875, 116.052095],
	[39.966757, 116.051982],
	[39.966648, 116.051947],
	[39.966579, 116.051862],
	[39.966577, 116.051761],
	[39.966609, 116.051715],
	[39.966711, 116.051722],
	[39.966784, 116.051694],
	[39.966802, 116.051608],
	[39.966787, 116.051544],
	[39.966628, 116.051443],
	[39.966376, 116.051426],
	[39.966294, 116.051469],
	[39.966114, 116.051985],
	[39.965982, 116.052686],
	[39.965975, 116.052979],
	[39.965894, 116.05308],
	[39.965748, 116.053198],
	[39.96556, 116.053485],
	[39.965423, 116.053981],
	[39.965388, 116.054297],
	[39.965403, 116.054425],
	[39.965555, 116.054753],
	[39.96587, 116.055036],
	[39.96587, 116.055149],
	[39.965815, 116.055181],
	[39.96571, 116.055097],
	[39.965405, 116.05476],
	[39.965053, 116.054255],
	[39.964689, 116.053571],
	[39.964688, 116.0534],
	[39.964688, 116.053399],
	[39.964855, 116.052891],
	[39.964853, 116.052892],
	[39.964879, 116.052687],
	[39.965038, 116.052078],
	[39.965025, 116.051928],
	[39.964988, 116.051728],
	[39.964706, 116.051507],
	[39.964515, 116.051307],
	[39.964408, 116.051105],
	[39.964429, 116.050839],
	[39.964532, 116.05058],
	[39.96465, 116.049956],
	[39.964641, 116.049475],
	[39.96447, 116.049169],
	[39.964265, 116.048908],
	[39.964178, 116.04869],
	[39.96422, 116.048408],
	[39.964387, 116.048226],
	[39.964441, 116.048005],
	[39.964428, 116.047455],
	[39.964363, 116.04696],
	[39.964456, 116.046399],
	[39.964771, 116.04584],
	[39.965003, 116.045739],
	[39.965089, 116.04557],
	[39.965199, 116.045514],
	[39.965375, 116.045544]
]

const isSubmit = ref(false)
const cameraAuthorized = ref(false)
const store = useStore()
const LOCATION_STATUS = {
	FAILED: 'failed',
	SUCCESSED: 'successed',
	UNABLE: 'unable'
}
// states
const hoofprintId = ref(0)
const fromPage = ref('index')
const imgUrls = ref<string[]>([])
const submitTitle = ref<string>('')
const submitTexts = ref<string>('')
const latitude = ref(0)
const longitude = ref(0)
const filePickerRef = ref<null | HTMLInputElement>(null)

// Define a type for image styles if not already defined
interface UniFilePickerImageStyles {
	width: string
	height: string
	border: {
		color: string
		width: string
		style: string
		radius: string
	}
}

const imageStyles = ref<UniFilePickerImageStyles>({
	width: '111px',
	height: '111px',
	border: {
		color: 'transparent',
		width: '0px',
		style: 'none',
		radius: '0px'
	}
})

const locationSuccessed = ref(LOCATION_STATUS.FAILED)
const navbarInfo = computed(() => store.getters.navbarInfo)
const showVoluteer = ref(false)
const isLocal = ref<number>(0)

const getCurrentLocationStatus = () => {
	console.log('获取定位权限')
	uni.authorize({
		scope: 'scope.userLocation',
		success() {
			uni.getLocation({
				type: 'gcj02',
				success(res) {
					latitude.value = res.latitude
					longitude.value = res.longitude
					if (res.latitude && res.longitude) {
						latitude.value = res.latitude
						longitude.value = res.longitude
						const locationInArea = isPointInPolygon(res.latitude, res.longitude, polygon)
						locationSuccessed.value = locationInArea
							? LOCATION_STATUS.SUCCESSED
							: LOCATION_STATUS.FAILED
					} else {
						locationSuccessed.value = LOCATION_STATUS.FAILED
					}
				},
				fail(err) {
					console.error('获取定位失败', err)
				}
			})
		},
		fail(err) {
			console.error('定位权限被拒绝', err)
			uni.showModal({
				title: '定位权限',
				content: '请在设置中开启定位权限',
				showCancel: false
			})
		}
	})
}

const backToPreviousPage = () => {
	uni.navigateBack({
		delta: 1
	})
}

const handleAuthorizeCamera = () => {
	uni.authorize({
		scope: 'scope.camera',
		success() {
			console.log('相册/摄像头权限被允许')
			cameraAuthorized.value = true
		},
		fail(err) {
			console.error('相册/摄像头权限被拒绝', err)
			cameraAuthorized.value = false
		}
	})
}

const handleImgSelect = (evt: { tempFilePaths: string[] }) => {
	const tempFilePaths = evt.tempFilePaths
	tempFilePaths.forEach((filePath) => {
		uni.uploadFile({
			url: `${BASE_URL}${uploadBasePath}multi`,
			filePath,
			name: 'files',
			header: {
				appId: config.appId,
				Authorization: `Bearer ${uni.getStorageSync('AuthTokens')}`
			},
			success(res) {
				try {
					const parsedRes = JSON.parse(res.data)
					const urls = (parsedRes.data || []).map((item: { url: string }) => item.url)
					imgUrls.value = [...imgUrls.value, ...urls]
				} catch (e) {
					console.error('解析响应出错:', e)
				}
			},
			fail(err) {
				console.error('上传失败:', err)
			}
		})
	})
}

const handleImgDelete = (evt: { tempFile: { name: string } }) => {
	const imgName = (evt.tempFile.name || '').split('.')[0]
	imgUrls.value = imgUrls.value.filter((url) => url.includes(imgName))
}

const handleSubmit = async () => {
	if (isSubmit.value) {
		return
	}
	if (locationSuccessed.value === LOCATION_STATUS.SUCCESSED) {
		isLocal.value = 1
	} else {
		isLocal.value = 0
	}
	isSubmit.value = true
	const params: GuardIssueParams = {
		title: submitTitle.value,
		content: submitTexts.value,
		hoofprintId: hoofprintId.value,
		latitude: latitude.value,
		longitude: longitude.value,
		location: '古道',
		urls: imgUrls.value,
		isLocal: isLocal.value
	}
	try {
		const res = await GuardService.submitGuardRecord(params)
		if (res && res.code === 1) {
			showVoluteer.value = true
			isSubmit.value = false
			uni.setStorageSync('isVolunteer', res.data?.isVolunteer ? 'true' : 'false')
			uni.redirectTo({
				url: `/pages/guard/records?from=${fromPage.value}&showVoluteer=${showVoluteer.value}`
			})
		} else {
			isSubmit.value = false
		}
	} catch (e) {
		console.error(e)
		isSubmit.value = false
	}
}

onLoad((options) => {
	fromPage.value = options.from as string
	hoofprintId.value = Number(options.hoofprintId || 0)
	getCurrentLocationStatus()
	uni.getSetting({
		success(res) {
			if (res.authSetting['scope.camera']) {
				cameraAuthorized.value = true
			} else {
				cameraAuthorized.value = false
			}
		}
	})
})
</script>

<style scoped>
page {
	background-color: #fff;
	height: 100%;
}
.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
}
.submit-container {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
}
.submit-imgs-container {
	padding: 8px 16px;
}

.submit-imgs-unauthorized {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 111px;
	width: 111px;
	border-radius: 3.658px;
	border: 1px solid rgba(21, 21, 21, 0.05);
	background: rgba(21, 21, 21, 0.05);
}

.unauthorized-is-add {
	display: flex;
	align-items: center;
	justify-content: center;
}

.unauthorized-icon-add {
	background-color: rgba(21, 21, 21, 0.1);
	width: 25px;
	height: 2.5px;
	border-radius: 2px;
}

.unauthorized-icon-add-rotate {
	position: absolute;
	transform: rotate(90deg);
}

:deep(.uni-file-picker .file-picker__box) {
	border-radius: 3.658px;
	border: 1px solid rgba(21, 21, 21, 0.05);
	background: rgba(21, 21, 21, 0.05);
	margin-right: 5px;
}

:deep(.uni-file-picker .file-picker__box-content) {
	margin: 0 !important;
}

:deep(.uni-file-picker__container) {
	margin: 0 !important;
}

:deep(.file-picker__progress) {
	display: none !important;
}

:deep(.icon-add) {
	background-color: rgba(21, 21, 21, 0.1) !important;
	width: 25px !important;
	height: 2.5px !important;
}

.submit-texts-container {
	padding: 24px 0 8px 0;
	margin: 0 16px;
	border-bottom: 0.5px solid rgba(21, 21, 21, 0.1);
}
.submit-texts-title {
	padding: 24px 0 8px 0;
	margin: 0 16px;
	border-bottom: 0.5px solid rgba(21, 21, 21, 0.1);
}

textarea {
	width: 100%;
}

.submit-location-container {
	padding: 12px 24px 12px 16px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.location-btn {
	font-size: 14px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.location-btn-icon {
	width: 16px;
	height: 16px;
}

.location-status-tag {
	height: 16px;
	padding: 0 8px;
	font-size: 8px;
	border-radius: 40px;
	display: flex;
	align-items: center;
}

.location-status-successed {
	color: rgba(21, 21, 21, 0.7);
	font-size: 12px;
}

.location-status-failed,
.location-status-successed {
	display: flex;
	align-items: center;
	gap: 8px;
}

.successed {
	color: #7bbc12;
	border: 0.5px solid #7bbc12;
	background: #fbffe8;
}

.failed {
	color: #ff9d30;
	border: 0.5px solid #ff9d30;
	background: #fff8e8;
}

.unable {
	color: #ff9d30;
	border: 0.5px solid #ff9d30;
	background: #fff8e8;
}

.location-reload-icon {
	width: 18px;
	height: 18px;
}

.location-notice-icon {
	width: 16px;
	height: 16px;
}

.submit-notice-container {
	padding: 8px 16px;
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 10px;
	color: #888888;
}

.submit-btn-container {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: flex-end;
	padding-bottom: 74px;
}

.submit-btn {
	position: relative;
	width: 138px;
	height: 44px;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	background: linear-gradient(0deg, #ff9d30 1.14%, #ff9d30 52.14%, #ffb555 101.14%);
	box-shadow: 0px 2px 4px 0px #d2781f40;
	border: 1px solid;
	border-image-source: linear-gradient(
		90.77deg,
		rgba(255, 255, 255, 0.6715) 1.24%,
		rgba(255, 255, 255, 0) 52.44%,
		rgba(255, 255, 255, 0.5865) 97.84%
	);
	border-radius: 44px;
}
.disabled {
	opacity: 0.5;
	cursor: not-allowed;
	pointer-events: none;
}

.submit-btn text {
	z-index: 10;
	color: #fff;
	font-weight: 600;
	text-shadow: 0.5px 0.5px 0px #d2781f;
	font-size: 18px;
}

.submit-btn-bg {
	width: 138px;
	height: 44px;
	top: 0;
	left: 0;
	position: absolute;
}

.location-info {
	padding: 12px 24px 12px 16px;
	font-size: 10px;
	color: #888888;
}
</style>
