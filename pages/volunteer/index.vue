<template>
	<view
		class="container"
		:style="{
			minHeight: `calc(100vh - ${navbarInfo.barHeight}px)`,
			marginTop: `${navbarInfo.barHeight}px`,
			backgroundImage: `url(${imgBaseUrl}volunteer/volunteer-container-bc.png)`
		}"
	>
		<navbar
			title="活动详情"
			showNavBack
			:onNavClick="backToPreviousPage"
			:opacity="navOpacity"
			:eleColor="navEleColor"
		></navbar>
		<image
			:src="`${imgBaseUrl}volunteer/volunteer-container-top.png`"
			mode="aspectFit"
			class="container-img-top"
		/>
		<view class="container-content">
			<!--  participate area -->
			<view class="container-content-title"
				>怎么参与？
				<view class="container-content-title-bc-block"></view>
			</view>

			<view
				class="step-block"
				v-for="(item, index) in stepDetail"
				:key="index"
				:style="{
					backgroundImage: `url(${item.url}`
				}"
			>
				<view class="step-block-title">{{ item.title }}</view>
				<view class="step-block-content">{{ item.content }}</view>
			</view>
			<!-- img show area  -->
			<view class="container-content-title"
				>古道蹄窝
				<view class="container-content-title-bc-block"></view>
			</view>
			<image
				:src="`${imgBaseUrl}volunteer/volunteer-img-show.png`"
				mode="aspectFit"
				class="container-content-img-show"
			/>

			<!-- action area  -->
			<view class="container-content-title"
				>活动详情
				<view class="container-content-title-bc-block"></view>
			</view>
			<view class="detail-area">
				<text class="action-area-content">招募日期：2024-09-05至2025-09-05 </text>
				<text class="action-area-content">项目日期：2024-09-05至2025-09-05</text>
				<text class="action-area-content">项目地址：门头沟区</text>
				<text class="action-area-content">服务时间：线上全天候、线下9时-17时</text>
				<text class="action-area-content">志愿团体：门头沟区妙峰山镇水峪嘴村青年志愿者服务队 </text>
			</view>

			<!-- information area  -->
			<view class="container-content-title"
				>岗位信息
				<view class="container-content-title-bc-block"></view>
			</view>
			<view class="detail-area">
				<text class="action-area-content">岗位名称：千年蹄窝守护者</text>
				<text class="action-area-content">计划招募：1000000</text>
				<text class="action-area-content"
					>岗位描述：通过水峪嘴云村民小程序（云游古道）在线认领数字蹄窝，学习蹄窝保护知识，进行在线除草、清理积水等活动。逐步从线上走到线下，在京西古道上找到属于自己的那个"窝”，进行保护和清理，也可以帮助清理其他蹄窝。</text
				>
				<text class="action-area-content">岗位条件：热爱公益、支持乡村振兴、富于行动</text>
			</view>
		</view>
	</view>
</template>
<script setup>
import { useStore } from 'vuex'
import { computed, ref } from 'vue'
import navbar from '../../components/navbar/navbar.vue'
import { imgBaseUrl } from '../../config'
import { stepDetail } from './config'
import { onPageScroll } from '@dcloudio/uni-app'

const store = useStore()
const navbarInfo = computed(() => store.getters.navbarInfo)
const navOpacity = ref(0)
const navEleColor = ref('#FFFFFF')

const backToPreviousPage = () => {
	uni.navigateBack({
		delta: 1
	})
}

onPageScroll((e) => {
	if (e.scrollTop <= 44) {
		navOpacity.value = e.scrollTop / 44
		navEleColor.value = '#FFFFFF'
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: 'transprant'
		})
	} else {
		navOpacity.value = 1
		navEleColor.value = '#29231D'
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: 'transprant'
		})
	}
})
</script>
<style scoped lang="scss">
.container {
	overflow-y: scroll;
	scrollbar-width: none;
	&::-webkit-scrollbar {
		display: none;
	}

	.container-img-top {
		width: calc(100vw - 32rpx);
		height: 264rpx;
		margin-left: 34rpx;
		vertical-align: top;
	}

	.container-content {
		vertical-align: top;
		background-color: #fff;
		box-sizing: border-box;
		width: calc(100vw - 64rpx);
		margin: 0 32rpx;
		padding: 0 28rpx;
		height: auto;
		border-top-left-radius: 16rpx;
		border-top-right-radius: 16rpx;
		overflow: hidden;

		.container-content-title {
			font-family: Douyin Sans;
			font-size: 16px;
			line-height: 19.23px;
			text-align: center;
			margin-top: 48rpx;
			margin-bottom: 32rpx;
			.container-content-title-bc-block {
				position: relative;
				width: 74rpx;
				height: 8rpx;
				border-radius: 5px;
				left: 305rpx;
				top: -7rpx;
				background-color: rgba(255, 107, 107, 0.7);
			}
			&:first-child {
				margin-top: 40rpx;
			}
		}

		.step-block {
			background-size: cover;
			width: 100%;
			height: 134rpx;
			overflow: hidden;
			font-family: PingFang SC;

			&:not(:last-child) {
				margin-bottom: 32rpx;
			}
			.step-block-title {
				width: 100%;
				text-align: center;
				margin-top: 18rpx;
				font-size: 14px;
				font-weight: 500;
				line-height: 19.6px;
				color: rgba(187, 111, 51, 1);
			}
			.step-block-content {
				width: 100%;
				margin-top: 20rpx;
				margin-left: 38rpx;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				color: rgba(0, 0, 0, 0.7);
			}
		}

		.container-content-img-show {
			width: 100%;
			height: 378rpx;
		}
		.detail-area {
			width: 100%;
			.action-area-content {
				font-family: PingFang SC;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				text-align: left;
				color: rgba(0, 0, 0, 0.8);
				display: block;
				&:not(:last-child) {
					margin-bottom: 32rpx;
				}
			}

			&:last-child {
				margin-bottom: 76rpx;
			}
		}
	}
}
</style>
