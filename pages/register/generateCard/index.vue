<template>
	<landing v-if="sharePageFlag"></landing>
	<view
		v-else
		class="container"
		:style="{ backgroundImage: `url(${imgBaseUrl}register/register-generate-bc.png)` }"
	>
		<img
			:src="`${imgBaseUrl}register/register-generate-title.png`"
			class="title"
			:style="{
				marginTop: `calc(100rpx + ${props.navbarInfo.barHeight}px)`
			}"
		/>

		<view class="card">
			<text class="identity-number">村民编号：{{ villagerInfo.identityNo }}</text>
			<view class="info">
				<text class="info-name">@{{ villagerInfo.userName }}</text>
				<text class="titile-text">云村民登记日</text>
				<text class="info-text">{{ villagerInfo.registerTime }}</text>
				<text class="titile-text">居住地</text>
				<text class="info-text">{{ villagerInfo.city }}</text>
				<text class="titile-text">职业</text>
				<text class="info-text">{{ villagerInfo.career }}</text>
			</view>
			<view class="label" v-if="villagerInfo.label">
				<view
					v-for="(item, index) in villagerInfo.label"
					:key="item"
					class="label-text"
					:style="getGradientStyle(index)"
					>{{ item }}</view
				>
			</view>

			<img
				:src="`${imgBaseUrl}register/register-cartoon-character.png`"
				class="cartoon-character"
			/>
			<img :src="`${imgBaseUrl}two-dimensional-barcode.png`" class="two-dimensional-barcode" />
		</view>

		<view class="button-area">
			<view class="btn-character btn-common" @click="onEntryCharacter">
				<image :src="`${imgBaseUrl}character/character-enter.png`" class="btn-character-pre" />
				<text>自定义村民形象</text>
			</view>
			<view class="btn-space">
				<button class="btn-first btn-common" @click="onEntryVillager">进入云村庄</button>
				<button class="btn-second btn-common" @click="showMyCard">分享</button>
			</view>
		</view>

		<SharePopup ref="sharePopup"></SharePopup>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useStore } from 'vuex'
import { imgBaseUrl } from '../../../config'
import SharePopup from '../../../components/share-popup/share-popup.vue'
import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { GuardApi } from '../../../api/guard'

const guardApi = new GuardApi()

const props = defineProps({
	navbarInfo: {
		type: Object,
		required: true
	},
	userInfo: {
		type: Object,
		required: true
	}
})

const store = useStore()
const sharePageFlag = computed(() => store.getters.sharePageFlag)
const villagerInfo = ref({})
const labelColor = [
	{
		start: 'rgba(255, 181, 194, 1)',
		end: 'rgba(255, 157, 175, 1)'
	},
	{
		start: 'rgba(157, 225, 28, 1)',
		end: 'rgba(128, 202, 18, 1)'
	},
	{
		start: 'rgba(255, 166, 50, 1)',
		end: 'rgba(255, 146, 30, 1)'
	},
	{
		start: 'rgba(125, 203, 237, 1)',
		end: 'rgba(56, 162, 255, 1)'
	}
]
const sharePopup = ref(null)
const getGradientStyle = (index) => {
	const gradient = labelColor[index % labelColor.length]

	return {
		background: `linear-gradient(to right, ${gradient.start}, ${gradient.end})`
	}
}

const onEntryVillager = () => {
	store.dispatch('setUserRegistered', true)
	uni.switchTab({
		url: '/pages/index/index'
	})
}

const showMyCard = () => {
	uni.showToast({
		title: '请点击右上角的“...”进行分享',
		icon: 'none'
	})
}

const onEntryCharacter = () => {
	uni.navigateTo({
		url: '/pages/character/index'
	})
}

onShareAppMessage(async () => {
	sharePopup.value.openPopup()
	await guardApi.getGuardShare()
})

onShareTimeline(async () => {
	sharePopup.value.openPopup()
	await guardApi.getGuardShare()
	return {
		title: '京西水峪嘴云村民'
	}
})

onMounted(() => {
	if (!sharePageFlag.value) {
		villagerInfo.value = { ...props.userInfo }
		villagerInfo.value.label = props.userInfo.label
			? props.userInfo.label.split(',').map((item) => item.trim())
			: []
	}
})
</script>
<style scoped lang="scss">
@mixin flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}

.container {
	width: 100%;
	height: 100%;

	background-size: cover;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;

	.title {
		width: 400rpx;
		height: 210rpx;
	}

	.card {
		background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/register/register-generate-card.png');
		background-size: contain;

		width: 100vw;
		height: 476rpx;
		position: relative;

		.identity-number {
			position: absolute;
			top: 118rpx;
			right: 124rpx;

			font-family: PingFang SC;
			font-size: 9px;
			font-weight: 600;
			line-height: 12.6px;
			text-align: left;
			background: linear-gradient(to right, rgba(125, 203, 237, 1), rgba(157, 225, 28, 1));
			-webkit-background-clip: text;
			background-clip: text;
			-webkit-text-fill-color: transparent;
		}

		.info {
			position: absolute;
			left: 74rpx;
			bottom: 28rpx;
			display: flex;
			flex-direction: column;

			font-weight: 400;
			font-family: PingFang SC;

			.info-name {
				font-size: 14px;
				margin-bottom: 16rpx;
				line-height: 16.83px;
			}
			.titile-text {
				color: rgba(125, 203, 237, 1);
				font-size: 6.22px;
				line-height: 8.71px;
			}
			.info-text {
				font-size: 7.78px;
				line-height: 10.89px;
				&:not(:last-child) {
					margin-bottom: 12.44rpx;
				}
			}
		}

		.label {
			height: 120rpx;
			position: absolute;
			right: 124rpx;
			bottom: 28rpx;
			display: flex;
			flex-direction: column-reverse;
			align-items: flex-end;
			.label-text {
				border-radius: 3px;
				padding: 1px 5px;
				color: #fff;
				height: 25%;
				font-family: PingFang SC;
				font-size: 7px;
				font-weight: 500;
				line-height: 10.73px;
				text-align: left;
				width: fit-content;
				height: fit-content;

				&:not(:first-child) {
					margin-bottom: 8rpx;
				}
			}
		}
		.two-dimensional-barcode {
			width: 92rpx;
			height: 92rpx;
			position: absolute;
			bottom: 234rpx;
			right: 132rpx;
		}
		.cartoon-character {
			width: 260rpx;
			height: 290rpx;
			position: absolute;
			bottom: 18rpx;
			right: 214rpx;
		}
	}

	.button-area {
		position: absolute;
		bottom: 160rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		gap: 32rpx;

		.btn-common {
			border: 1px solid rgba(255, 255, 255, 0.6715);
			background: linear-gradient(0deg, #ff9d30 1.14%, #ff9d30 52.14%, #ffb555 101.14%);
			font-family: PingFang SC;
			border-radius: 44rpx;
			height: 88rpx;
			font-size: 18px;
			font-weight: 600;
			line-height: 20px;
			color: #fff;
			margin: 0;
		}

		.btn-character {
			display: flex;
			align-items: center;
			justify-content: center;
			padding-left: 71px;
			padding-right: 17px;
			position: relative;

			.btn-character-pre {
				width: 47.46px;
				height: 58.97px;
				position: absolute;
				left: 17px;
				bottom: 3px;
				background-size: cover;
			}
		}

		.btn-space {
			@include flex-center;
			justify-content: space-between;
			width: 432rpx;
			gap: 10rpx;

			.btn-first {
				@include flex-center;
				text-align: center;
			}

			.btn-second {
				@include flex-center;
				flex: 1;
			}
		}
	}
}
</style>
