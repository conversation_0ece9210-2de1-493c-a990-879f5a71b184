<template>
	<view
		class="container"
		:style="{
			paddingTop: `${navbarInfo.barheight}px`
		}"
	>
		<navbar
			:showNavBack="stepFlag == 1 ? false : true"
			:onNavClick="decStepFlag"
			:navBackIcon="`${imgBaseUrl}` + 'nav/nav-bar-backWhite-icon.png'"
		></navbar>
		<AddVillage v-if="stepFlag == 1" :formData="formData" :navbarInfo="navbarInfo" />
		<SelectTag
			v-else-if="stepFlag == 2"
			:formData="formData"
			:navbarInfo="navbarInfo"
			@getUserInfo="getUserInfo"
		/>
		<GenerateCard v-else-if="stepFlag == 3" :navbarInfo="navbarInfo" :userInfo="userInfo" />
	</view>
</template>
<script setup>
import { useStore } from 'vuex'
import { onLoad } from '@dcloudio/uni-app'
import { computed, reactive, ref } from 'vue'
import AddVillage from './addVillage/index.vue'
import SelectTag from './selectTag/index.vue'
import GenerateCard from './generateCard/index.vue'
import navbar from '../../components/navbar/navbar.vue'
import { GuardApi } from '../../api/guard'
import dayjs from 'dayjs'
import { imgBaseUrl } from '../../config'
const guardApi = new GuardApi()
const store = useStore()
const stepFlag = computed(() => store.getters['step/stepFlag'])
// const stepFlag = 3
const navbarInfo = computed(() => store.getters.navbarInfo)
const formData = reactive({
	openId: '',
	gender: 0,
	userName: '',
	city: '',
	avatarPic: '',
	label: '',
	career: '',
	inviteCode: ''
})
const userInfo = ref({})
const showAwardCard = ref(false)

// Decrease Step Flag Function
const decStepFlag = () => {
	store.dispatch('step/decStepFlag')
}

// Get User Info Function
const getUserInfo = (data) => {
	userInfo.value = { ...userInfo.value, ...data }
	const { tips } = data || {}
	if (tips === 10000) {
		showAwardCard.value = true
	}
}
const getUserIdCardInfo = async () => {
	const res = await guardApi.getUserIdCard()
	if (res.code === 1) {
		userInfo.value.registerTime = dayjs(res.data.registerTime).format('YYYY年MM月DD日')
	}
}

onLoad((query) => {
	formData.openId = uni.getStorageSync('OpenId')
	getUserIdCardInfo()
	const inviteCode = query.inviteCode || ''
	if (inviteCode !== '') {
		formData.inviteCode = inviteCode
	}
	console.warn('register inviteCode', inviteCode)
})
</script>
<style scoped lang="scss">
.container {
	width: 100vw;
	height: 100vh;
	background-size: contain;
}
</style>
