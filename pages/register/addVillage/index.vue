<template>
	<view
		class="container"
		:style="{ backgroundImage: `url(${imgBaseUrl}register/register-select-bc.png)` }"
	>
		<!-- titleArea -->
		<img :src="`${imgBaseUrl}register/register-addVillage-title.png`" class="top-title" />

		<!-- makeArea -->
		<view class="form-area">
			<form>
				<view class="title">加入云村</view>
				<img
					v-if="!avatarFlag"
					class="avator"
					:src="`${imgBaseUrl}` + 'register/register-addVillage-avator.png'"
					@click="selectImage"
				/>
				<img v-else class="avator round-avator" :src="showAvatarUrl" @click="selectImage" />

				<view class="form-item sex-item-area">
					<label>性别</label>
					<view class="sex-item">
						<view
							class="option"
							:class="{
								selected: formData.gender == GENDER_TYPE.MALE
							}"
							@click="selectSex(1)"
						>
							<img
								src="http://**************:39090/virtual-village-mini-static/register-addVillage-sexMan.svg"
								class="sex"
							/>
							<text>男</text>
						</view>
						<view
							class="option"
							:class="{
								selected: formData.gender == GENDER_TYPE.FAMALE
							}"
							@click="selectSex(2)"
							><img
								class="sex"
								src="http://**************:39090/virtual-village-mini-static/register-addVillage-sexWoman.svg"
							/>
							<text>女</text>
						</view>
					</view>
				</view>

				<view class="form-item">
					<label>昵称</label>
					<input
						class="enter-input"
						v-model="formData.userName"
						type="text"
						placeholder="输入你的昵称"
					/>
				</view>
				<view class="form-item">
					<label>职业</label>
					<input
						class="enter-input"
						v-model="formData.career"
						type="text"
						placeholder="输入你的职业"
					/>
				</view>
				<view class="form-item city-item">
					<label>地区</label>
					<uni-data-picker
						:localdata="provinces"
						class="city-area"
						v-slot:default="{ provinces }"
						popup-title="请选择所在城市"
						@change="onProvinceChange"
						@nodeclick="onNodeClick"
					>
						<view>
							<view class="image-area">
								{{ formData.city }}
								<img
									src="http://**************:39090/virtual-village-mini-static/register-addvillage-DownIcon.svg"
									class="cityIcon"
								/>
							</view>
						</view>
					</uni-data-picker>
				</view>

				<button class="submit-btn" @click="formSubmit">下一步</button>
			</form>
		</view>
	</view>
</template>

<script>
export default {
	options: { styleIsolation: 'shared' }
}
</script>

<script setup>
import { ref } from 'vue'
import RegisterApi from '../../../api/register.js'
import { useStore } from 'vuex'
import regions from './cityList.json'
import { BASE_URL, imgBaseUrl } from '../../../config'
// TODO: fix:props的值只能做读取不可做改动
const props = defineProps({
	formData: {
		type: Object,
		required: true
	}
})
const GENDER_TYPE = { MALE: 1, FAMALE: 2 }

const avatarFlag = ref(false)
const showAvatarUrl = ref('')
const store = useStore()
const selectedProvince = ref('')
const selectedCity = ref('')

// Formatted Data
// eslint-disable-next-line
const provinces = Object.keys(regions).map((province) => ({
	text: province,
	value: province,
	type: 'province',
	children: regions[province].map((city) => ({
		text: city,
		value: city,
		type: 'city'
	}))
}))

// Get City Function
const onProvinceChange = () => {
	if (selectedProvince.value && selectedCity.value) {
		props.formData.city = `${selectedProvince.value} - ${selectedCity.value}`
	}
}
const onNodeClick = (node) => {
	if (node.type === 'province') {
		selectedProvince.value = node.value
		selectedCity.value = ''
	} else if (node.type === 'city') {
		selectedCity.value = node.value
		onProvinceChange()
	}
}

//  Upload Avatar Function
const selectImage = () => {
	uni.chooseImage({
		count: 1,
		sizeType: ['original', 'compressed'],
		sourceType: ['album', 'camera'],
		success: (res) => {
			showAvatarUrl.value = res.tempFilePaths[0]
			const imgSize = res.tempFiles[0].size
			if (imgSize > 10485760) {
				uni.showToast({
					title: '上传图片需小于10MB',
					icon: 'none'
				})
				showAvatarUrl.value = ''
			} else {
				avatarFlag.value = true
				uni.uploadFile({
					url: `${BASE_URL}/oss/api/v1/oss/avatar`,
					filePath: res.tempFilePaths[0],
					name: 'file',
					header: {
						'Content-Type': 'multipart/form-data',
						Authorization: `Bearer ${uni.getStorageSync('AuthTokens')}`
					},
					success: (res) => {
						try {
							const responseData = JSON.parse(res.data)
							if (responseData?.code) {
								props.formData.avatarPic = responseData.data?.url || ''
							} else {
								uni.showToast({
									title: '图片上传失败',
									icon: 'none'
								})
							}
						} catch (error) {
							console.error(error)
						}
					}
				})
			}
		},
		fail: (err) => {
			uni.showToast({
				title: err || '图片上传失败，请重试',
				icon: 'none'
			})
		}
	})
}

// Select Sex Function
const selectSex = (gender) => {
	props.formData.gender = gender
}

// Submit Form Function
const formSubmit = async () => {
	try {
		if (!props.formData.userName) {
			uni.showToast({
				title: '请输入村民昵称',
				icon: 'none',
				position: 'top'
			})
			return
		}
		const resData = await RegisterApi.userNameJudgment(props.formData.userName)

		if (resData.data) {
			store.dispatch('step/addStepFlag')
		} else {
			uni.showToast({
				title: '村民昵称不可用',
				icon: 'none',
				position: 'top'
			})
		}
	} catch (error) {
		uni.showToast({
			title: error || '请求失败，请稍后重试',
			icon: 'none'
		})
	}
}
</script>

<style scoped lang="scss">
.container {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	flex-direction: column;
	background-size: cover;
	position: relative;

	.top-title {
		height: 200rpx;
		width: 522rpx;
		margin-top: 176rpx;
		background-size: cover;
	}

	.form-area {
		width: 100%;
		height: 1156rpx;
		margin-top: 92rpx;
		background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/register/register-addVillage-formBackGround.png');
		background-size: cover;
		display: flex;
		justify-content: center;
		position: absolute;
		bottom: 0;

		form {
			position: relative;
			display: flex;
			justify-content: center;
			width: 714rpx;
			height: 100%;

			.title {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 58rpx;
				width: 100%;

				color: #fff;
				text-shadow: 0.6px 0.6px 0px #5c9709;
				font-family: FZZhengHeiS-B-GB;
				font-size: 18px;
			}

			.avator {
				width: 120rpx;
				height: 120rpx;
				margin: 52rpx auto 80rpx;
				display: block;
			}
			.round-avator {
				border-radius: 50%;
			}

			.sex-item-area {
				position: relative;
				.sex-item {
					position: absolute;
					right: 0;
					display: flex;
					justify-content: center;
					align-items: center;
					.option {
						width: 92rpx;
						height: 44rpx;
						padding: 6rpx 24rpx;
						border-radius: 22rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						.sex {
							width: 44rpx;
							height: 44rpx;
							margin-right: 16rpx;
						}
						&:first-child {
							background-color: rgba(157, 225, 28, 0.2);
							color: rgba(157, 225, 28, 1);
							margin-right: 16rpx;
						}
						&:last-child {
							background-color: rgba(255, 157, 48, 0.2);
							color: rgba(255, 157, 48, 1);
						}

						&.selected {
							box-shadow: 0 0 10px 4px rgba(0, 0, 0, 0.1);
						}
					}
				}
			}

			.form-item {
				width: 576rpx;
				height: 56rpx;
				display: flex;
				align-items: center;
				margin-bottom: 48rpx;
				position: relative;

				label {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 100%;
					min-width: 112rpx;
					color: #151515;
					font-family: 'PingFang SC';
					font-size: 14px;
					font-weight: 400;
				}

				input {
					margin-left: 48rpx;
					height: 100%;
					width: 414rpx;
				}

				.enter-input {
					@extend .common-input-style;
				}
			}

			// city-item
			.city-item {
				.city-area {
					margin-left: 48rpx;
					width: 414rpx;
					height: 100%;
					flex: 1;
					.image-area {
						@extend .common-input-style;

						height: 56rpx;
						display: flex;
						align-items: center;
						position: relative;
						.cityIcon {
							height: 32rpx;
							width: 32rpx;
							position: absolute;
							right: 24rpx;
						}
					}
				}
			}
			// date-item
			.date-item {
				::v-deep .uni-date {
					margin-left: 48rpx;
					width: 414rpx;
					height: 100%;
				}
				::v-deep .uni-date-editor {
					width: 100%;
					height: 100%;
				}
				.date-area {
					border: 1px solid #c7ed68;
					padding-left: 24rpx;
					border-radius: 28rpx;

					height: 100%;

					display: flex;
					align-items: center;

					.dateIcon {
						height: 32rpx;
						width: 32rpx;
						position: absolute;
						right: 24rpx;
					}
				}
			}

			.submit-btn {
				display: flex;
				align-items: center;
				justify-content: center;

				position: absolute;
				background: linear-gradient(0deg, #ff9d30 1.14%, #ff9d30 52.14%, #ffb555 101.14%);

				width: 276rpx;
				height: 88rpx;

				border-radius: 44rpx;

				left: 50%;
				bottom: 116rpx;
				transform: translateX(-50%);

				color: #fff;
				font-family: PingFang SC;
				font-size: 18px;
				font-weight: 600;
				line-height: 20px;
			}
		}
	}
}

// common
.common-input-style {
	border-radius: 28rpx;
	padding-left: 24rpx;
	border: 1px solid #c7ed68;
}
</style>
