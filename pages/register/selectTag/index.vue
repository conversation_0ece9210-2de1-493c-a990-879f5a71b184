<template>
	<view
		class="container"
		:style="{ backgroundImage: `url(${imgBaseUrl}register/register-generate-bc.png)` }"
	>
		<img
			:src="`${imgBaseUrl}register/register-select-title.png`"
			class="title"
			mode="aspectFit"
			:style="{
				marginTop: `calc(27rpx + ${props.navbarInfo.barHeight}px)`
			}"
		/>

		<view class="tag-area">
			<view class="tag-container">
				<view v-for="(line, lineIndex) in groupedTags" :key="lineIndex" class="tag-line">
					<view
						class="tag-item"
						v-for="tag in line"
						:key="tag.name"
						:class="{ selected: tag.selected }"
						@tap="onTagTap(tag)"
					>
						{{ tag.name }}
					</view>
				</view>
			</view>
			<view class="overlay-block"></view>
		</view>

		<view class="bottom-area">
			<view class="diy-tag-area">
				<view class="diy-tag tag-item" :class="{ selected: isDiy }">
					<img
						class="diy-tag-img"
						@click="onEditDiyTag"
						:src="
							isDiy
								? `${imgBaseUrl}register/register-select-icon-pen.png`
								: `${imgBaseUrl}register/register-select-icon-add.png`
						"
					/>
					<text>{{ isDiy ? diyTag : '标签内容' }}</text>
					<img
						class="diy-tag-img"
						@click="onCloseDiyTag"
						:src="
							isDiy
								? `${imgBaseUrl}register/register-select-icon-close.png`
								: `${imgBaseUrl}register/register-select-icon-star.png`
						"
					/>
				</view>
			</view>

			<button
				class="submit-btn"
				@click="submitTag"
				:style="{ backgroundImage: `url(${imgBaseUrl}register/register-select-btn-bc.png)` }"
				:disabled="isSubmitLoading"
				:loading="isSubmitLoading"
			>
				生成云村民证
			</button>
		</view>

		<uni-popup
			class="diy-tag-dialog"
			ref="diyTagRef"
			type="center"
			:animation="false"
			:mask-click="true"
			:is-mask-click="true"
			@maskClick="onCloseDiyTag"
		>
			<view
				class="diy-tag-dialog-container"
				:style="{ backgroundImage: `url(${imgBaseUrl}login-popup-bc.png)` }"
			>
				<view class="diy-tag-title">输入你的自定义标签<view class="bc-block"></view></view>
				<input type="text" v-model="diyTag" class="diy-tag-input" maxlength="10" />
				<button class="diy-btn" @click="onConfirmDiyTag">确认</button>
			</view>
		</uni-popup>
	</view>
</template>
<script setup>
import { onMounted, ref } from 'vue'
import RegisterApi from '../../../api/register.js'
import { useStore } from 'vuex'
import { imgBaseUrl } from '../../../config'

const store = useStore()
const props = defineProps({
	formData: {
		type: Object,
		required: true
	},
	navbarInfo: {
		type: Object,
		required: true
	}
})

// Init Data
const Tags = ref([])
// Processing data
const groupedTags = ref([])
const selectedTags = ref([])
const emit = defineEmits(['getUserInfo'])
const isDiy = ref(false)
const diyTagRef = ref()
const diyTag = ref('')
const isSubmitLoading = ref(false)

const onEditDiyTag = () => {
	diyTagRef.value.open()
}

const onConfirmDiyTag = async () => {
	if (diyTag.value.trim().length > 5) {
		uni.showToast({
			title: '自定义标签不能超过5个字符',
			icon: 'none',
			duration: 2000
		})
		return
	}
	diyTagRef.value.close()
	isDiy.value = diyTag.value.trim()
}

const onCloseDiyTag = () => {
	isDiy.value = false
	diyTag.value = ''
}

// Submit Function
const submitTag = async () => {
	if (diyTag.value && !selectedTags.value.includes(diyTag.value)) {
		selectedTags.value.push(diyTag.value)
	}
	props.formData.label = selectedTags.value.join(',')
	isSubmitLoading.value = true
	try {
		const resData = await RegisterApi.register(props.formData)
		if (resData.code) {
			emit('getUserInfo', resData.data)
			store.dispatch('step/addStepFlag')
		} else {
			uni.showToast({
				title: '提交失败，请重试',
				icon: 'none'
			})
		}
		isSubmitLoading.value = false
	} catch (error) {
		selectedTags.value = selectedTags.value.filter((tag) => tag !== diyTag.value)
		uni.showToast({
			title: error || '请求失败，请稍后重试',
			icon: 'none'
		})
		isSubmitLoading.value = false
	}
}

// Data Process Function
const splitIntoColumns = (tags, columns) => {
	const result = []
	const tagsWithSelected = tags.map((tag) => ({
		...tag,
		selected: false
	}))

	for (let i = 0; i < tagsWithSelected.length; i += columns) {
		result.push(tagsWithSelected.slice(i, i + columns))
	}

	return result
}

// Select Tag Function
const onTagTap = (tag) => {
	if (selectedTags.value.includes(tag.name)) {
		selectedTags.value = selectedTags.value.filter((item) => item !== tag.name)
		tag.selected = false
	} else {
		if (selectedTags.value.length >= 3) {
			uni.showToast({
				title: '您最多可以选择3个村民标签',
				icon: 'none'
			})
			return
		}
		if (selectedTags.value.length >= 5) return
		selectedTags.value.push(tag.name)
		tag.selected = true
	}
}

// Init
onMounted(async () => {
	try {
		const resData = await RegisterApi.getTag()
		if (resData?.code) {
			Tags.value = resData.data
			let Tags_Deal

			// Tags 少于10则进行复制操作
			if (Tags.value.length < 10) {
				Tags_Deal = Array(5).fill(Tags.value).flat()
			} else {
				Tags_Deal = Tags.value
			}

			groupedTags.value = splitIntoColumns(Tags_Deal, 3)
		} else {
			uni.showToast({
				title: '标签加载失败',
				icon: 'none'
			})
		}
	} catch (error) {
		uni.showToast({
			title: '请求出错，请稍后重试',
			icon: 'none'
		})
	}
})
</script>

<style scoped lang="scss">
@mixin flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}

@mixin common-btn {
	background: linear-gradient(0deg, #ff9d30 1.14%, #ff9d30 52.14%, #ffb555 101.14%);

	width: 276rpx;
	height: 88rpx;
	border-radius: 44rpx;

	color: #fff;
	font-family: PingFang SC;
	font-size: 18px;
	font-weight: 600;
	line-height: 20px;
	z-index: 3;

	@include flex-center;
}

.container {
	width: 100%;
	height: 100%;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/register/register-select-bc.png');
	background-size: cover;
	display: flex;
	flex-direction: column;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.7);
		z-index: 1;
	}

	.title {
		height: 182rpx;
		width: 348rpx;
		margin-left: calc(50% - 174rpx);
		z-index: 3;
	}

	.tag-area {
		width: 100%;
		height: 902rpx;
		z-index: 3;
		position: relative;
		overflow: hidden;
		.tag-container {
			position: relative;
			overflow-y: scroll;
			height: 100%;
			.tag-line {
				@include flex-center;
				&:first-child {
					margin-top: 38rpx;
				}

				.tag-item {
					@include flex-center;
					width: 224rpx;
					height: 74rpx;
					border: 1px solid rgba(255, 255, 255, 0.6);

					border-radius: 37rpx;

					margin: 17rpx 12rpx;

					background-color: rgba(255, 255, 255, 0.16);
					color: rgba(255, 255, 255, 0.6);

					font-family: PingFang SC;
					font-size: 18px;
					font-weight: 500;
					line-height: 12.29px;

					// opacity: 0.7;
				}
				.selected {
					background-color: #728232;
					color: #ffffff;
					box-shadow: 0 0 6px rgba(157, 225, 28, 1);
				}
			}

			&::-webkit-scrollbar {
				display: none;
			}
		}

		.overlay-block {
			position: absolute;
			bottom: 0;
			width: 100%;
			height: 120rpx;
			background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/register/register-selete-tag-bc.png');
			pointer-events: none;
		}
	}

	.diy-tag-dialog-container {
		height: 356rpx;
		width: 594rpx;
		display: flex;
		align-items: center;
		flex-direction: column;
		background-size: cover;

		.diy-tag-title {
			margin-top: 50rpx;
			font-family: PingFang SC;
			font-size: 17px;
			font-weight: 500;
			line-height: 23.8px;
			z-index: 2;
			.bc-block {
				position: absolute;
				background-color: rgba(195, 243, 90, 1);
				width: 240rpx;
				height: 30rpx;
				left: 50%;
				top: 75rpx;
				z-index: -1;
				transform: translateX(-50%);
			}
		}
		.diy-tag-input {
			background-color: #fff;
			margin: 32rpx 0;
			height: 80rpx;
			padding-left: 9px;
		}
		.diy-btn {
			@include common-btn;
		}
	}
	.bottom-area {
		@include flex-center;
		flex-direction: column;
		gap: 28rpx;

		.diy-tag-area {
			@include flex-center;

			width: 100%;
			height: 74rpx;
			margin-top: 24rpx;
			z-index: 2;

			.tag-item {
				border: 1px solid rgba(255, 255, 255, 0.6);

				border-radius: 37rpx;

				margin: 17rpx 12rpx;

				padding: 12px 24px;

				background-color: rgba(255, 255, 255, 0.16);
				color: rgba(255, 255, 255, 0.6);

				font-family: PingFang SC;
				font-size: 18px;
				font-weight: 500;
				line-height: 12.29px;
			}

			.diy-tag {
				@include flex-center;
				width: 372rpx;
				height: 100%;
				border-radius: 88rpx;
				gap: 24rpx;

				margin: 0;
				padding: 0;

				.diy-tag-img {
					width: 48rpx;
					height: 48rpx;
				}
			}
			.selected {
				background-color: #728232;
				color: #ffffff;
				box-shadow: 0 0 6px rgba(157, 225, 28, 1);
			}
		}

		.submit-btn {
			@include flex-center;
			background-size: cover;
			width: fit-content;
			height: 88rpx;
			border-radius: 44rpx;

			color: #fff;
			font-family: PingFang SC;
			font-size: 18px;
			font-weight: 600;
			z-index: 3;
		}
	}
}
</style>
