import { ScenicData } from '@/types/scenic'

export interface SpotPosition {
	code: string
	size?: {
		width: number
		height: number
	}
	position?: {
		top: number
		left: number
	}
	sceneIconInfo?: {
		sceneIconBgUrl: string
		top: number
		left: number
	}
	locationCenter?: {
		longitude: number
		latitude: number
	}
	locationRadius?: number
}

export type SpotMapCardInfo = SpotPosition & ScenicData

export const spotPositions: SpotPosition[] = [
	{
		code: 'chengmenlou',
		size: {
			width: 322,
			height: 238
		},
		position: {
			top: 82,
			left: 3
		},

		sceneIconInfo: {
			sceneIconBgUrl: 'visual/visual-scene-3d-chengmenlou.png',
			top: -120,
			left: 120
		},
		locationCenter: {
			longitude: 116.04766967773438,
			latitude: 39.967201199001735
		},
		locationRadius: 20
	},
	{
		code: 'tiejiangpu',
		position: {
			top: 72,
			left: 66
		},
		size: {
			width: 220,
			height: 174
		},
		sceneIconInfo: {
			sceneIconBgUrl: 'visual/visual-scene-3d-tiejiangpu.png',
			top: 40,
			left: -50
		},
		locationCenter: {
			longitude: 116.04525824652777,
			latitude: 39.96716071234809
		},
		locationRadius: 8
	},
	{
		code: 'wangxiange',
		position: {
			top: 62,
			left: 70
		},
		size: {
			width: 146,
			height: 158
		},
		sceneIconInfo: {
			sceneIconBgUrl: 'visual/visual-scene-3d-wangxiange.png',
			top: -120,
			left: 40
		},
		locationCenter: {
			longitude: 116.339599609375,
			latitude: 39.99302463107639
		},
		locationRadius: 10000
	},

	{
		code: 'mianfuting',
		position: {
			top: 55,
			left: 4
		},
		size: {
			width: 192,
			height: 198
		},
		sceneIconInfo: {
			sceneIconBgUrl: 'visual/visual-scene-3d-mianfuting.png',
			top: -100,
			left: 20
		},
		locationCenter: {
			longitude: 116.339599609375,
			latitude: 39.99302463107639
		},
		locationRadius: 10000
	},
	{
		code: 'niujiaolingguancheng',
		position: {
			top: 51,
			left: 45
		},
		size: {
			width: 184,
			height: 182
		},
		sceneIconInfo: {
			sceneIconBgUrl: 'visual/visual-scene-3d-niujiaolingguancheng.png',
			top: -60,
			left: 50
		},
		locationCenter: {
			longitude: 116.0430308285447,
			latitude: 39.96507526803793
		},
		locationRadius: 15
	},
	{
		code: 'guandimiao',
		position: {
			top: 38,
			left: 18
		},
		size: {
			width: 156,
			height: 158
		},
		sceneIconInfo: {
			sceneIconBgUrl: 'visual/visual-scene-3d-guandimiao.png',
			top: -70,
			left: 30
		}
	},
	{
		code: 'qiusiting',
		position: {
			top: 24,
			left: 68
		},
		size: {
			width: 130,
			height: 220
		},
		sceneIconInfo: {
			sceneIconBgUrl: 'visual/visual-scene-3d-qiusiting.png',
			top: -100,
			left: 30
		},
		locationCenter: {
			longitude: 116.04095650195964,
			latitude: 39.963990942155675
		},
		locationRadius: 30
	}
]
