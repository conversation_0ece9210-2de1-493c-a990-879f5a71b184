<template>
	<view class="container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar
			title="古道快讯"
			showNavBack
			:onNavClick="backToPreviousPage"
			:navBackIcon="`${imgBaseUrl}` + 'nav/nav-bar-back-icon.svg'"
			:opacity="navOpacity"
			:eleColor="navEleColor"
		></navbar>

		<view class="information-item" v-for="item in videoData" :key="item.id">
			<view class="information-item-left">
				<video
					:show-center-play-btn="false"
					:poster="item.coverUrl"
					:controls="true"
					ref="el => videoRefs[item.id] = el"
					:src="item.files[0].url"
					class="information-item-video"
					@ended="stopVideo(item.id)"
				></video>
			</view>
			<view class="information-item-right">
				<view class="information-item-title">{{ item.title }}</view>
				<view class="information-item-content">
					<text class="information-item-original">来源：{{ item.original }}</text>
					<text class="information-item-time">发布时间：{{ item.postTime }}</text>
				</view>
			</view>
		</view>

		<view v-if="nextPageFlag" class="loading">正在加载...</view>
		<view v-if="noNextPageFlag" class="loading">没有更多内容了</view>
	</view>
</template>
<script setup>
import { onLoad, onPageScroll, onReachBottom } from '@dcloudio/uni-app'
import { useStore } from 'vuex'
import { ref, computed } from 'vue'
import { GuardApi } from '../../api/guard'
import { imgBaseUrl } from '../../config'
const store = useStore()
const navEleColor = ref('#000000')
const navOpacity = ref(0)
const navbarInfo = computed(() => store.getters.navbarInfo)
const videoData = ref({})
const resData = ref({})
const guardApi = new GuardApi()
const nextPageFlag = ref(false)
const noNextPageFlag = ref(false)

const backToPreviousPage = () => {
	uni.navigateBack({
		delta: 1
	})
}

const nextPage = async () => {
	if (!resData.value.hasNextPage) {
		noNextPageFlag.value = true
		setTimeout(() => {
			noNextPageFlag.value = false
		}, 500)
		return
	}
	nextPageFlag.value = true
	try {
		const res = await guardApi.getVideoNews({
			pageNum: resData.value.nextPage,
			pageSize: resData.value.pageSize
		})
		const { list, ...rest } = res.data
		videoData.value = [...videoData.value, ...list]
		resData.value = { ...rest }
	} catch (error) {
		console.error('Error in guardApi.getVideoNews:', error)
	} finally {
		nextPageFlag.value = false
	}
}

onLoad(async () => {
	try {
		const res = await guardApi.getVideoNews({ pageNum: 1, pageSize: 10 })
		const { list, ...rest } = res.data
		videoData.value = list
		resData.value = { ...rest }
	} catch (error) {
		console.error('Error in guardApi.getVideoNews:', error)
	}
})

onReachBottom(() => {
	nextPage()
})

onPageScroll((e) => {
	if (e.scrollTop <= 44) {
		navOpacity.value = e.scrollTop / 44
		navEleColor.value = '#000000'
	} else {
		navOpacity.value = 1
		navEleColor.value = '#29231D'
	}
})
</script>
<style scoped lang="scss">
.container {
	width: 100vw;
	padding-bottom: 32rpx;
	.information-item {
		width: 100vw;
		height: 220rpx;
		box-sizing: border-box;
		padding: 0 32rpx;
		display: flex;
		margin: 32rpx 0;
		.information-item-left {
			width: 336rpx;
			height: 200rpx;
			overflow: hidden;
			.information-item-video {
				border-radius: 10px;
				height: 100%;
				width: 100%;
				object-fit: cover;
			}
		}

		.information-item-right {
			flex: 1;
			padding-left: 32rpx;
			font-family: PingFang SC;

			.information-item-title {
				font-size: 14px;
				font-weight: 500;
				line-height: 19.6px;
				text-align: left;
			}

			.information-item-content {
				display: flex;
				align-items: flex-start;
				flex-direction: column;
				margin-top: 22rpx;
				gap: 4rpx;
				font-size: 12px;
				font-weight: 400;
				line-height: 16.8px;
				text-align: left;
				color: rgba(0, 0, 0, 0.4);
			}
		}
	}

	.loading {
		width: 100%;
		text-align: center;
	}
}
</style>
