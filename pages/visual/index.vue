<template>
	<view class="map-container">
		<navbar title="云游古道"></navbar>
		<view class="container">
			<view class="map-message-infos"> </view>
			<map-card
				v-for="item in scenicData"
				:key="item.code"
				:spot-info="item"
				@clicked-scene-info="clickedSceneInfo"
			></map-card>
		</view>
		<scene-card
			v-show="showCardPopup"
			:current-scenic-data="currentScenicData"
			@handle-close="cancelEvent"
			@handle-enter-scene="handleEnterScene"
			@handle-enter-ar="handleEnterAR"
		/>
		<tabbar-shadow></tabbar-shadow>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import mapCard from '@/components/map-card/map-card.vue'
import sceneCard from '@/components/scene-card/scene-card.vue'
import ScenicService from '@/service/scenic'
import { findCommonUrl } from '@/utils/count'
import { type SpotMapCardInfo, spotPositions } from './settings/configs'

const scenicData = ref<SpotMapCardInfo[]>([])
const currentScenicData = ref<SpotMapCardInfo>()
const showCardPopup = ref(false)

const handleEnterScene = (sceneData: SpotMapCardInfo) => {
	console.log('sceneData: ', sceneData)
	const { models } = sceneData
	const spotUrl = findCommonUrl(models[0], models[1])
	uni.navigateTo({
		url: '/pages/visual/scene?splatUrl=' + encodeURIComponent(spotUrl)
	})
}

const handleEnterAR = (sceneData: SpotMapCardInfo) => {
	uni.navigateTo({
		url:
			'/pages/ar/vps?projectId=' +
			sceneData.id +
			'&location=' +
			JSON.stringify(sceneData.locationCenter) +
			'&locationName=' +
			sceneData.name
	})
}

const clickedSceneInfo = (message: { clickedSceneCode: string }) => {
	console.log('message: ', message, scenicData.value)
	showCardPopup.value = true
	currentScenicData.value = scenicData.value.find((item) => item.code == message.clickedSceneCode)
}

const cancelEvent = (message: boolean) => {
	showCardPopup.value = message
}

const getScenicPoiInfo = async () => {
	try {
		const { data } = await ScenicService.getPoiList()
		scenicData.value = (data || []).map((item) => {
			return {
				...item,
				position: spotPositions.find((spot) => spot.code == item.code)?.position,
				size: spotPositions.find((spot) => spot.code == item.code)?.size,
				sceneIconInfo: spotPositions.find((spot) => spot.code == item.code)?.sceneIconInfo,
				locationCenter: spotPositions.find((spot) => spot.code == item.code)?.locationCenter
			}
		})
	} catch (error) {
		console.error('获取景点信息失败', error)
	}
}

onMounted(async () => {
	await getScenicPoiInfo()
})
</script>
<style scoped lang="scss">
.map-container {
	width: 100vw;
	height: 100vh;
	overflow: hidden;
}
.container {
	width: 100%;
	height: 100%;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/visual/visual-scene-map-bg.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	overflow: hidden;
}

:deep(.uni-popup__wrapper) {
	background-color: transparent !important;
}
.map-message-infos {
	width: 213rpx;
	height: 213rpx;
	position: absolute;
	top: 10vh;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/visual/visual-scene-gudaokuaixun.png');
	background-repeat: no-repeat;
	background-size: cover;
}
.map-message-infos-btn {
	display: block;
	width: 120rpx;
	height: 44rpx;
	line-height: 44rpx;
	position: absolute;
	bottom: 5rpx;
	left: 35rpx;
	border: 2rpx solid #a7560f;
	border-radius: 58rpx;
	color: #a7560f;
	background: linear-gradient(to bottom, #ffefd4, #ffdca0);
	text-align: center;
	font-size: 12px;
}

.map-popup-containers {
	width: 432rpx;
	height: 448rpx;
	background-size: cover;
	display: flex;
	flex-direction: column;
	align-items: center;

	font-family: PingFang SC;
	line-height: 23.8px;
	font-size: 17px;

	background-color: transparent;
	position: relative;

	.map-popup-text {
		position: absolute;
		bottom: 200rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;

		.map-popup-title {
			text-align: left;
			color: rgba(53, 53, 53, 1);
			font-weight: 500;
			position: relative;
		}

		.map-popup-content {
			font-size: 14px;
			font-weight: 400;
			line-height: 19.6px;
			text-align: left;
			color: rgba(0, 0, 0, 0.55);
			margin-top: 4px;
		}
	}

	.map-popup-btns {
		position: absolute;
		bottom: 50rpx;

		.map-popup-btn {
			height: 72rpx;
			border-radius: 88rpx;
			background-color: rgba(255, 146, 18, 1);
			font-weight: 500;
			text-align: center;
			color: rgba(255, 255, 255, 1);
			display: flex;
			justify-content: center;
			align-items: center;
			margin-bottom: 8px;
		}

		.map-popup-botton {
			font-size: 10px;
			text-align: center;
			color: rgba(0, 0, 0, 0.2);
			font-weight: 500;

			.label-btn-area {
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
}
</style>
