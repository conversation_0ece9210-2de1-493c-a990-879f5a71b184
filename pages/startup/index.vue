<template>
	<view class="container">
		<view class="contextTitle">
			<text class="verticalText">蹄窝深处</text>
			<text class="verticalText">古道悠悠</text>
		</view>
		<view class="footerTitle">
			<view>HI，云村民</view>
			<view>欢迎来到水峪嘴村</view>
		</view>
	</view>
</template>
<script setup lang="ts">
import { useStore } from 'vuex'
import { UserApi } from '@/api/user'
import { AppModel } from '../../api/app.js'
import { setLogged } from '../../utils/loginPromise.js'
import { onLoad } from '@dcloudio/uni-app'

const userApi = new UserApi()
const appModel = new AppModel()
const store = useStore()

const setUserRegisteredStore = (state: boolean) => store.commit('SET_USER_REGISTERED', state)
// Get Login Function
const loginOrRegister = async (inviteCode?: string) => {
	await getLogin()
	let userRegistered = false
	try {
		const res = await userApi.checkUserRegistered(uni.getStorageSync('OpenId'))
		userRegistered = res.data || false
	} catch (error) {
		console.error('Error in userApi.checkUserRegistered:', error)
	}
	setUserRegisteredStore(userRegistered)
	store.commit('SET_USER_LOGIN', true)

	if (!inviteCode) {
		uni.switchTab({
			url: '/pages/index/index'
		})
	} else {
		uni.redirectTo({
			url: '/pages/register/index?inviteCode=' + inviteCode
		})
	}
}

// Get Login Function
const getLogin = async () => {
	try {
		const res = await uni.login()
		if (res.code) {
			const response = await appModel.login({ wxCode: res.code })
			if (response.data.accessToken && response.data.openId) {
				uni.setStorageSync('AuthTokens', response.data.accessToken)
				uni.setStorageSync('OpenId', response.data.openId)
				// formData.openId = response.data.openId
				setLogged(true)
			} else {
				throw new Error('Invalid response data')
			}
		}
	} catch (err) {
		console.error(err)
		uni.showToast({ title: '微信登录失败', icon: 'none' })
	}
}

onLoad((query) => {
	const code = decodeURIComponent(query.scene || '')
	const inviteCode = code.split('=')[1]
	console.warn('starup', inviteCode)

	// TODO：为了展示进行延迟 后续删除
	setTimeout(async () => {
		await loginOrRegister(inviteCode)
	}, 1000)
})
</script>
<style scoped lang="scss">
.container {
	width: 100vw;
	height: 100vh;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/startup/startup-startup-bc.png');
	background-size: cover;
	position: relative;
	.contextTitle {
		padding-top: 298rpx;
		display: flex;
		justify-content: center;
		.verticalText {
			writing-mode: vertical-rl;
			font-family: Douyin Sans;
			font-size: 28px;
			line-height: 34px;
			text-align: left;
			color: rgba(21, 21, 21, 1);
			&:last-child {
				padding-top: 192rpx;
				margin-left: 38rpx;
			}
		}
	}

	.footerTitle {
		position: absolute;
		bottom: 94rpx;
		left: 50%;
		transform: translateX(-50%);
		text-align: center;
		color: #fff;
		font-family: PingFang SC;
		font-size: 17px;
		font-weight: 600;
		line-height: 27px;
		text-align: center;
	}
}
</style>
