<template>
	<view class="confirm-dress-container">
		<navbar show-nav-back :on-nav-click="backToPreviousPage"></navbar>
		<view class="all-dress-container">
			<CharacterCard :equipments-list="confirmDressList" class="cartoon-character"></CharacterCard>
			<span class="character-dress-save-btn" @click="handleSaveDress"></span>
		</view>
	</view>
</template>

<script setup>
import { CharacterCard } from './components/character-card/character.vue'
import { EquipmentApi } from '@/api/character.js'

const equeipmentApi = new EquipmentApi()
const props = defineProps({
	confirmDressList: {
		type: Array,
		defaults: () => []
	}
})
const CURRENT_STATUS = {
	CANCEL: 0,
	CONFIRM: 1
}
async function handleSaveDress() {
	const params = props.confirmDressList.map((item) => {
		return {
			id: item.status === CURRENT_STATUS.CONFIRM ? item.id : 0,
			type: item.type
		}
	})
	await equeipmentApi.saveEquipment({ equipments: params })
	backToPreviousPage()
}

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}
</script>

<style>
.confirm-dress-container {
	width: 100vw;
	height: 100vh;
	background-image: linear-gradient(to top, #f1fcd5, #b3f636);
	position: relative;
}

.all-dress-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	position: absolute;
	top: 500rpx;
}

.character-dress-save-btn {
	display: block;
	position: absolute;
	top: 850rpx;
	width: 208rpx;
	height: 64rpx;
	border-radius: 70rpx;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/character/character-savedress-btn.png');
	background-size: 100%;
	background-repeat: no-repeat;
}

.cartoon-character {
	width: 100%;
}
</style>
