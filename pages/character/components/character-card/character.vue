<template>
	<view class="character-card-container" :style="props.isCard ? 'height: 100%;' : 'height:570rpx'">
		<view class="character-main">
			<image
				:src="`${imgBaseUrl}character/character-default-equipment.png`"
				mode="aspectFit"
				class="img-card"
			/>
		</view>
		<view class="character-clothes" :style="imageStyle(EquipmentsType.CLOTHES)">
			<image :src="equipmentStyle(EquipmentsType.CLOTHES)" mode="aspectFit" class="img-card" />
		</view>
		<view class="character-pants" :style="imageStyle(EquipmentsType.PANTS)">
			<image :src="equipmentStyle(EquipmentsType.PANTS)" mode="aspectFit" class="img-card" />
		</view>
		<view class="character-suit" :style="imageStyle(EquipmentsType.SUIT)">
			<image :src="equipmentStyle(EquipmentsType.SUIT)" mode="aspectFit" class="img-card" />
		</view>
		<view class="character-head" :style="imageStyle(EquipmentsType.GLASS)">
			<image :src="equipmentStyle(EquipmentsType.GLASS)" mode="aspectFit" class="img-card" />
		</view>
		<view class="character-left-hand" :style="imageStyle(EquipmentsType.LEFT_HAND)">
			<image :src="equipmentStyle(EquipmentsType.LEFT_HAND)" mode="aspectFit" class="img-card" />
		</view>
		<view class="character-right-hand" :style="imageStyle(EquipmentsType.RIGHT_HAND)">
			<image :src="equipmentStyle(EquipmentsType.RIGHT_HAND)" mode="aspectFit" class="img-card" />
		</view>
	</view>
</template>
<script setup>
import { imgBaseUrl } from '../../../../config'
import { computed } from 'vue'

const CURRENT_STATUS = {
	CANCEL: 0,
	CONFIRM: 1
}
const EquipmentsType = {
	CLOTHES: 1,
	PANTS: 2,
	LEFT_HAND: 3,
	RIGHT_HAND: 4,
	GLASS: 5,
	SUIT: 6
}
const IMAGE_STYLE_LEVEL = {
	GLASS: 5,
	CLOTHES: 3,
	PANTS: 2,
	LEFT_HAND: 4,
	RIGHT_HAND: 4,
	SUIT: 4
}
const props = defineProps({
	equipmentsList: {
		type: Array,
		default: () => []
	},
	isCard: {
		type: Boolean,
		default: false
	}
})

const equipmentStyle = computed(() => (type) => {
	const equipment = props.equipmentsList.find(
		(item) => item.type === type && item.status === CURRENT_STATUS.CONFIRM
	)
	if (equipment) {
		return equipment.fileUrl
	} else {
		return imgBaseUrl + 'character/character-default-equipment.png'
	}
})

const imageStyle = computed(() => (type) => {
	const equipment = props.equipmentsList.find(
		(item) => item.type === type && item.status === CURRENT_STATUS.CONFIRM
	)
	if (equipment) {
		const types = Object.values(EquipmentsType)
		if (types.includes(type)) {
			const TYPE = Object.keys(EquipmentsType).find((key) => EquipmentsType[key] === type)
			return `z-index: ${IMAGE_STYLE_LEVEL[TYPE]}`
		}
	} else {
		return `z-index: 0`
	}
})
</script>
<style scoped lang="scss">
.character-card-container {
	display: flex;
	justify-content: center;
	position: relative;
}

.character-main,
.character-head,
.character-clothes,
.character-pants,
.character-left-hand,
.character-right-hand,
.character-suit {
	position: absolute;
	width: 100%;
	height: 100%;
}

.character-main {
	z-index: 0;
}
.img-card {
	width: 100%;
	height: 100%;
}
</style>
