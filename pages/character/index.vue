<template>
	<view v-if="!isConfirmDress" class="page-container">
		<navbar show-nav-back :on-nav-click="backToPreviousPage"></navbar>
		<view class="character-container">
			<CharacterCard :equipments-list="currentEquipmentsList"></CharacterCard>
			<view class="character-submit">
				<view class="character-submit-left-btn">
					<span :class="{ 'dress-back-next': dressHistory.length <= 1 }"
						><uni-icons type="left" size="20" @click="handleUndoDress"></uni-icons
					></span>
					<span :class="{ 'dress-back-next': redoHistory.length <= 0 }"
						><uni-icons type="right" size="20" @click="handleRedoDress"></uni-icons
					></span>
				</view>
				<button class="character-dress-confirm-btn" @click="handleConfirmDresss"></button>
			</view>
			<view class="dress-container">
				<view class="character-tab-category-icons">
					<view
						v-for="item in dressList"
						:key="item.type"
						class="character-tab-category-icon"
						:class="{ 'character-dress-selected': selectedCategoryType === item.type }"
						@click="handleChangeType(item.type)"
					>
						<image :src="item.icon" mode="ascpectFit" class="character-tab-category-img"></image>
					</view>
				</view>
				<uni-grid :column="4" :show-border="false" :highlight="false">
					<uni-grid-item>
						<view class="dress-image-container" @click="handleClearDress">
							<image
								class="dress-image-item"
								:src="`${imgBaseUrl}character/character-dress-clear${selectedDressId === 0 ? '-checked' : ''}.png`"
								mode="scaleToFill"
							/>
						</view>
					</uni-grid-item>
					<uni-grid-item v-for="item in currentCategoryList" :key="item.id">
						<view
							class="dress-image-container"
							:class="{ 'dress-active': selectedDressId === item.id }"
							@click="handleChooesseDress(item.id)"
						>
							<image class="dress-image-item" mode="aspectFit" :src="item.previewUrl"> </image>
						</view>
					</uni-grid-item>
				</uni-grid>
			</view>
		</view>
	</view>
	<view v-else class="confirm-dress-main">
		<ConfirmdressCard :confirm-dress-list="currentEquipmentsList"></ConfirmdressCard>
	</view>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
import { EquipmentApi } from '@/api/character.js'
import CharacterCard from './components/character-card/character.vue'
import ConfirmdressCard from './confirmdress.vue'
import { imgBaseUrl } from '../../config'

const equeipmentApi = new EquipmentApi()
const currentEquipmentsList = ref([])
const dressList = ref([])
const selectedCategoryType = ref()
const currentCategoryList = ref([])
const selectedDressId = ref(0)
const isConfirmDress = ref(false)
const dressHistory = ref([])
const redoHistory = ref([])

const CURRENT_STATUS = {
	CANCEL: 0,
	CONFIRM: 1
}
const EquipmentsType = {
	CLOTHES: 1,
	PANTS: 2,
	LEFT_HAND: 3,
	RIGHT_HAND: 4,
	GLASS: 5,
	SUIT: 6
}

async function getCategoryList() {
	const res = await equeipmentApi.getEquipmentType()
	dressList.value = res.data
	selectedCategoryType.value = dressList.value[0].type
	getCurrentCategoryList()
}

function handleConfirmDresss() {
	isConfirmDress.value = true
}

function handleUndoDress() {
	if (dressHistory.value.length > 1) {
		redoHistory.value.push(dressHistory.value.pop())
		const previousDress = dressHistory.value[dressHistory.value.length - 1]
		currentEquipmentsList.value = previousDress.map((item) => {
			return { ...item, status: CURRENT_STATUS.CONFIRM }
		})
	} else {
		dressHistory.value = []
	}
}

function handleRedoDress() {
	if (redoHistory.value.length > 0) {
		dressHistory.value.push(redoHistory.value.pop())
		const nextDress = dressHistory.value[dressHistory.value.length - 1]
		currentEquipmentsList.value = nextDress.map((item) => {
			return { ...item, status: CURRENT_STATUS.CONFIRM }
		})
	} else {
		redoHistory.value = []
	}
}

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}

function handleChangeType(type) {
	selectedCategoryType.value = type
	getCurrentCategoryList()
}

async function getCurrentCategoryList() {
	const res = await equeipmentApi.getEquipmentList(selectedCategoryType.value)
	currentCategoryList.value = res.data || []
}

function handleClearClothAndPants() {
	currentEquipmentsList.value = currentEquipmentsList.value.map((item) => {
		if (item.type === EquipmentsType.CLOTHES || item.type === EquipmentsType.PANTS) {
			return { ...item, status: CURRENT_STATUS.CANCEL, id: 0 }
		}
		return item
	})
}

function handleClearSuit() {
	console.warn('handleClearSuit', currentEquipmentsList.value)
	currentEquipmentsList.value = currentEquipmentsList.value.map((item) => {
		if (item.type === EquipmentsType.SUIT) {
			return { ...item, status: CURRENT_STATUS.CANCEL, id: 0 }
		}
		return item
	})
}

function handleChooesseDress(id) {
	const equipment = currentEquipmentsList.value.find((item) => item.id === id)
	if (!equipment) {
		const newEquipment = currentCategoryList.value.find((item) => item.id === id)
		const existingType = currentEquipmentsList.value.findIndex(
			(item) => item.type === newEquipment.type
		)
		if (existingType !== -1) {
			currentEquipmentsList.value[existingType].status = CURRENT_STATUS.CANCEL
			currentEquipmentsList.value[existingType] = {
				...newEquipment,
				status: CURRENT_STATUS.CONFIRM
			}
		} else {
			currentEquipmentsList.value.push({ ...newEquipment, status: CURRENT_STATUS.CONFIRM })
		}
	} else {
		equipment.status = CURRENT_STATUS.CONFIRM
	}
	selectedDressId.value = id
	if (
		selectedCategoryType.value === EquipmentsType.CLOTHES ||
		selectedCategoryType.value === EquipmentsType.PANTS
	) {
		handleClearSuit()
	} else if (selectedCategoryType.value === EquipmentsType.SUIT) {
		handleClearClothAndPants()
	}
	try {
		dressHistory.value.push(JSON.parse(JSON.stringify(currentEquipmentsList.value) || '[]'))
	} catch (error) {
		console.error('currentEquipmentList error', error)
	}
}
async function getMyCurrentEquipment() {
	const res = await equeipmentApi.getCurrentEquipment()
	currentEquipmentsList.value = res.data.length
		? res.data.map((item) => {
				return { ...item, status: CURRENT_STATUS.CONFIRM }
			})
		: []
	try {
		dressHistory.value.push(JSON.parse(JSON.stringify(currentEquipmentsList.value) || '[]'))
	} catch (error) {
		console.error('currentEquipmentList error', error)
	}
	const firstEquipment = currentEquipmentsList.value[0] || {}
	selectedCategoryType.value = firstEquipment.type || 1
}
function handleClearDress() {
	currentEquipmentsList.value = currentEquipmentsList.value.map((item) => {
		if (item.type === selectedCategoryType.value) {
			return { ...item, status: CURRENT_STATUS.CANCEL, id: 0 }
		} else {
			return item
		}
	})
	selectedDressId.value = 0
}

watch(
	() => selectedCategoryType.value,
	(newVal) => {
		selectedDressId.value =
			currentEquipmentsList.value.find((item) => item.type === newVal)?.id || 0
	}
)
onMounted(() => {
	getCategoryList()
	getMyCurrentEquipment()
})
</script>

<style scoped lang="scss">
page {
	background-color: #fff;
	height: 100%;
}

.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
}

.character-container {
	width: 100vw;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	height: 100%;
	background-image: linear-gradient(to top, #f1fcd5, #b3f636);

	.character-submit {
		height: 58rpx;
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-bottom: 52rpx;
		padding: 0 36rpx;
		box-sizing: border-box;

		.character-submit-left-btn {
			display: flex;
			justify-content: space-between;
			width: 176rpx;
			height: 58rpx;
			padding: 10rpx 30rpx;
			box-sizing: border-box;
			background-color: #fff;
			border-radius: 30rpx;
			position: relative;

			.dress-back-next {
				display: block;
				opacity: 0.5;
				pointer-events: none;
			}
		}

		.character-submit-left-btn::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 1px;
			height: 30rpx;
			background-color: #c0c0c0;
		}
	}

	.character-tab {
		display: flex;
		flex-direction: column;
		width: 100%;
		background-color: #fff;
		border-radius: 30rpx 30rpx 0 0;

		.character-tab-title {
			display: flex;
			padding: 16rpx 80rpx;
			box-sizing: border-box;

			.character-tab-item {
				display: block;
				width: 64rpx;
				height: 44rpx;
				line-height: 44rpx;
				margin-right: 120rpx;
			}
		}

		.character-tab-content {
			width: 100%;
			height: 100%;
			padding: 0 36rpx;
			box-sizing: border-box;
		}
	}
}

.character-dress-confirm-btn {
	width: 176rpx;
	height: 58rpx;
	border-radius: 40rpx;
	margin: 0;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/character/character-dress-confirm-btn.png');
	background-size: 100%;
	background-repeat: no-repeat;
}

.character-tab-active {
	color: #7bbc12;
	border-bottom: #7bbc12 solid 4rpx;
}

.dress-container {
	width: 100%;
	height: 778rpx;
	background-color: #fff;
	border-radius: 40rpx;
	padding: 15rpx 0;
	box-sizing: border-box;
}

.dress-image-container {
	width: 148rpx;
	height: 148rpx;

	.dress-image-item {
		width: 100%;
		height: 100%;
	}
}

.dress-active {
	border: 5rpx solid #b4ef3d;
	box-shadow: 0px 1px 3px 0px #0000002e;
	border-radius: 20rpx;
	box-sizing: border-box;
}

.character-tab-category-icons {
	display: flex;
	width: 100%;
	height: 72rpx;
	padding: 8rpx 36rpx;
	border-bottom: 1px solid #b8b8b8;
	box-sizing: border-box;

	.character-tab-category-icon {
		width: 56rpx;
		height: 56rpx;
		margin-right: 40rpx;

		.character-tab-category-img {
			width: 100%;
			height: 100%;
		}
	}
}

.character-dress-selected {
	width: 100px;
	height: 100px;
	background-color: #b8d58a;
	border-radius: 50%;
}

:deep(.uni-grid-wrap) {
	height: 100%;
	padding: 75rpx 52rpx;
	overflow-y: auto;
}

:deep(.uni-grid-item) {
	width: 152rpx !important;
	height: 152rpx;
	margin-right: 10rpx;
}

:deep(.uni-grid-item__box) {
	width: 152rpx !important;
	height: 152rpx;
}
</style>
