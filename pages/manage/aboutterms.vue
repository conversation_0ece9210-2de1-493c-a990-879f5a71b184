<template>
	<view class="page-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar title="服务条款" show-nav-back :on-nav-click="backToPreviousPage"></navbar>
		<view class="content">
			<scroll-view scroll-y class="scroll-view">
				<view class="content-title">京西水峪嘴云村民用户服务协议</view>
				<view class="content-date">
					<text>更新日期：2024年9月30日</text>
					<text>生效日期：2024年9月30日</text>
				</view>
				<view class="content-texts">
					<view class="content-text">
						<b class="content-bold">前言</b>
						<view class="content-normal"
							>欢迎使用本微信小程序（以下简称“本服务”或“小程序”）。在使用本服务之前，请您务必仔细
							阅读并理解本《微信小程序用户服务协议》（以下简称“协议”）中的各项条款。本协议由您
							与本小程序的开发者（以下简称“我们”或“开发者”）共同缔结，具有法律效力。一旦您使用
							本服务，即表示您已充分理解并同意接受本协议的全部内容。如果您不同意本协议的任何条
							款，请立即停止使用本服务。</view
						>
						<view class="content-normal"
							>如对本协议内容有任何疑问、意见或建议，您可通过发送邮件至【<EMAIL>】
							与我们联系</view
						>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">一、服务内容</b>
						<view class="content-normal">
							<span class="content-normal-bold">1. 服务描述：</span>
							本服务是开发者基于微信平台提供的小程序服务，旨在为用户提供包括但不限于信息查询、功能体验、云游景区、动态发布、内容浏览等多元化的服务体验。
						</view>
						<view class="content-normal">
							<span class="content-normal-bold">2. 服务变更</span>
							我们有权根据业务发展和市场需求调整、增加或减少服务内容，无需事先通知用户。若服务内容变更对用户体验造成重大影响，我们将尽力通过适当方式提前通知用户。
						</view>
					</view>
					<view class="content-text">
						<b>二、用户账号</b>
						<view class="content-normal">
							<span class="content-normal-bold">1. 注册与登录:</span>
							使用本服务前，您需按照微信小程序的注册流程完成账号注册及登录。您应保证提供的注册信息真实、准确、完整，并承诺遵守微信平台的相关规定。
						</view>
						<view class="content-normal">
							<span class="content-normal-bold">2. 账号安全:</span>
							您应妥善保管您的账号信息及密码，对您的账号及密码下发生的所有活动承担全部责任。若发现任何非法使用账号或存在安全漏洞的情况，应立即通知我们。
						</view>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">三、用户行为规范</b>
						<view class="content-normal">
							<span class="content-normal-bold">1. 合法合规:</span>
							您在使用本服务时，应遵守国家法律法规、社会公共道德及微信平台的相关规定，不得发布、传播违法、虚假、淫秽、暴力、侮辱、诽谤等内容。您在账号注册及使用过程中需遵守相关法律法规，不得实施任何侵害国家利益、损害其他公民合法权益、有害社会道德风尚的行为。我们有权对您提交的信息进行审核。
						</view>
						<view class="content-normal">
							<span class="content-normal-bold">2. 尊重知识产权:</span>
							您应尊重并保护本服务及其他用户的知识产权，不得未经授权复制、传播、修改或用于商业目的的内容。
						</view>
						<view class="content-normal">
							<span class="content-normal-bold">3. 禁止行为:</span>
							严禁利用本服务进行任何形式的欺诈、骚扰、恶意攻击、侵犯他人隐私、干扰正常服务秩序等行为。
						</view>
						<view>若发现您有上述违规行为，您可能会被暂停账号直至封号，并承担相应法律责任。</view>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">四、隐私保护</b>
						<view class="content-normal">
							<span class="content-normal-bold">1. 信息收集:</span>
							我们仅在获取您的同意后，收集为提供服务所必需的用户信息，包括但不限于用户注册信息、地理位置数据、相册读写权限等。
						</view>
						<view class="content-normal">
							<span class="content-normal-bold">2. 信息使用:</span>
							我们承诺合法、正当地使用您的信息，除用于提供服务、优化体验外，未经您同意，不会将您的信息泄露给第三方。
						</view>
						<view class="content-normal">
							<span class="content-normal-bold">3. 信息保护:</span>
							我们将采取合理的技术手段和管理措施保护您的信息安全，防止信息泄露、毁损或丢失。</view
						>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">五、责任与免责</b>
						<view class="content-normal">
							<span class="content-normal-bold">1. 服务保障:</span>
							我们将尽力保证服务的稳定性与安全性，但由于技术、网络等因素，可能存在服务中断、数据丢失等风险，我们对此不承担绝对责任。</view
						>
						<view class="content-normal">
							<span class="content-normal-bold">2. 免责声明：</span>
							可能存在的计算机病毒、网络通讯故障、系统维护等方面的因素以及可能发生的不可抗力事件或意外事件，我们在此明确声明对本服务不作明示或暗示的保证，包括对本服务的可适用性、没有错误或疏漏、持续性、准确性、可靠性、适用于某一特定用途或满足用户的需求、使用效果等。对于因不可抗力、第三方原因、用户自身过错或本服务之外的其他因素导致的损失，我们不承担任何责任。</view
						>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">六、协议变更与终止</b>
						<view class="content-normal">
							<span class="content-normal-bold">1. 协议变更：</span>
							我们有权根据业务发展需要，对本协议进行修改。修改后的协议将通过适当方式通知用户，用户继续使用本服务即视为接受修改后的协议。</view
						>
						<view class="content-normal">
							<span class="content-normal-bold">2. 协议终止：</span>
							双方均可随时终止本协议，用户停止使用本服务即视为用户单方面终止本协议。我们有权在法律法规允许的范围内，因任何原因单方面终止本协议并停止提供服务，且无需对用户进行任何赔偿或补偿。</view
						>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">七、争议解决</b>
						<view class="content-normal"
							>本协议的解释、效力及执行均适用中华人民共和国法律。因本协议引起的或与本协议有关的任何争议，双方应首先通过友好协商解决；协商不成时，任何一方均可向开发者所在地人民法院提起诉讼。</view
						>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">八、其他</b>
						<view class="content-normal"
							>本协议未尽事宜，由双方另行协商确定，并签订补充协议。补充协议与本协议具有同等法律效力。</view
						>
					</view>
					<View class="content-end">
						<view class="content-normal">北京京西古道景区管理中心</view>
						<view class="content-normal">2024.9.30</view>
					</View>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()

const navbarInfo = computed(() => store.getters.navbarInfo)

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}
</script>

<style scoped>
page {
	background-color: #fff;
	height: 100%;
}
.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
	background: #fafafa;
}
.content {
	display: flex;
	flex-direction: column;
	height: 100vh;
	width: 100%;
	padding: 24px 24px 94px 24px;
	box-sizing: border-box;
}

.content-title {
	font-size: 18px;
	font-weight: 500;
	line-height: 25.2px;
	display: flex;
	justify-content: center;
	margin-bottom: 24px;
}

.content-date {
	display: flex;
	flex-direction: column;
	gap: 8px;
	font-size: 12px;
	line-height: 16.8px;
	color: #151515cc;
	margin-bottom: 24px;
}
.content-texts {
	display: flex;
	flex-direction: column;
	text-indent: 2em;
	gap: 16px;
}
.content-text {
	display: flex;
	flex-direction: column;
	gap: 8px;
}
.content-bold {
	font-weight: 600;
	line-height: 30px;
	font-size: 16px;
}
.content-normal {
	font-size: 14px;
	font-weight: 400;
	line-height: 16.8px;
	color: rgba(21, 21, 21, 0.8);
}
.content-normal-bold {
	font-weight: 600;
}
.content-end {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8px;
}
.scroll-view {
	height: 100%;
}
.scroll-view ::-webkit-scrollbar {
	display: none;
	width: 0;
	height: 0;
	color: transparent;
}
</style>
