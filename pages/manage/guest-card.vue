<template>
	<view
		class="guest-card"
		:style="{ backgroundImage: `url(${imgBaseUrl}home/guest-manage-card-bg.png)` }"
	>
		<view class="guest-card-avatar-wrapper">
			<image :src="`${imgBaseUrl}home/guest-manage-card-avatar.png`" class="guest-card-avatar" />
		</view>
		<view class="guest-card-info">请先点击登录</view>
		<view class="guest-card-btn">
			<image
				:src="`${imgBaseUrl}home/guest-home-card-btn.png`"
				class="guest-card-btn-img"
				@click="gotoRegister"
			/>
		</view>
	</view>
</template>

<script setup>
import { imgBaseUrl } from '@/config'

const gotoRegister = () => {
	uni.navigateTo({
		url: '/pages/register/index'
	})
}
</script>

<style scoped>
.guest-card {
	width: 686rpx;
	height: 258rpx;
	position: absolute;
	bottom: 0;
	left: 32rpx;
	background-size: 100% 100%;
	background-repeat: no-repeat;
}

.guest-card-avatar-wrapper {
	width: 144rpx;
	height: 144rpx;
	position: absolute;
	top: -46rpx;
	left: 54rpx;
	background-color: #fff;
	border-radius: 50%;
}

.guest-card-avatar {
	width: 144rpx;
	height: 144rpx;
}

.guest-card-info {
	font-size: 16px;
	color: rgba(21, 21, 21, 0.8);
	position: absolute;
	top: 24rpx;
	left: 218rpx;
	font-weight: 500;
}

.guest-card-btn {
	width: 100%;
	height: 200rpx;
	position: absolute;
	bottom: 0;
	left: 0;
	display: flex;
	justify-content: center;
	align-items: flex-end;
}

.guest-card-btn-img {
	width: 192rpx;
	height: 58rpx;
	margin-bottom: 42rpx;
}
</style>
