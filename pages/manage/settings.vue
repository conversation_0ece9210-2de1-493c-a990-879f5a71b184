<template>
	<view class="page-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar title="账户设置" show-nav-back :on-nav-click="backToPreviousPage"></navbar>
		<view class="content">
			<view class="top-area">
				<view class="delete-account-btn" @click="showPopup('deletePopup')">注销账号</view>
			</view>
			<!-- TODO: 退出登录依赖微信鉴权 -->
			<view class="bottom-area">
				<view class="logout-btn" @click="showPopup('logoutPopup')">退出登录</view>
			</view>
		</view>
		<uni-popup
			ref="deletePopup"
			type="bottom"
			background-color="#F2F3F5"
			border-radius="10px 10px 0 0"
		>
			<view class="popup-box">
				<view class="popup-box-top">
					<view class="popup-box-title">
						<text class="popup-box-title-fir">确定注销账号？</text>
						<text class="popup-box-title-sec">注销后当前账号数据不可恢复</text>
					</view>
					<view class="popup-box-handle-btn" @click="onDeregister">注销</view>
				</view>

				<view class="popup-box-bottom" @click="onCancel">取消</view>
			</view>
		</uni-popup>
		<uni-popup
			ref="logoutPopup"
			type="bottom"
			background-color="#F2F3F5"
			border-radius="10px 10px 0 0"
		>
			<view class="popup-box">
				<view class="popup-box-top">
					<view class="popup-box-title">
						<text class="popup-box-title-fir">确定退出账号？</text>
					</view>
					<view class="popup-box-handle-btn" @click="handleLogOut">退出</view>
				</view>

				<view class="popup-box-bottom">取消</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import ManageApi from '../../api/manage.js'
const store = useStore()

interface PopupActions {
	open: () => void
	close: () => void
}

const deletePopup = ref<PopupActions | null>(null)
const logoutPopup = ref<PopupActions | null>(null)
const navbarInfo = computed(() => store.getters.navbarInfo)
const userLogin = computed(() => store.getters.userLogin)

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}
const showPopup = (popup: string) => {
	if (popup === 'deletePopup' && deletePopup.value) {
		deletePopup.value.open()
	} else if (popup === 'logoutPopup' && logoutPopup.value) {
		logoutPopup.value.open()
	}
}

const onDeregister = async () => {
	const res = await ManageApi.deregister()
	store.dispatch('clearState')
	uni.clearStorageSync()
	if (res.code === 1) {
		uni.showToast({
			title: '注销成功',
			icon: 'none',
			duration: 2000
		})
		store.dispatch('step/resetStep')
		uni.reLaunch({ url: '/pages/startup/index' })
	}
}

const onCancel = () => {
	deletePopup.value?.close()
	logoutPopup.value?.close()
}

const handleLogOut = () => {
	store.dispatch('clearState')
	store.dispatch('setUserLogin', false)
	uni.showToast({
		title: '退出成功',
		icon: 'none'
	})
	uni.switchTab({ url: '/pages/index/index' })
}
</script>

<style scoped>
page {
	background-color: #fff;
	height: 100%;
}
.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
	background: #fafafa;
}
.content {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;
	width: 100%;
	padding: 16px 16px 78px 16px;
	box-sizing: border-box;
}

.delete-account-btn {
	width: 100%;
	padding: 18px;
	font-size: 14px;
	border-radius: 8px;
	background-color: #fff;
	box-sizing: border-box;
}

.bottom-area {
	padding: 0 10px;
}

.logout-btn {
	width: 100%;
	padding: 10px 0;
	display: flex;
	background-color: #fff;
	align-items: center;
	justify-content: center;
	font-size: 16px;
	border: 1px solid #151515cc;
	border-radius: 5px;
}

.popup-box {
	display: flex;
	flex-direction: column;
}

.popup-box-top {
	background-color: #fff;
	border-radius: 10px 10px 0 0;
}

.popup-box-title {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0px -0.5px 0px 0px #e5e6eb inset;
	height: 70px;
}

.popup-box-title-fir {
	padding-top: 2px;
	font-size: 16px;
	font-weight: 500;
	line-height: 22px;
	color: #1d2129;
}

.popup-box-title-sec {
	font-size: 12px;
	line-height: 22px;
	color: #86909c;
}

.popup-box-handle-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16px 0;
	font-size: 16px;
	line-height: 22px;
	color: #f53f3f;
	box-shadow: 0px -0.5px 0px 0px #e5e6eb inset;
}

.popup-box-bottom {
	box-shadow: 0px -0.5px 0px 0px #e5e6eb inset;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16px 0;
	font-size: 16px;
	line-height: 22px;
	margin-top: 8px;
	background-color: #fff;
}
</style>
