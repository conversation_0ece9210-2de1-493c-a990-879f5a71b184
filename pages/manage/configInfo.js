import { imgBaseUrl } from '../../config'

export const btnInfo = [
	{
		btnName: '云村民证',
		btnUrl: 'manage/manage-icon-certificate.png',
		btnKey: 'character'
	},
	{
		btnName: '账户设置',
		btnUrl: 'manage/manage-icon-setting.png',
		btnKey: 'setting'
	},
	{
		btnName: '关于我们',
		btnUrl: 'manage/manage-icon-about.png',
		btnKey: 'about'
	},
	{
		btnName: '意见反馈',
		btnUrl: 'manage/manage-icon-lianxi.png',
		btnKey: 'feedback'
	}
]

export const levelConfig = [
	{
		url: imgBaseUrl + 'manage/manage-level-1.png',
		color: 'rgba(121, 194, 5, 1)',
		leftTextColor: 'rgba(128, 148, 91, 1)',
		rightTextProgressColor: ' rgba(121, 194, 5, 1)',
		levelName: '古道西风',
		level: 1
	},
	{
		url: imgBaseUrl + 'manage/manage-level-2.png',
		color: 'rgba(246, 214, 40, 1)',
		leftTextColor: 'rgba(137, 125, 68, 1)',
		rightTextProgressColor: ' rgba(209, 170, 0, 1)',
		levelName: '古道行者',
		level: 2
	},
	{
		url: imgBaseUrl + 'manage/manage-level-3.png',
		color: 'rgba(249, 152, 44, 1)',
		leftTextColor: 'rgba(164, 131, 94, 1)',
		rightTextProgressColor: 'rgba(223, 118, 0, 1)',
		levelName: '古道热肠',
		level: 3
	},
	{
		url: imgBaseUrl + 'manage/manage-level-4.png',
		color: 'rgba(39, 190, 255, 1)',
		leftTextColor: 'rgba(97, 139, 157, 1)',
		rightTextProgressColor: 'rgba(0, 157, 228, 1)',
		levelName: '古道匠人',
		level: 4
	},
	{
		url: imgBaseUrl + 'manage/manage-level-5.png',
		color: 'rgba(244, 83, 88, 1)',
		leftTextColor: 'rgba(162, 101, 103, 1)',
		rightTextProgressColor: 'rgba(232, 121, 125, 1)',
		levelName: '古道先锋',
		level: 5
	}
]

export const cardUrls = [
	{
		infoUrl: imgBaseUrl + 'manage/manage-card-info-bottom-1.png',
		bcUrl: imgBaseUrl + 'manage/manage-card-info-bc-1.png'
	},
	{
		infoUrl: imgBaseUrl + 'manage/manage-card-info-bottom-2.png',
		bcUrl: imgBaseUrl + 'manage/manage-card-info-bc-2.png'
	},
	{
		infoUrl: imgBaseUrl + 'manage/manage-card-info-bottom-3.png',
		bcUrl: imgBaseUrl + 'manage/manage-card-info-bc-3.png'
	},
	{
		infoUrl: imgBaseUrl + 'manage/manage-card-info-bottom-4.png',
		bcUrl: imgBaseUrl + 'manage/manage-card-info-bc-4.png'
	},
	{
		infoUrl: imgBaseUrl + 'manage/manage-card-info-bottom-5.png',
		bcUrl: imgBaseUrl + 'manage/manage-card-info-bc-5.png'
	}
]
