<template>
	<view class="container">
		<navbar title="我的"></navbar>
		<view
			class="card-container"
			:style="{
				backgroundImage: `url(${imgBaseUrl}manage/manage-card-bc.png)`
			}"
		>
			<GuestCard v-if="!userRegistered" />
			<CharacterCard
				v-if="isLogin && userRegistered"
				:equipments-list="currentEquipmentsList"
				:is-card="true"
				class="image"
			/>
			<view
				v-if="userRegistered"
				class="card-info"
				:style="{
					backgroundImage: `url(${cardUrls[userInfo.level - 1]?.bcUrl})`
				}"
			>
				<view v-if="isLogin" class="login-card-wrapper">
					<!-- top area -->
					<view class="card-info-top">
						<!-- <img :src="`${userInfo.avatarPic}`" class="user-avatar" /> -->
						<view
							class="user-avatar"
							:style="{ backgroundImage: `url(${userInfo.avatarPic})` }"
						></view>
						<view class="user-info">
							<text class="user-name">{{ userInfo.userName }}</text>
							<text class="user-gender-city">
								<text v-if="userInfo.gender" class="user-gender"
									>{{
										userInfo.gender == 1 ? '男' : userInfo.gender == 2 ? '女' : ''
									}}&nbsp;&nbsp;</text
								>
								<text class="user-city">{{ userInfo.city }}</text>
							</text>
						</view>
						<button class="edit-btn" @click="onEdit">编辑资料</button>
					</view>

					<!-- green area -->
					<view
						class="card-info-bottom"
						:style="{
							backgroundImage: `url(${cardUrls[userInfo.level - 1]?.infoUrl})`
						}"
					>
						<view class="bottom-container">
							<view class="bottom-info-top">
								<text class="level-show"
									>LV.{{ userInfo.level || 1 }}
									<text class="level-show-title"
										>{{ levelConfig[userInfo.level - 1]?.levelName }}
										<img :src="`${imgBaseUrl}manage/manage-icon-star.png`" class="icon-star" />
									</text>
								</text>
								<view class="level-detail" @click="onInterest">
									了解等级权益
									<image
										class="icon-level-detail"
										:src="`${imgBaseUrl}manage/manage-card-icon-levelDetail.png`"
									/>
								</view>
							</view>
							<view class="bottom-info-progress">
								<progress
									:percent="(userInfo.points / userInfo.nextLevelPoints) * 100"
									stroke-width="3"
									:activeColor="(levelConfig[userInfo.level - 1] || {}).rightTextProgressColor"
									backgroundColor="#fff"
								/>
							</view>
							<view class="bottom-info-bottom">
								<text
									class="value-left"
									:style="{ color: (levelConfig[userInfo.level - 1] || {}).leftTextColor }"
									@click="handleGoScoreHistory"
								>
									守护值{{ userInfo.points }}/{{ userInfo.nextLevelPoints }}</text
								>

								<text
									v-if="userInfo.level !== 5"
									class="value-right"
									:style="{ color: (levelConfig[userInfo.level - 1] || {}).rightTextProgressColor }"
									>还差{{ userInfo.nextLevelPoints - userInfo.points }}分升级至{{
										levelConfig[userInfo.level]?.levelName
									}}</text
								>
							</view>
						</view>
					</view>
				</view>
				<view v-else class="login-false">
					<view class="login-false-top"
						><img :src="`${imgBaseUrl}manage/manage-login-avatar.png`" class="login-false-avatar" />
						<text class="login-false-text">请先点击登录</text>
					</view>
					<button class="login-false-bottom" @click="onLogin">点击登录</button>
				</view>
			</view>
		</view>

		<view class="btns-container">
			<view class="btns">
				<button
					v-for="(item, index) in btnInfo"
					:key="item.btnName"
					class="btn-item"
					:open-type="item.btnKey == 'feedback' ? 'feedback' : ''"
					@click="onBtnClick(item)"
				>
					<!-- btn -->
					<view class="btn">
						<view class="btn-left">
							<img :src="`${imgBaseUrl}${item.btnUrl}`" class="icon" />{{ item.btnName }}
						</view>

						<img :src="`${imgBaseUrl}manage/manage-icon-leftBtn.png`" class="icon left-Icon" />
					</view>
					<!-- line -->
					<view v-if="index != btnInfo.length - 1" class="line"></view>
				</button>
			</view>
		</view>

		<tabbar-shadow></tabbar-shadow>
	</view>
</template>
<script setup>
import { imgBaseUrl } from '../../config.js'
// import ManageApi from '../../api/manage.js'
import { btnInfo, levelConfig, cardUrls } from './configInfo.js'
import { ref, watch, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { UserApi } from '../../api/user'
import { EquipmentApi } from '@/api/character'
import CharacterCard from '../../pages/character/components/character-card/character.vue'
import GuestCard from './guest-card.vue'
import { onShow } from '@dcloudio/uni-app'
import { handleGuestMode } from '@/utils/common'

const userApi = new UserApi()
const store = useStore()
const isLogin = ref(false)
const userInfo = computed(() => store.state.userInfo)
const userRegistered = computed(() => store.state.userRegistered)
const equeipmentApi = new EquipmentApi()
const currentEquipmentsList = ref([])

const onBtnClick = handleGuestMode((item) => {
	const RouterMap = {
		setting: '/pages/manage/settings',
		about: '/pages/manage/about',
		character: '/pages/manage/character'
	}
	if (RouterMap[item.btnKey]) {
		uni.navigateTo({
			url: RouterMap[item.btnKey]
		})
	}
})

// Login Function
const onLogin = async () => {
	const { data } = await userApi.getUserProfile()
	store.dispatch('setUserLogin', data)
	const signData = uni.getStorageSync('signInfo')
	store.dispatch('setSignInfo', signData)
	isLogin.value = true
}

const onEdit = () => {
	uni.navigateTo({
		url: '/pages/manage/edit'
	})
}

const onInterest = () => {
	uni.navigateTo({
		url: '/pages/manage/interestLevel'
	})
}
const getMyCurrentEquipment = async () => {
	const res = await equeipmentApi.getCurrentEquipment()
	const data =
		(res.data || []).map((item) => {
			return { ...item, status: 1 }
		}) || []
	currentEquipmentsList.value = data
}

const handleGoScoreHistory = () => {
	uni.navigateTo({
		url: '/pages/score/index'
	})
}
watch(
	() => store.getters.userLogin,
	() => {
		isLogin.value = store.getters.userLogin
		userInfo.value = store.getters.userInfo
	},
	{ immediate: true }
)

onMounted(async () => {
	getMyCurrentEquipment()
})
onShow(() => {
	getMyCurrentEquipment()
})
</script>
<style scoped lang="scss">
// common
@mixin avatar-position {
	position: absolute;
	left: 46rpx;
	top: -54rpx;
	width: 136rpx;
	height: 136rpx;
	border: 8rpx solid #fff;
	border-radius: 50%;
	object-fit: cover;
	box-sizing: border-box;
}

// alone
.container {
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	// padding: 0 40rpx 0 38rpx;
	box-sizing: border-box;

	.card-container {
		width: 100vw;
		height: 550rpx;
		background-size: cover;
		position: relative;

		.card-info {
			display: flex;
			flex-direction: column;
			border-radius: 24rpx;
			box-sizing: border-box;
			width: calc(100vw - 78rpx);
			height: 258rpx;
			background-size: cover;
			background-clip: content-box;
			position: absolute;
			bottom: 0;
			margin: 0 40rpx 0 38rpx;

			.card-info-top {
				display: flex;
				position: relative;
				flex: 1;

				.user-avatar {
					@include avatar-position;
					background-size: cover;
					background-repeat: no-repeat;
				}

				.user-info {
					margin-top: 24rpx;
					margin-left: 218rpx;
					height: fit-content;
					width: fit-content;

					.user-name {
						font-size: 16px;
						font-weight: 700;
						line-height: 19.23px;
						text-align: left;
					}

					.user-gender-city {
						display: flex;
						flex-direction: column;
						align-items: center;
						font-family: PingFang SC;
						font-size: 12px;
						font-weight: 400;
						line-height: 16.8px;
						text-align: left;
						color: rgba(109, 111, 118, 1);
					}
				}

				.edit-btn {
					display: flex;
					justify-content: center;
					align-items: center;

					background-color: rgba(255, 220, 160, 0.32);
					width: fit-content;
					height: 34rpx;
					border-radius: 23rpx;
					border: 0.5px solid rgba(255, 157, 48, 1);

					position: absolute;
					right: 26rpx;
					top: 50%;
					transform: translateY(-50%);

					font-family: PingFang SC;
					font-size: 10px;
					font-weight: 400;
					line-height: 13px;
					text-align: left;
					overflow: auto;
					color: rgba(255, 158, 49, 1);
				}
			}

			.card-info-bottom {
				box-sizing: border-box;
				width: 100%;
				height: 142rpx;
				padding: 0 8rpx 8rpx 8rpx;
				background-size: cover;
				background-clip: content-box;
				position: relative;
				bottom: 0;
				border-bottom-left-radius: 24rpx;
				border-bottom-right-radius: 24rpx;

				.bottom-container {
					padding-left: 34rpx;
					padding-right: 16rpx;
					font-family: PingFang SC;
					font-size: 10px;
					font-weight: 400;

					.bottom-info-top {
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-top: 16rpx;

						.level-show {
							font-family: Douyin Sans;
							font-size: 19.13px;
							line-height: 22.99px;
							text-align: left;
							color: rgba(255, 255, 255, 1);

							.level-show-title {
								font-family: Douyin Sans;
								font-size: 14.34px;
								line-height: 17.24px;
								text-align: left;
								position: relative;

								.icon-star {
									position: absolute;
									top: 0;
									right: 0;
									width: 38rpx;
									height: 38rpx;
								}
							}
						}

						.level-detail {
							display: flex;
							align-items: center;
							justify-content: center;
							gap: 2rpx;
							line-height: 13px;
							text-align: left;
							color: rgba(0, 0, 0, 0.6);

							.icon-level-detail {
								width: 20rpx;
								height: 20rpx;
							}
						}
					}

					.bottom-info-progress {
						margin-top: 16rpx;
						width: 602rpx;
						height: 6rpx;

						progress {
							width: 100%;
						}
					}

					.bottom-info-bottom {
						width: 602rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-top: 6rpx;

						line-height: 14px;

						.value-left {
							color: rgba(128, 148, 91, 1);
						}

						.value-right {
							color: rgba(121, 194, 5, 1);
						}
					}
				}
			}

			.login-false {
				flex: 1;
				position: relative;

				.login-false-top {
					margin-top: 24rpx;
					margin-left: 218rpx;

					.login-false-avatar {
						@include avatar-position;
					}

					.login-false-text {
						font-family: PingFang SC;
						font-size: 16px;
						font-weight: 500;
						line-height: 22.4px;
						text-align: left;
						color: rgba(21, 21, 21, 0.8);
					}
				}

				.login-false-bottom {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 192rpx;
					height: 58rpx;
					border-radius: 88rpx;
					font-family: Douyin Sans;
					font-size: 16px;
					line-height: 18px;
					text-align: center;
					color: #fff;
					background-color: rgba(255, 146, 18, 1);
					position: absolute;
					bottom: 32rpx;
					left: 50%;
					transform: translateX(-50%);
				}
			}
		}
	}

	.login-card-wrapper {
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	.btns-container {
		flex: 1;
		padding: 32rpx 40rpx 0 38rpx;
		background-color: #fafafa;

		.btns {
			border-radius: 16rpx;
			background-color: rgba(255, 255, 255, 1);

			.btn-item {
				padding: 20rpx 36rpx;
				height: 80rpx;
				box-sizing: border-box;
				margin-top: 8rpx;
				margin-bottom: 8rpx;
				position: relative;
				background-color: #fff;

				&::after {
					border: none;
				}

				.btn {
					display: flex;
					justify-content: space-between;
					align-items: center;
					position: relative;

					.icon {
						width: 32rpx;
						height: 32rpx;
					}

					.btn-left {
						display: flex;
						align-items: center;
						gap: 12rpx;

						font-family: PingFang SC;
						font-size: 14px;
						font-weight: 400;
						line-height: 14px;
						text-align: left;
					}
				}

				.line {
					width: 100%;
					border-top: 0.5px solid rgba(146, 146, 146, 0.2);
					height: 0.5px;
					margin-top: 28rpx;
				}
			}
		}
	}
}

.image {
	width: 313.78rpx;
	height: 238.48rpx;
	top: 122rpx;
	right: -22rpx;
	position: fixed;
}
</style>
