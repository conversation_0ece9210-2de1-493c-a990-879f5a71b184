<template>
	<view class="container" :style="`padding-top: ${navbarInfo?.barHeight}px;`">
		<navbar title="编辑资料" showNavBack :onNavClick="backToPreviousPage"></navbar>
		<img
			:src="showAvatarUrl ? showAvatarUrl : userEditInfo.avatarPic"
			@click="selectImage"
			class="avator round-avator"
		/>
		<view class="edit-area">
			<!-- userNmae -->
			<view class="edit-item">
				<label>昵称</label>
				<view class="item-right-area">
					<input class="enter-input" v-model="userEditInfo.userName" type="text" />
				</view>
			</view>
			<view class="line"></view>
			<!-- gender -->
			<view class="edit-item">
				<label>性别</label>
				<uni-data-picker
					:localdata="genderData"
					popup-title="请选择性别"
					v-slot:default="{ genderData }"
					@nodeclick="onGenderNodeClick"
				>
					<view class="image-area">
						{{ GenderStrMap[userEditInfo.gender] }}
						<img :src="`${imgBaseUrl}manage/manage-icon-leftBtn.png`" class="icon" />
					</view>
				</uni-data-picker>
			</view>
			<view class="line"></view>
			<view class="edit-item">
				<label>职业</label>
				<view class="item-right-area">
					<input class="enter-input" v-model="career" type="text" />
				</view>
			</view>
			<view class="line"></view>
			<!-- 地区 -->
			<view class="edit-item">
				<label>地区</label>
				<uni-data-picker
					:localdata="provinces"
					v-slot:default="{ provinces }"
					popup-title="请选择所在城市"
					@change="onProvinceChange"
					@nodeclick="onProvinceNodeClick"
				>
					<view class="image-area">
						{{ userEditInfo.city }}
						<img :src="`${imgBaseUrl}manage/manage-icon-leftBtn.png`" class="icon" />
					</view>
				</uni-data-picker>
			</view>
		</view>
		<button class="save-btn" @click="onSave">保存</button>
	</view>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { imgBaseUrl, uploadBasePath, BASE_URL } from '../../config'
import { onLoad } from '@dcloudio/uni-app'
import regions from '../register/addVillage/cityList.json'
import manageApi from '../../api/manage'
import { GuardApi } from '../../api/guard'
const store = useStore()
const selectedProvince = ref('')
const selectedCity = ref('')
const showAvatarUrl = ref('')
const avatarFlag = ref(false)
const guardApi = new GuardApi()
const navbarInfo = computed(() => store.getters.navbarInfo)
const career = ref('')
const userEditInfo = ref({
	userName: '',
	gender: 0,
	professional: '',
	city: '',
	avatarPic: ''
})
const GenderStrMap = {
	0: '未选择',
	1: '男',
	2: '女'
}

// eslint-disable-next-line
const provinces = Object.keys(regions).map((province) => ({
	text: province,
	value: province,
	type: 'province',
	children: regions[province].map((city) => ({
		text: city,
		value: city,
		type: 'city'
	}))
}))

// eslint-disable-next-line
const genderData = [
	{ text: '未选择', value: 0 },
	{ text: '男', value: 1 },
	{ text: '女', value: 2 }
]

const backToPreviousPage = () => {
	uni.navigateBack({
		delta: 1
	})
}

const onProvinceChange = () => {
	if (selectedProvince.value && selectedCity.value) {
		userEditInfo.value.city = `${selectedProvince.value} - ${selectedCity.value}`
	}
}
const onProvinceNodeClick = (node) => {
	if (node.type === 'province') {
		selectedProvince.value = node.value
		selectedCity.value = ''
	} else if (node.type === 'city') {
		selectedCity.value = node.value
		onProvinceChange()
	}
}

const onGenderNodeClick = (node) => {
	userEditInfo.value.gender = node.value || 0
}
const selectImage = () => {
	uni.chooseImage({
		count: 1,
		sizeType: ['original', 'compressed'],
		sourceType: ['album', 'camera'],
		success: (res) => {
			showAvatarUrl.value = res.tempFilePaths[0]
			const imgSize = res.tempFiles[0].size
			if (imgSize > 10485760) {
				uni.showToast({
					title: '上传图片需小于10MB',
					icon: 'none'
				})
				showAvatarUrl.value = ''
			} else {
				avatarFlag.value = true
				uni.uploadFile({
					url: `${BASE_URL}${uploadBasePath}avatar`,
					filePath: res.tempFilePaths[0],
					name: 'file',
					header: {
						'Content-Type': 'multipart/form-data',
						Authorization: `Bearer ${uni.getStorageSync('AuthTokens')}`
					},
					success: (res) => {
						try {
							const responseData = JSON.parse(res.data)
							if (responseData?.code) {
								userEditInfo.value.avatarPic = responseData.data?.url || ''
							} else {
								uni.showToast({
									title: '图片上传失败',
									icon: 'none'
								})
							}
						} catch (error) {
							console.error(error)
						}
					}
				})
			}
		},
		fail: (err) => {
			uni.showToast({
				title: err || '图片上传失败，请重试',
				icon: 'none'
			})
		}
	})
}

const onSave = async () => {
	try {
		const requestDate = {
			...userEditInfo.value,
			career: career.value
		}
		const res = await manageApi.editInfo({
			content: JSON.stringify(requestDate)
		})
		if (res.code) {
			if (!userEditInfo.value.avatarPic.startsWith('https://')) {
				userEditInfo.value.avatarPic =
					'https://shuiyuzuiimg.bdnrc.org.cn' + userEditInfo.value.avatarPic
			}

			store.commit('UPDATE_USER_INFO', userEditInfo.value)
			uni.showModal({
				title: '保存成功',
				showCancel: false,

				success: function (res) {
					if (res.confirm) {
						uni.navigateBack()
					}
				}
			})
		}
	} catch (error) {
		console.error(error)
	}
}
const getCareer = async () => {
	try {
		const res = await guardApi.getUserIdCard()
		if (res.code === 1) {
			career.value = res.data.career
		}
	} catch (error) {
		console.error(error)
	}
}
onMounted(() => {
	getCareer()
})
onLoad(() => {
	const { userName, gender, city, avatarPic } = store.getters.userInfo
	userEditInfo.value = { userName, gender, city, avatarPic }
})
</script>
<style scoped lang="scss">
.common-info {
	font-family: PingFang SC;
	font-size: 14px;
	font-weight: 400;
	line-height: 14px;
	text-align: left;
	color: rgba(21, 21, 21, 0.6) !important;
}
.common-title {
	font-family: PingFang SC;
	font-size: 14px;
	font-weight: 400;
	line-height: 14px;
	text-align: left;
	color: rgba(21, 21, 21, 1);
}

.container {
	width: 100vw;
	height: 100vh;
	background-color: #fafafa;
	box-sizing: border-box;
	position: relative;
	.avator {
		width: 164rpx;
		height: 164rpx;
		margin: 52rpx auto 80rpx;
		display: block;
	}
	.round-avator {
		border-radius: 50%;
	}

	.edit-area {
		width: 100%;
		box-sizing: border-box;
		margin-top: 32rpx;
		padding: 0 32rpx;
		background-color: rgba(255, 255, 255, 1);
		background-clip: content-box;
		border-radius: 8px;
		.edit-item {
			@extend .common-title;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 80rpx;
			box-sizing: border-box;
			padding: 0 18px;

			.item-right-area {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 8px;
				.enter-input {
					@extend .common-info;
					width: fit-content;
					color: black;
					background-color: white;
					text-align: right;
				}
				.icon {
					width: 32rpx;
					height: 32rpx;
				}
			}

			&:deep(.uni-date) {
				text-align: right;
				width: fit-content;
				flex: none;
				.date-area {
					@extend .common-info;
					width: fit-content;
					display: flex;
					gap: 8px;
					align-items: center;
					justify-content: flex-end;
					.icon {
						height: 32rpx;
						width: 32rpx;
					}
				}
			}

			&:deep(.uni-data-tree-input) {
				.image-area {
					@extend .common-info;
					height: 100%;
					display: flex;
					align-items: center;
					gap: 8px;
					.icon {
						height: 32rpx;
						width: 32rpx;
					}
				}
			}
		}

		.line {
			width: calc(100% - 36px);
			box-sizing: border-box;
			border-top: 0.5px solid rgba(146, 146, 146, 0.2);
			height: 0.5px;
			margin: 4px 18px;
		}
	}

	.save-btn {
		width: calc(100% - 64rpx);
		box-sizing: border-box;
		margin: 0 32rpx;
		position: absolute;
		bottom: 156rpx;
	}
}
</style>
