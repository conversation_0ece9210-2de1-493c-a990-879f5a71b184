<template>
	<view class="page-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar title="关于我们" show-nav-back :on-nav-click="backToPreviousPage"></navbar>
		<view class="content">
			<view class="top-area">
				<view class="about-logo">
					<image :src="`${imgBaseUrl}about-us-logo.png`" class="about-logo-img"></image>
					<text>版本 v{{ version }}</text>
				</view>
				<view class="about-nav">
					<view class="about-nav-item" @click="toPage('aboutmini')">
						<text class="about-nav-item-text">关于小程序</text>
						<image :src="`${imgBaseUrl}nav/nav_arrow.svg`" class="about-nav-arrow"></image>
					</view>
					<view class="about-nav-item" @click="toPage('aboutterms')">
						<text class="about-nav-item-text">服务条款</text>
						<image :src="`${imgBaseUrl}nav/nav_arrow.svg`" class="about-nav-arrow"></image>
					</view>
					<view class="about-nav-item" @click="toPage('aboutprivacy')">
						<text class="about-nav-item-text">隐私政策</text>
						<image :src="`${imgBaseUrl}nav/nav_arrow.svg`" class="about-nav-arrow"></image>
					</view>
				</view>
			</view>
			<view class="bottom-area">
				<text>ICP备案号：京ICP备2024087428号-2X</text>
				<text>copyright@北京数原数字化城市研究中心</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import { imgBaseUrl } from '../../config'

const store = useStore()

const version = ref('1.1.0')
const navbarInfo = computed(() => store.getters.navbarInfo)

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}

function toPage(url: string) {
	uni.navigateTo({
		url: `/pages/manage/${url}`
	})
}

onMounted(() => {
	const accountInfo = uni.getAccountInfoSync() || {}
	version.value = (accountInfo.miniProgram || {}).version || '1.1.0'
})
</script>

<style scoped>
page {
	background-color: #fff;
	height: 100%;
}
.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
	background: #fafafa;
}
.content {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;
	width: 100%;
	padding: 16px 16px 65px 16px;
	box-sizing: border-box;
}

.top-area {
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: #fff;
	border-radius: 8px;
	padding: 0 16.5px;
}

.about-logo {
	display: flex;
	flex-direction: column;
	align-items: center;
	font-size: 10px;
	line-height: 14px;
	margin-top: 16px;
	margin-bottom: 28px;
}

.about-logo-img {
	width: 220rpx;
	height: 220rpx;
}

.about-nav {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
}

.about-nav-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	font-size: 14px;
	padding: 16px 0;
}

.about-nav-item:not(:last-child) {
	border-bottom: 0.5px solid #92929233;
}

.about-nav-item-text {
	margin-left: 1.5px;
}

.about-nav-arrow {
	width: 16px;
	height: 16px;
}

.bottom-area {
	display: flex;
	flex-direction: column;
	align-items: center;
	font-size: 10px;
	line-height: 14px;
}
</style>
