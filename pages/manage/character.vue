<template>
	<landing v-if="sharePageFlag"></landing>
	<view
		v-else
		class="container"
		:style="{
			paddingTop: `${navbarInfo.barheight}px`
		}"
	>
		<navbar
			showNavBack
			:onNavClick="backToPreviousPage"
			:navBackIcon="`${imgBaseUrl}` + 'nav/nav-bar-backWhite-icon.png'"
		></navbar>
		<generate-card :navbarInfo="navbarInfo" :userInfo="userInfo" />
		<SharePopup ref="sharePopup"></SharePopup>
	</view>
</template>
<script setup>
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import navbar from '../../components/navbar/navbar.vue'
import { GuardApi } from '../../api/guard'
import dayjs from 'dayjs'
import SharePopup from '../../components/share-popup/share-popup.vue'
import { imgBaseUrl } from '../../config'

const guardApi = new GuardApi()
const store = useStore()
const sharePageFlag = computed(() => store.getters.sharePageFlag)

const navbarInfo = computed(() => store.getters.navbarInfo)
const userInfo = ref({})
const sharePopup = ref(null)

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}

// Get User Info Function
const getUserInfo = async () => {
	const res = await guardApi.getUserIdCard()
	if (res.code === 1) {
		userInfo.value = res.data
		userInfo.value.registerTime = dayjs(res.data.registerTime).format('YYYY年MM月DD日')
	}
}

onShareAppMessage(async () => {
	sharePopup.value.openPopup()
	await guardApi.getGuardShare()
})

onShareTimeline(async () => {
	sharePopup.value.openPopup()
	await guardApi.getGuardShare()
	return {
		title: '京西水峪嘴云村民'
	}
})

onLoad(async () => {
	if (!sharePageFlag.value) {
		await getUserInfo()
	}
})
</script>
<style scoped lang="scss">
.container {
	width: 100vw;
	height: 100vh;
	background-size: contain;
}
</style>
