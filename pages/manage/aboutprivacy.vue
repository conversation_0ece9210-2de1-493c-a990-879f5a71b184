<template>
	<view class="page-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar title="隐私政策" showNavBack :onNavClick="backToPreviousPage"></navbar>
		<view class="content">
			<scroll-view scroll-y class="scroll-view">
				<view class="content-title">京西水峪嘴云村民小程序隐私保护指引</view>
				<view class="content-texts">
					<view class="content-normal">
						本指引是京西水峪嘴云村民小程序开发者
						北京数原数字化城市研究中心（以下简称“开发者”）为处理你的个人信息而制定。
					</view>
					<view class="content-text">
						<b class="content-normal-bold">1. 开发者处理的信息</b>
						<view class="content-normal"
							>根据法律规定，开发者仅处理实现小程序功能所必要的信息。
							为了在线下实地发布蹄窝守护动态并增加守护值，开发者将在获取你的明示同意后，收集你的位置信息。
							开发者 收集你选中的照片或视频信息，用于编辑守护动态时上传相册图片。
							为了编辑守护动态时直接拍照，开发者将在获取你的明示同意后，访问你的摄像头。 开发者
							收集你的发布内容，用于优化守护动态用户体验。
							为了保存蹄窝守护卡片和保存村民证等，开发者将在获取你的明示同意后，使用你的相册（仅写入）权限。
						</view>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">2. 第三方插件信息/SDK信息</b>
						<view class="content-normal">
							为实现特定功能，开发者可能会接入由第三方提供的插件/SDK。第三方插件/SDK的个人信息处理规则，请以其公示的官方说明为准。京西云村小程序接入的第三方插件信息/SDK信息如下：
						</view>
						<view class="content-normal">
							2.1
							关于你的个人信息，你可以通过以下方式与开发者联系，行使查阅、复制、更正、删除等法定权利。
						</view>
						<view class="content-normal">
							2.2
							若你在小程序中注册了账号，你可以通过以下方式与开发者联系，申请注销你在小程序中使用的账号。在受理你的申请后，开发者承诺在十五个工作日内完成核查和处理，并按照法律法规要求处理你的相关信息。
							邮箱: <EMAIL>
						</view>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">3. 你的权益</b>
						<view class="content-normal">
							关于你的个人信息，你可以通过以下方式与开发者联系，行使查阅、复制、更正、删除等法定权利。
						</view>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">4. 开发者对信息的存储</b>
						<view class="content-normal"> 4.1 开发者将会在本指引所明示的用途内使用收集的信息 </view>
						<view class="content-normal">
							4.2如开发者使用你的信息超出本指引目的或合理范围，开发者必须在变更使用目的或范围前，再次以小程序内公告方式告知并征得你的明示同意。
						</view>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">5. 信息的使用规则</b>
						<view class="content-normal"> 5.1 开发者将会在本指引所明示的用途内使用收集的信息 </view>
						<view class="content-normal">
							5.2
							开发者承诺，不会对外公开披露你的信息，如必须公开披露时，开发者应当向你告知公开披露的目的、披露信息的类型及可能涉及的信息，并征得你的单独同意。
						</view>
					</view>
					<view class="content-text">
						<b class="content-normal-bold">6. 信息对外提供</b>
						<view class="content-normal"> 邮箱 : <EMAIL> </view>
					</view>
					<view class="content-normal">
						本小程序已对用户的信息处理进行了逐一、如实的说明，并保证如有变更会及时更新指引。
					</view>
					<view class="content-normal"> 生效日期：2024-10-09 </view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()

const navbarInfo = computed(() => store.getters.navbarInfo)

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}
</script>

<style scoped>
.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
	background: #fafafa;
}
.content {
	display: flex;
	flex-direction: column;
	height: 100vh;
	width: 100%;
	padding: 24px 24px 94px 24px;
	box-sizing: border-box;
}
.scroll-view {
	height: 100%;
}
.content-title {
	font-size: 18px;
	font-weight: 500;
	line-height: 25.2px;
	display: flex;
	justify-content: center;
	margin-bottom: 24px;
}
.content-texts {
	display: flex;
	flex-direction: column;
	text-indent: 2em;
	gap: 16px;
}
.content-normal {
	font-size: 14px;
	font-weight: 400;
	line-height: 16.8px;
	color: rgba(21, 21, 21, 0.8);
}
.content-normal-bold {
	font-weight: 600;
}
.content-text {
	display: flex;
	flex-direction: column;
	gap: 8px;
}
</style>
