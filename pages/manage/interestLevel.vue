<template>
	<view
		class="container"
		:style="`padding-top: ${navbarInfo.barHeight}px; height: calc(100vh - ${navbarInfo.barHeight}px);`"
	>
		<navbar title="等级权益" showNavBack :onNavClick="backToPreviousPage"></navbar>
		<swiper class="swiper-container" circular>
			<swiper-item v-for="(item, index) in levelConfig" :key="index" class="swiper-item">
				<view class="swiper-item-content" :style="{ backgroundImage: `url(${item.url})` }">
					<view class="progress-area">
						<progress
							:percent="
								userInfo.level != item.level
									? 1 * 100
									: (userInfo.points / userInfo.nextLevelPoints) * 100
							"
							stroke-width="5"
							:activeColor="item.color"
							backgroundColor="#fff"
						/>
						<view
							class="progress-text"
							:style="{ color: item.color }"
							v-if="userInfo.level != item.level"
						>
							<view class="text-1">你当前的等级为LV{{ userInfo.level }}</view>
							<view class="text-2">守护值{{ userInfo.points }}</view>
						</view>
						<view class="progress-text" :style="{ color: item.color }" v-else>
							<view class="text-1">守护值{{ userInfo.points }}/{{ userInfo.nextLevelPoints }}</view>
							<view class="text-2" v-if="item.level != 5"
								>还差{{ userInfo.nextLevelPoints - userInfo.points }}分升级至{{
									levelConfig[item.level]?.levelName
								}}</view
							>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>
<script setup>
import navbar from '../../components/navbar/navbar.vue'
import { computed } from 'vue'
import { useStore } from 'vuex'
import { levelConfig } from './configInfo'

const store = useStore()
const navbarInfo = computed(() => store.getters.navbarInfo)
const userInfo = computed(() => store.state.userInfo)

const backToPreviousPage = () => {
	uni.navigateBack({
		delta: 1
	})
}
</script>
<style scoped lang="scss">
.container {
	width: 100vw;
	.swiper-container {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;

		.swiper-item {
			&:last-child {
				overflow-y: auto;
				&::-webkit-scrollbar {
					display: none;
				}
			}
			.swiper-item-content {
				background-size: contain;
				&:last-child {
					min-height: 1910rpx;
					overflow-y: auto;
				}
				.progress-area {
					position: absolute;
					top: 926rpx;
					width: 602rpx;
					left: 50%;
					transform: translateX(-50%);
					.progress-text {
						margin-top: 16rpx;
						font-family: PingFang SC;
						font-size: 12px;
						font-weight: 400;
						line-height: 16.8px;
						text-align: left;
						display: flex;
						align-items: center;
						justify-content: space-between;
					}
				}
			}
		}
	}
}
</style>
