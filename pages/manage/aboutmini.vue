<template>
	<view class="page-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar title="关于小程序" showNavBack :onNavClick="backToPreviousPage"></navbar>
		<view class="content">
			<view class="top-area">
				<text class="top-text"
					>京西古道距今有数千年的历史，具有商道、军道、香道等功用，其以“西山大路”为主干线，组成了一张纵横门头沟全境的路网。位于水峪嘴村的西山大路北道牛角岭-韭园段是目前保存最为完好的古道线路，在牛角岭关城处有大小蹄窝300余个，从关城沿蜿蜒西行，便能看到马致远故居，“古道西风瘦马”说的正是这一段古道。水峪嘴村依托京西古道建成了3A级的古道景区和古道人民公社，走出了旅游观光兴村的路子。</text
				>
				<text class="top-text"
					>“京西水峪嘴云村民”小程序即以对上述历史文化、地貌遗迹的保护为宗旨，通过数字化手段实现文化遗产持久化保存的同时，连接城市用户与乡村资源，实现线上流量向村庄留量的转化，促进水峪嘴村的可持续发展和文化传承。</text
				>
				<text class="top-text"
					>本小程序由北京数原数字化城市研究中心（BDNRC，数原中心）开发。数原中心于2021年在北京市委市政府的指导和支持下，经北京市科学技术委员会、中关村科技园区管理委员会和海淀区人民政府共同推动而成立，汇聚国内外顶尖名校人才，以“共创触手可及的数字城市”为使命，在城市感知、城市元宇宙、多模态空间大模型方向具有业界领先的研发能力和场景落地经验。</text
				>
			</view>
			<view class="bottom-area">
				<text>联系景区：<EMAIL> &nbsp; 010-61880104</text>
				<text>联系数原：<EMAIL> &nbsp;&nbsp;&nbsp;&nbsp; 010-56683770</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()

const navbarInfo = computed(() => store.getters.navbarInfo)

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}
</script>

<style scoped>
page {
	background-color: #fff;
	height: 100%;
}
.page-container {
	display: flex;
	height: 100vh;
	overflow: hidden;
	flex-direction: column;
	box-sizing: border-box;
	background: #fafafa;
}
.content {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;
	width: 100%;
	padding: 16px 29px 65px 29px;
	box-sizing: border-box;
}

.top-area {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.top-text {
	font-size: 14px;
	line-height: 19.6px;
	color: #151515cc;
	text-indent: 2em;
}

.bottom-area {
	display: flex;
	flex-direction: column;
	align-items: center;
	font-size: 10px;
	line-height: 14px;
}
</style>
