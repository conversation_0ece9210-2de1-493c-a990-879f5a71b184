<template>
	<view class="container">
		<web-view :src="webPageUrl"></web-view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { baseMapUrl } from '../../config'

const webPageUrl = ref('')

onLoad((options) => {
	const modelsName = options.splatUrl || ''
	const token = options.authToken || ''
	webPageUrl.value = baseMapUrl + modelsName + '&authToken=' + token
})
</script>
