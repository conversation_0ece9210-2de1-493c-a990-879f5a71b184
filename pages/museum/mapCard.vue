<template>
	<view class="spot-img-container" :style="computedSpotStyle()">
		<view class="spot-btn" :style="computedSpotBtnStyle()" @click="handleGetScene"></view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { findCommonUrl } from '../../utils/count'

const props = defineProps({
	spotInfo: {
		type: Object,
		default: () => ({})
	},
	mapName: {
		typeof: String,
		required: false
	}
})

const showCard = ref(false)
const emit = defineEmits(['emitSceneInfo'])

const computedSpotStyle = () => {
	if (props.spotInfo.code === props.mapName) {
		const { top, left } = props.spotInfo.position3d
		const { width, height } = props.spotInfo.size3d
		return {
			width: `${width}rpx`,
			height: `${height}rpx`,
			top: `${top}vh`,
			left: `${left}vw`
		}
	} else {
		const { top, left } = props.spotInfo.position
		const { width, height } = props.spotInfo.btnsize
		return {
			width: `${width}rpx`,
			height: `${height}rpx`,
			top: `${top}vh`,
			left: `${left}vw`
		}
	}
}
const handleGetScene = () => {
	if (props.mapName) {
		const { models } = props.spotInfo
		const spotUrl = findCommonUrl(models[0], models[1])
		uni.navigateTo({
			url:
				'/pages/museum/museumScene?splatUrl=' +
				encodeURIComponent(spotUrl) +
				'&mapName=' +
				encodeURIComponent(props.mapName)
		})
	} else {
		showCard.value = true
		const message = {
			showCard: showCard.value,
			clickedSceneCode: props.spotInfo.code,
			clickedScene: props.spotInfo
		}
		emit('emitSceneInfo', message)
	}
}
const computedSpotBtnStyle = () => {
	if (props.spotInfo.code === props.mapName) {
		const { width, height } = props.spotInfo?.size3d || {}
		return {
			width: `${width}rpx`,
			height: `${height}rpx`,
			backgroundImage: `url(${props.spotInfo?.map})`,
			backgroundRepeat: 'no-repeat'
		}
	} else {
		const { width, height } = props.spotInfo?.btnsize || {}
		return {
			width: `${width}rpx`,
			height: `${height}rpx`,
			backgroundImage: `url(${props.spotInfo?.icon})`,
			backgroundRepeat: 'no-repeat'
		}
	}
}
</script>

<style scoped lang="scss">
.spot-img-container {
	position: absolute;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	.spot-btn {
		position: absolute;
		bottom: 3rpx;
		background-size: cover;
		background-repeat: no-repeat;
	}
}
</style>
