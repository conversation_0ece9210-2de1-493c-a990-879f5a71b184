<template>
	<view class="museum-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar title="云博物地图" class="navbar" showNavBack :onNavClick="backToPreviousPage"></navbar>
		<view class="container">
			<image
				:src="`${imgBaseUrl}museum/cloud/museum-left-animation.png`"
				class="container-left-cloud"
			></image>
			<image
				:src="`${imgBaseUrl}museum/cloud/museum-right-animation.png`"
				class="container-right-cloud"
				:style="`top: ${navbarInfo.barHeight}px;`"
			></image>
		</view>
		<mapCard
			v-for="item in scenicData"
			:key="item.id"
			:spotInfo="item"
			@emitSceneInfo="clickedSceneInfo"
			@click="handleGetScene"
			:mapName="mapName"
		></mapCard>
	</view>
</template>
<script setup>
import { ref, computed } from 'vue'
import { imgBaseUrl } from '../../config'
import { useStore } from 'vuex'
import mapCard from './mapCard.vue'
import spotPositions from './spot_data'
import { onLoad } from '@dcloudio/uni-app'
import mapApi from '../../api/map'
const mapName = ref('')
const cardCode = ref('')
const showCard = ref(false)
const scenicData = ref([])
const store = useStore()
const navbarInfo = computed(() => store.getters.navbarInfo)
const clickedSceneInfo = (message) => {
	showCard.value = message.showCard
	cardCode.value = message.clickedSceneCode
}

function backToPreviousPage() {
	uni.navigateBack({
		delta: 1
	})
}
const getPositionData = async () => {
	try {
		const res = await mapApi.getMuseumPoi()
		scenicData.value = res.data.map((item) => {
			return {
				...item,
				code: spotPositions.find((spot) => spot.code == item.code)?.code,
				size3d: spotPositions.find((spot) => spot.code == item.code)?.size3d,
				btnsize: spotPositions.find((spot) => spot.code == item.code)?.btnsize,
				position: spotPositions.find((spot) => spot.code == item.code)?.position,
				position3d: spotPositions.find((spot) => spot.code == item.code)?.position3d
			}
		})
	} catch (error) {
		console.error('error', error)
	}
}
onLoad(async (options) => {
	mapName.value = options.sceneCode || ''
	getPositionData()
})
</script>

<style scoped lang="scss">
.museum-container {
	width: 100vw;
	height: 100vh;
	overflow: hidden;
	box-sizing: border-box;
}
.container {
	width: 100%;
	height: 100%;
	background-image: url(http://**************:39090/virtual-village-mini-static/museum/cloud/museum-background.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	overflow: hidden;
	background-color: #c0a16b;
}
.container-left-cloud {
	width: 346rpx;
	height: 326rpx;
	position: absolute;
	bottom: 0px;
}
.container-right-cloud {
	width: 220rpx;
	height: 176rpx;
	position: absolute;
	right: 0px;
}

.scene-container {
	position: relative;
	bottom: 162px;
}
</style>
