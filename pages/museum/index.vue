<template>
	<view class="museum-container">
		<navbar title="云博物" class="navbar"></navbar>
		<view class="container-bg-img">
			<view class="container-bg" :class="{ animate: goToMuseum }">
				<image
					:src="`${imgBaseUrl}museum/cloud/museum-background.png`"
					class="container-bg-img"
				></image>
			</view>
			<view class="cloud-group" :class="{ animate: goToMuseum }">
				<image
					:src="`${imgBaseUrl}museum/cloud/museum-left-cloud.png`"
					class="container-left-cloud-one"
				></image>
				<image
					:src="`${imgBaseUrl}museum/cloud/museum-right-cloud.png`"
					class="container-right-cloud-one"
				></image>
			</view>

			<view v-show="goToMuseum" class="cloud-group-two" :class="{ animate: goToMuseum }">
				<image
					:src="`${imgBaseUrl}museum/cloud/museum-left-animation.png`"
					class="container-left-cloud-two"
				></image>
				<image
					:src="`${imgBaseUrl}museum/cloud/museum-right-animation.png`"
					class="container-right-cloud-two"
					:style="`top: ${navbarInfo.barHeight}px;`"
				></image>
			</view>
			<view v-show="!goToMuseum" class="container-btn">
				<button class="btn container-btn-btn" @click="startAnimation">
					<text class="container-btn-text">进入博物馆</text>
				</button>
			</view>
		</view>
		<mapCard
			v-for="item in scenicData"
			v-show="goToMuseum"
			:key="item.id"
			:spot-info="item"
			@emit-scene-info="clickedSceneInfo"
		></mapCard>
		<view v-show="firstShowMask" class="mask">
			<view
				class="mask-text"
				:style="{ backgroundImage: `url(${imgBaseUrl}museum/cloud/museum-message-icon.png)` }"
			>
				<view class="mask-text-container">
					<view class="mask-text-one">欢迎来到古道博物馆</view>
					<view class="mask-text-two">您可以在线浏览真实馆内场景~</view>
				</view>
			</view>
			<view class="mask-btn">
				<button class="btn mask-btn-btn" @click="firstShowMask = false">
					<text class="container-btn-text">知道了</text>
				</button>
			</view>
			<view class="mask-ip">
				<image :src="`${imgBaseUrl}museum/cloud/museum-ip-icon.png`" class="mask-ip-img"></image>
			</view>
		</view>
		<view v-show="showCard" class="scene-container">
			<scene-card
				:current-scenic-data="currentScenicData"
				@handle-close="cancelEvent"
				@handle-enter-scene="handleEnterScene"
			/>
		</view>
		<tabbar-shadow></tabbar-shadow>
	</view>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue'
import { imgBaseUrl } from '../../config'
import { useStore } from 'vuex'
import spotPositions from './spot_data'
import mapCard from './mapCard.vue'
import sceneCard from '@/components/scene-card/scene-card.vue'
import mapApi from '../../api/map'
import { handleGuestMode } from '@/utils/common'
import { findCommonUrl } from '@/utils/count'

const scenicData = ref([])
const currentScenicData = ref()
const showCard = ref(false)
const firstShowMask = ref(false)
const mapCount = computed(() => store.state.mapCount)
const store = useStore()
const navbarInfo = computed(() => store.getters.navbarInfo)
const goToMuseum = ref(false)
const startAnimation = handleGuestMode(() => {
	goToMuseum.value = true
	if (mapCount.value === 0) {
		setTimeout(() => {
			firstShowMask.value = true
		}, 300)
	}
	let newMapCount = store.state.mapCount + 1
	store.dispatch('setMapCount', newMapCount)
	uni.setStorageSync('mapCount', newMapCount)
})
const clickedSceneInfo = (message) => {
	showCard.value = message.showCard
	currentScenicData.value = message.clickedScene
}
const cancelEvent = (message) => {
	if (message) {
		showCard.value = false
	}
}
const getPositionData = async () => {
	try {
		const res = await mapApi.getMuseumPoi()
		scenicData.value = res.data.map((item) => {
			return {
				...item,
				...(spotPositions.find((spot) => spot.code == item.code) || {})
			}
		})
	} catch (error) {
		console.error('error', error)
	}
}

const handleEnterScene = (sceneData) => {
	const { models, code } = sceneData
	const spotUrl = findCommonUrl(models[0], models[1])
	uni.navigateTo({
		url:
			'/pages/museum/museumScene?splatUrl=' +
			encodeURIComponent(spotUrl) +
			'&mapName=' +
			encodeURIComponent(code) +
			'&authToken=' +
			uni.getStorageSync('AuthTokens')
	})
}

onMounted(() => {
	goToMuseum.value = false
	getPositionData()
})
</script>

<style scoped lang="scss">
@keyframes slideOutLeft {
	from {
		transform: translateX(0);
		opacity: 1;
	}
	to {
		transform: translateX(-100vw);
		opacity: 0;
	}
}

@keyframes slideInLeft {
	from {
		transform: translateX(-100vw);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}
@keyframes slideOutRight {
	from {
		transform: translateX(0);
		opacity: 1;
	}
	to {
		transform: translateX(100vw);
		opacity: 0;
	}
}
@keyframes slideInRight {
	from {
		transform: translateX(100vw);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}
@keyframes enlarge {
	from {
		transform: scale(1.62);
		transform-origin: bottom;
	}
	to {
		transform: scale(1);
		transform-origin: center;
	}
}
::v-deep .navbar .navbar {
	background: linear-gradient(
		180deg,
		rgba(192, 161, 107, 0.86) 50%,
		rgba(192, 161, 107, 0) 100%
	) !important;
}
.museum-container {
	width: 100vw;
	height: 100vh;
	overflow: hidden;
	background: #c0a16b;
}
.container-bg {
	width: 100%;
	height: 100%;
	overflow: hidden;
	transform: scale(1.62);
	transform-origin: bottom;
	&.animate {
		animation: enlarge 300ms forwards;
	}
}
.container-bg-img {
	width: 100%;
	height: 100%;
	overflow: hidden;
}
.cloud-group {
	.container-left-cloud-one {
		width: 376rpx;
		height: 526rpx;
		position: absolute;
		bottom: 0px;
	}
	.container-right-cloud-one {
		width: 376rpx;
		height: 526rpx;
		position: absolute;
		bottom: 0px;
		right: 0px;
	}
	&.animate {
		.container-left-cloud-one {
			animation: slideOutLeft 300ms forwards;
		}
		.container-right-cloud-one {
			animation: slideOutRight 300ms forwards;
		}
	}
}
.cloud-group-two {
	.container-left-cloud-two {
		width: 264rpx;
		height: 248rpx;
		position: absolute;
		bottom: 0px;
	}
	.container-right-cloud-two {
		width: 220rpx;
		height: 176rpx;
		position: absolute;
		right: 0px;
	}
	&.animate {
		.container-left-cloud-two {
			animation: slideInLeft 300ms forwards;
		}
		.container-right-cloud-two {
			animation: slideInRight 300ms forwards;
		}
	}
}
.container-btn {
	position: absolute;
	bottom: 11px;
	left: 50%;
	transform: translateX(-50%);
}
.btn {
	height: 36px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 44px;
	background: linear-gradient(0deg, #ff9212 42.09%, #ffb763 101.14%);
	border: 1px solid;
	border-image-source: linear-gradient(
		93.14deg,
		rgba(255, 255, 255, 0.316) 9.18%,
		rgba(255, 255, 255, 0) 51.87%,
		rgba(255, 255, 255, 0.276) 89.73%
	);
	box-shadow: 0px 3px 4px 0px #8a4b0033;
}
.container-btn-btn {
	padding: 0 12px;
}
.container-btn-text {
	font-family: Douyin Sans;
	font-size: 17px;
	font-weight: 500;
	line-height: 18px;
	color: #ffffff;
}
.mask {
	position: fixed;
	z-index: 1000;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	transition: opacity 0.3s;
	background: #00000066;
}
.mask-text {
	position: absolute;
	bottom: 131px;
	left: 23px;
	width: 440rpx;
	height: 176rpx;
	background-size: cover;
}
.mask-ip-img {
	width: 240rpx;
	height: 450rpx;
	position: absolute;
	bottom: 0px;
	right: 0px;
}
.mask-text-container {
	font-size: 12px;
	font-weight: 500;
	line-height: 20px;
	z-index: 2;
	position: absolute;
	left: 56rpx;
	top: 40rpx;
}
.mask-text-one {
	color: #151515;
}
.mask-text-two {
	color: #a7560f;
}
.mask-btn {
	position: absolute;
	bottom: 23px;
	left: 50%;
	transform: translateX(-50%);
}
.mask-btn-btn {
	width: 109px;
}
</style>
