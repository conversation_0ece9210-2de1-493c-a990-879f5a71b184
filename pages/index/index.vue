<template>
	<landing v-if="sharePageFlag"></landing>
	<view v-else class="page-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<image :src="`${imgBaseUrl}home/home-header-bg.png`" class="container-bg"></image>
		<navbar title="水峪嘴村云村民" :opacity="navOpacity" :ele-color="navEleColor"></navbar>
		<view class="home-container">
			<!-- card -->
			<view v-if="!userRegistered" class="guest-user-card">
				<guestHomeCard />
			</view>
			<view
				v-else
				class="user-card"
				:style="{
					filter: isClaimFlag ? 'drop-shadow(0px 0px 5px rgba(0, 0, 0, 0.06))' : 'none',

					width: isClaimFlag ? '100%' : '100vw',
					transform: !isClaimFlag ? 'translateX(-18px)' : 'none'
				}"
			>
				<view v-if="!isClaimFlag" class="claim-area">
					<img :src="`${imgBaseUrl}home/home-card-claim-bg.png`" class="claim-img-bc" />
					<button class="claim-btn" @click="onClaim">点击认领</button>
				</view>
				<homeCard v-else :card-data="cardData" @show-guard-card="showGuardCard"></homeCard>
			</view>
			<!-- nav -->
			<view class="nav-container">
				<view
					v-for="navItem in navItems"
					:key="navItem.text"
					class="nav-item"
					@click="onSkipPage(navItem.skipUrl)"
				>
					<image :src="navItem.iconUrl" class="nav-item-icon" mode="aspectFit" />
					<text class="nav-item-text">{{ navItem.text }}</text>
				</view>
			</view>
			<view class="community-entry">
				<homeSwiper :swiper-data="swiperData"></homeSwiper>
			</view>
			<view class="record-list">
				<view class="record-list-title">
					<image
						:src="`${imgBaseUrl}guard/guard-records-top10-title.png`"
						class="record-list-title-png"
					></image>
					<view class="record-list-title-text">
						<text class="record-list-title-left">守护动态</text>
						<image
							class="record-list-title-underline"
							:src="`${imgBaseUrl}index/index-guard-img.png`"
						></image>
						<view class="record-list-title-right" @click="onRecordsNav">
							<text class="record-list-title-more">查看更多</text>
							<image
								class="recor-list-title-icon"
								:src="`${imgBaseUrl}guard/guard-records-left.png`"
							></image>
						</view>
					</view>
				</view>
				<view v-for="record in trendsRes" :key="record.id">
					<RecordCard
						:record="record"
						:left-width="leftWidth"
						:margin-class="marginClass"
						:user-login="userLogin"
						:go-to-login="goToLogin"
						:name="name"
						:record-line="true"
						@record-deleted="refreshRecords"
						@init-trends="getTrendsTop"
					></RecordCard>
				</view>
			</view>
		</view>
		<!-- claim -->
		<uni-popup
			ref="claimPopup"
			background-color="#fff"
			:safe-area="true"
			:is-mask-click="false"
			mask-background-color="rgba(0, 0, 0, 0.8)"
			class="claim-popup"
		>
			<!-- claim-title -->
			<view class="claim-title"> <text class="claim-title-green"> 感谢 </text>您的支持！ </view>
			<!-- claim-text -->
			<img :src="`${imgBaseUrl}home/home-claim-line.png`" class="line" />
			<view class="claim-text">
				<text class="claim-text-first">
					您是第
					<text class="claim-text-number">{{ claimData.guardMember }}</text>
					位编号
				</text>
				<text class="claim-text-first"> {{ claimData.hoofprintCode }}蹄窝的守护者 </text>
			</view>
			<img :src="`${imgBaseUrl}home/home-claim-line.png`" class="line" />

			<detailsCard :claim-data="claimData" :is-claim-skip-flag="true"></detailsCard>

			<view class="claim-btn">
				<button class="claim-btn-item" @click="onShare">分享</button>
				<button class="claim-btn-item" @click="onEntryGuard">进入守护</button>
			</view>
		</uni-popup>
		<!--  -->
		<uni-popup
			ref="groupDialogRef"
			class="group-dialog"
			type="center"
			:animation="false"
			:safe-area="true"
			mask-background-color="rgba(0, 0, 0, 0.8)"
			@mask-click="closeGroupDialog"
		>
			<view class="group-dialog-container">
				<view class="group-dialog-content">
					<!-- group-top -->
					<view class="group-dialog-top">
						<img :src="`${imgBaseUrl}home/home-group-bc.png`" class="group-dialog-bc" />
						<img :src="`${imgBaseUrl}home/home-group-people.png`" class="group-dialog-pepole" />
					</view>
					<!-- group-code -->
					<view class="group-dialog-code-container">
						<img
							:src="`${imgBaseUrl}home/home-group-code.png`"
							class="group-dialog-code"
							show-menu-by-longpress="true"
							mode="aspectFill"
						/>
					</view>

					<!-- group-bottom -->
					<view class="group-dialog-bottom">
						<text>长按扫码进群</text>
						<text>获取更多古道游玩资讯与福利</text>
					</view>
				</view>

				<img
					:src="`${imgBaseUrl}home/home-group-icon-close.png`"
					class="group-icon-close"
					@click="closeGroupDialog"
				/>
			</view>
		</uni-popup>

		<textPopop
			ref="loginPopup"
			title="请先登录"
			content="登录之后才可以体验完整功能"
			cancel-text="暂不登录"
			sumbit-text="立即登录"
			:is-customizable="true"
			:style-data="{
				bcUrl: `${imgBaseUrl}login-popup-bc.png`,
				width: '594rpx',
				height: '356rpx'
			}"
			:submit-function="goLogin"
		></textPopop>
		<GuardCard
			v-if="showDialog"
			:dialog="dialog"
			:img-url="imgUrl"
			@update:dialog="updateDialog"
		></GuardCard>
		<SharePopup ref="sharePopup"></SharePopup>
		<JoinPopup ref="joinPopup" :invite-count="inviteCount"></JoinPopup>
		<!-- <image
			class="chat-enter"
			:src="`${imgBaseUrl}chat/chat-luoxiaoxi.png`"
			mode="scaleToFill"
			@click="handleToChat"
		/> -->
		<tabbar-shadow></tabbar-shadow>
	</view>
</template>

<script setup>
// package api
import {
	onLoad,
	onShow,
	onHide,
	onPageScroll,
	onShareTimeline,
	onShareAppMessage
} from '@dcloudio/uni-app'
import { ref, computed, reactive } from 'vue'
import { useStore } from 'vuex'
import { RecordCard } from '../guard/component/RecordCard.vue'
import { GuardApi } from '../../api/guard.js'
import homeCard from './home-card/home-card.vue'
import guestHomeCard from './home-card/guest-home-card.vue'
import homeSwiper from './home-swiper/home-swiper.vue'
import detailsCard from '../../components/details-card/details-card.vue'
import { IndexApi } from '../../api/index.js'
import { imgBaseUrl } from '../../config'
import { UserApi } from '../../api/user.js'
import textPopop from '../../components/text-popup/text-popop.vue'
import SharePopup from '../../components/share-popup/share-popup.vue'
import { GuardCard } from '../guard/component/GuardCard.vue'
import JoinPopup from '../../components/join-popup/join-popup.vue'
import { navItems } from './config.js'
import { handleGuestMode } from '@/utils/common'

const joinPopup = ref(null)
const navEleColor = ref('#FFFFFF')
const navOpacity = ref(0)
const userApi = new UserApi()
const indexApi = new IndexApi()
const store = useStore()
const sharePageFlag = computed(() => store.getters.sharePageFlag)
const guardApi = new GuardApi()
const trendsRes = ref([])
const leftWidth = ref(16)
const marginClass = ref('normal')
const dialog = ref(false)
const imgUrl = ref('')
const ifSign = computed(() => store.getters.signInfo.ifSign)
const date = computed(() => store.getters.signInfo.date)
const today = new Date().toISOString().split('T')[0]
const hoofprintId = computed(() => store.state.hoofPrintId)
const showDialog = ref(false)
const sharePopup = ref(null)
const inviteCount = ref(0)
// states
const userCardInfo = ref({
	guardDays: 0,
	hoofprintCode: '',
	claimTime: ''
})
const navbarInfo = computed(() => store.getters.navbarInfo)
const userLogin = computed(() => store.getters.userLogin)
const name = computed(() => store.state.userInfo.userName)
const userRegistered = computed(() => store.state.userRegistered)

const showGuardCard = async (status) => {
	showDialog.value = status
	if (showDialog.value) {
		if (today === date.value && ifSign) {
			return
		} else {
			try {
				const resData = await guardApi.getGuardSignin(hoofprintId.value)
				if (resData.code == 1) {
					imgUrl.value = `${imgBaseUrl}guard/guard-sign-card.png`
					dialog.value = true
					const { data } = await userApi.getUserProfile()
					store.dispatch('setUserInfo', data)
					ifSign.value = true
					const signdDate = {
						date: new Date().toISOString().split('T')[0],
						ifSign: true
					}
					uni.setStorageSync('signInfo', signdDate)
					store.dispatch('setSignInfo', signdDate)
				}
			} catch (error) {
				console.error('error', error)
			}
		}
	}
}
// swiper data
const swiperData = [
	{
		bcUrl: imgBaseUrl + 'home/home-swiper-bc-1.png',
		eventFunction: handleGuestMode(() => {
			groupDialogRef.value.open()
			uni.hideTabBar()
		})
	},
	{
		bcUrl: imgBaseUrl + 'home/home-swiper-bc-2.png',
		eventFunction: handleGuestMode(() => {
			uni.navigateTo({
				url: '/pages/volunteer/index'
			})
		})
	}
]

const claimData = reactive({
	claimTime: '',
	hoofprintCode: '',
	hoofprintId: 0,
	imageUrl: '',
	guardMember: 0,
	userName: ''
})

const isClaimFlag = ref(false)
const claimPopup = ref(null)
const isShowGroupDialog = ref(false)
const groupDialogRef = ref(null)
const loginPopup = ref(null)
const updateDialog = (value) => {
	dialog.value = value
}
// Share Function
const onShare = () => {
	// claimPopup.value.close()
	// popup.value.close()
	uni.showToast({
		title: '请点击右上角的“...”进行分享',
		icon: 'none'
	})
}

// Entry guard Function
const onEntryGuard = () => {
	errorSolve(claimPopup.value)
	claimPopup.value.close()
	uni.showTabBar()
	onTapNav('guard')
}
// functions
function onTapNav(type) {
	if (userLogin.value === false) {
		goToLogin()
	} else {
		if (type === 'wait') {
			uni.showToast({
				title: '敬请期待',
				icon: 'none'
			})
		} else {
			if (!isClaimFlag.value) {
				uni.showToast({
					title: '请先进行认领',
					icon: 'none'
				})
				return
			}
			if (type == 'guard') {
				uni.removeStorageSync('isVolunteerPage')
			}
			uni.navigateTo({
				url: `/pages/${type}/index`
			})
		}
	}
}
const onRecordsNav = handleGuestMode(() => {
	if (userLogin.value === false) {
		goToLogin()
	} else {
		if (!isClaimFlag.value) {
			uni.showToast({
				title: '请先进行蹄窝认领',
				icon: 'none'
			})
			return
		}
		uni.removeStorageSync('isVolunteerPage')
		uni.navigateTo({
			url: '/pages/guard/records?from=index'
		})
	}
})

// Claim Function
const onClaim = async () => {
	if (userLogin.value === false) {
		goToLogin()
	} else {
		errorSolve(claimPopup.value)
		claimPopup.value.open('center')
		uni.hideTabBar()
		const resData = await guardApi.getGuardApply()
		if (resData?.code == 1) {
			Object.assign(claimData, resData.data)
			store.dispatch('saveHoofPrintId', claimData.hoofprintId)
			isClaimFlag.value = true
		}
	}
}

// Calculation Level Function
const setUserLevel = computed(() => {
	const days = userCardInfo.value.guardDays
	switch (true) {
		case days > 730:
			return 54
		case days > 365:
			return 3
		case days > 100:
			return 2
		case days > 30:
			return 1
		default:
			return 0
	}
})

const cardData = computed(() => ({
	userCardInfo: userCardInfo.value,
	bcUrl: setUserLevel.value
}))

// Get User CardInfo Function
const getUserCardInfo = async () => {
	try {
		const resData = await guardApi.getCheckApplied()
		if (resData.data) {
			isClaimFlag.value = true
		}
		const { data } = await indexApi.getUserCardInfo()
		store.dispatch('saveHoofPrintId', data.hoofprintId)
		userCardInfo.value = data || {}
	} catch (error) {
		console.error('Error in indexApi.getUserCardInfo:', error)
	}
}

function closeGroupDialog() {
	groupDialogRef.value.close()
	isShowGroupDialog.value = false
	uni.showTabBar()
}

async function getTrendsTop() {
	const topRes = await indexApi.getTrendsList()
	trendsRes.value = topRes.data
}

async function getUserProfile() {
	const { data } = await userApi.getUserProfile()
	store.dispatch('setUserInfo', data)
}

const goLogin = () => {
	uni.switchTab({ url: '/pages/manage/index' })
}

const refreshPopup = () => {
	loginPopup.value?.closePopup()
	claimPopup.value.close()
}

async function goToLogin() {
	errorSolve(loginPopup.value)
	loginPopup.value.openPopup()
}

function removeRecordById(id) {
	trendsRes.value = trendsRes.value.filter((record) => record.id !== id)
}
function refreshRecords(id) {
	removeRecordById(id)
}

const onSkipPage = handleGuestMode((url) => {
	uni.navigateTo({
		url: url
	})
})

onShareAppMessage(async () => {
	errorSolve(sharePopup.value)
	sharePopup.value.openPopup()
	await guardApi.getGuardShare()
})

onShareTimeline(async () => {
	errorSolve(sharePopup.value)
	sharePopup.value.openPopup()
	await guardApi.getGuardShare()
	return {
		title: '京西水峪嘴云村民'
	}
})

const getInviteCount = async () => {
	errorSolve(joinPopup.value)
	const { data: count } = await userApi.getUserInviteCount()
	inviteCount.value = count
	if (count) {
		uni.hideTabBar()
		joinPopup.value.openPopup()
	}
}

// TODO：兜底函数，后续开发可拓展，目前仅进行空判断
const errorSolve = (thing) => {
	if (!thing) {
		return
	}
}

// const handleToChat = () => {
// 	uni.navigateTo({
// 		url: '/pages/chat/index'
// 	})
// }

// Init
onLoad(async () => {
	if (!sharePageFlag.value && userRegistered.value) {
		await getUserCardInfo()
		await getUserProfile()
		await getInviteCount()
		const signData = uni.getStorageSync('signInfo')
		if (signData.date === today && signData.ifSign) {
			store.dispatch('setSignInfo', signData)
		}
	}
})
onShow(async () => {
	if (!sharePageFlag.value && userRegistered.value) {
		await getUserCardInfo()
		await getUserProfile()
		refreshPopup()
		if (userLogin.value === false) {
			isClaimFlag.value = false
		}
	}
	if (!sharePageFlag.value) {
		await getTrendsTop()
	}
})
onHide(() => {
	refreshPopup()
})
onPageScroll((e) => {
	if (e.scrollTop <= 44) {
		navOpacity.value = e.scrollTop / 44
		navEleColor.value = '#FFFFFF'
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: 'transprant'
		})
	} else {
		navOpacity.value = 1
		navEleColor.value = '#29231D'
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: 'transprant'
		})
	}
})
</script>

<style scoped lang="scss">
@mixin flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}

page {
	background-color: #fff;
	height: 100%;
}

@mixin flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

.page-container {
	display: flex;
	min-height: 100vh;
	overflow-y: auto;
	flex-direction: column;
	box-sizing: border-box;
	position: relative;
}

.container-bg {
	top: 0;
	width: 100%;
	height: 634rpx;
	position: absolute;
	z-index: 0;
}

.home-container {
	display: flex;
	flex-direction: column;
	gap: 16px;
	height: 100%;
	width: 100%;
	padding: 28px 18px 0 18px;
	box-sizing: border-box;
	background-color: #fafafa;
}

.user-card {
	width: 100vw;
	height: 362rpx;
	position: relative;
	display: flex;
	flex-direction: column;
	fill: #fff;
	box-sizing: border-box;
	.claim-img-bc {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}

	.claim-area {
		width: 100%;
		height: 100%;
		background-size: cover;
		background-repeat: no-repeat;
		background-position: center;
		position: relative;

		.claim-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			background: linear-gradient(0deg, #ff9212 42.09%, #ffb763 101.14%);
			box-shadow: 0px 3px 4px 0px rgba(138, 75, 0, 0.2);

			width: 192rpx;
			height: 58rpx;
			border-radius: 88rpx;

			color: #fff;
			font-family: Douyin Sans;
			font-size: 16px;
			text-align: center;

			position: absolute;
			bottom: 28rpx;
			left: 50%;
			transform: translateX(-50%);
		}
	}
}

.guest-user-card {
	width: 100vw;
	position: relative;
	transform: translateX(-18px);
}

.user-card-bg {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: -1;
}

.user-card-title-area {
	display: flex;
	flex-direction: column;
	margin-left: 38rpx;
	margin-top: 82rpx;

	.user-card-title {
		position: relative;

		.claim-btn {
			background-color: rgba(255, 146, 18, 1);
			width: 192rpx;
			height: 58rpx;
			border-radius: 88rpx;

			font-family: Douyin Sans;
			text-align: left;
			color: rgba(107, 152, 52, 1);
		}

		.textIcon {
			width: 7.55px;
			height: 7.55px;
			position: absolute;
			top: 0;
			right: 0;
			transform: translate(50%, -50%);
		}
	}

	.user-card-small {
		font-family: PingFang SC;
		font-size: 10px;
		font-weight: 400;
		line-height: 14px;
		text-align: left;
		color: rgba(107, 152, 52, 1);
		margin-top: 2px;
	}
}

.user-card-area {
	position: absolute;
	bottom: 65.5px;
	margin-left: 38rpx;
}

.user-card-record {
	display: flex;
	align-items: flex-end;
	align-items: baseline;
}

.user-card-record-text {
	font-size: 16px;
	color: rgba(0, 0, 0, 0.4);
	margin-right: 8rpx;
	font-size: 10px;
	font-weight: 500;
	line-height: 14px;
	text-align: left;
}

.user-card-record-num {
	font-size: 26px;
	line-height: 26px;
	color: rgba(255, 146, 18, 1);
	font-family: PingFang SC;
	font-size: 16px;
	font-weight: 500;
	line-height: 14px;
	text-align: left;
}

.user-card-record-after {
	vertical-align: baseline;
	font-size: 14px;
	color: rgba(121, 194, 5, 1);
	font-family: PingFang SC;
	font-size: 8px;
	font-weight: 500;
	line-height: 14px;
	text-align: left;

	.slash {
		font-size: 10px;
		text-align: left;
		color: rgba(121, 194, 5, 1);
	}
}

.user-card-progress {
	display: flex;
	align-items: center;
	margin-top: 10rpx;
	gap: 8px;
}

.user-card-progress-bar {
	width: 246rpx;
}

.user-card-progress-text {
	font-size: 10px;
	color: #ffb555;
	line-height: 14px;
}

.user-card-bottom {
	position: absolute;
	bottom: 24rpx;
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	width: 100%;
	padding: 0 16px;
	box-sizing: border-box;
}

.user-card-guard-infos {
	display: flex;
	align-items: center;
	gap: 4px;
	color: rgba(21, 21, 21, 0.7);
	font-size: 10px;
	line-height: 14px;

	.signIcon {
		width: 64rpx;
		height: 64rpx;
	}

	.signTextArea {
		margin-left: 16rpx;
		font-family: PingFang SC;
		font-weight: 500;

		.sign-text-title {
			font-size: 12px;
			line-height: 16.8px;
			text-align: left;
			color: rgba(21, 21, 21, 1);
		}

		.sign-text-content {
			margin-top: 4rpx;
			font-size: 10px;
			line-height: 14px;
			text-align: left;
			color: rgba(21, 21, 21, 0.4);

			text {
				font-family: PingFang SC;
				text-align: center;
				color: rgba(121, 194, 5, 1);
			}
		}
	}
}

.user-card-guard-entry {
	padding: 4px 12px;
	border-radius: 44px;
	border: 1px solid rgba(255, 255, 255, 0.79);
	background: linear-gradient(0deg, #ff9014 35.64%, #ffb555 101.14%);
	color: #fff;
	text-shadow: 0.5px 0.5px 0px #d2781f;
	font-size: 14px;
	font-weight: 600;
	line-height: 20px;
}

.nav-container {
	@include flex-center;
	gap: 80rpx;
	z-index: 2;
	.nav-item {
		@include flex-center;
		flex-direction: column;
		width: 88rpx;
		.nav-item-icon {
			width: 100%;
			height: 88rpx;
		}
		.nav-item-text {
			font-family: PingFang SC;
			font-size: 9.2px;
			font-weight: 400;
		}
	}
}

.community-entry {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 220rpx;
	position: relative;
	filter: drop-shadow(0px 0px 5px rgba(0, 0, 0, 0.06));
	box-sizing: border-box;
}

:deep(.uni-popup__wrapper) {
	width: 100%;
	background-color: transparent !important;
}

.community-entry-bg {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: -1;
	top: 0;
	left: 0;
}

.community-entry-records {
	display: flex;
	flex: 1;
	align-items: flex-end;
	gap: 4px;

	font-family: PingFang SC;
	font-size: 10px;
	font-weight: 400;
	line-height: 14px;
	text-align: left;
	color: #fff;
	position: absolute;
	bottom: 10rpx;

	image {
		margin-right: 4rpx;
	}
}

.avater {
	height: 14px;
	width: 25px;
	display: inline-block;
}

.numberArea {
	position: absolute;
	right: 32rpx;
	bottom: 114rpx;
	background-color: rgba(18, 31, 3, 0.1);
	border-radius: 23px;
	color: #fff;
	padding: 0px 9px 1px 9px;
	font-family: PingFang SC;
	font-size: 10px;
	font-weight: 400;
	line-height: 14px;
	text-align: left;
	backdrop-filter: blur(10px);
}

.claim-popup {
	.line {
		width: 100%;
		height: 12rpx;
	}

	// claim-title
	.claim-title {
		display: flex;
		justify-content: center;
		align-items: flex-end;

		font-family: Douyin Sans;
		font-size: 26px;
		line-height: 32px;
		letter-spacing: 3px;
		text-align: center;
		color: #fff;

		margin-bottom: 32rpx;

		.claim-title-green {
			width: fit-content;
			font-size: 32px;
			font-family: Douyin Sans;
			line-height: 38.46px;
			text-align: center;
			color: #b4ef3d;
		}
	}

	// claim-text
	.claim-text {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		color: rgba(230, 230, 230, 1);

		.claim-text-first {
			font-family: PingFang SC;
			font-size: 14px;
			font-weight: 400;
			line-height: 19.6px;
			text-align: center;

			.claim-text-number {
				font-size: 24px;
				font-weight: 600;
				line-height: 33.6px;
				color: #a7ec22;
				width: fit-content;
			}
		}
	}

	// claim-button
	.claim-btn {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 48rpx;

		.claim-btn-item {
			height: 64rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0;
			font-family: Douyin Sans;
			font-size: 18px;

			&:first-child {
				width: 154rpx;
				margin-right: 32rpx;
				border-radius: 88rpx;
				color: rgba(255, 255, 255, 0.6);
				background-color: transparent;
				border: 1px solid rgba(255, 255, 255, 0.6);
				line-height: 21.64px;
				text-align: center;
			}

			&:last-child {
				display: flex;
				align-items: center;
				justify-content: center;
				background: linear-gradient(0deg, #ff9212 42.09%, #ffb763 101.14%);

				width: fit-content;
				border-radius: 88rpx;
				color: #fff;
				line-height: 21.64px;
				text-align: center;
			}
		}
	}
}

.record-list {
	margin-bottom: 16rpx;
}

.record-list-title {
	position: relative;
}

.record-list-title-png {
	height: 40px;
	width: 100%;
	z-index: 1;
	position: absolute;
	border-radius: 8px 8px 0px 0px;
}

.record-list-title-text {
	display: flex;
	z-index: 2;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	height: 40px;
	margin: 0px 16px 0px 16px;
	position: relative;
}

.record-list-title-left {
	color: rgba(21, 21, 21, 1);
	font-family: FZZhengHeiS-B-GB;
	font-size: 16px;
	font-weight: 400;
	line-height: 19.56px;
	text-align: left;
	color: #151515;
	letter-spacing: 0.1px;
	z-index: 3;
}
.record-list-title-underline {
	width: 74rpx;
	height: 8rpx;
	position: absolute;
	padding-top: 26rpx;
	padding-left: 60rpx;
	z-index: 2;
}
.record-list-title-right {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.record-list-title-more {
	font-size: 12px;
	color: rgba(21, 21, 21, 0.5);
	letter-spacing: 0.1px;
	font-family: PingFang SC;
	font-weight: 400;
}

.recor-list-title-icon {
	width: 12px;
	height: 12px;
}

.group-dialog-container {
	display: flex;
	justify-content: center;
	flex-direction: column;
	align-items: center;
	.group-dialog-content {
		padding: 0 80rpx 0 80rpx;
		height: 770rpx;
		width: 100%;
		box-sizing: border-box;
		display: flex;
		position: relative;
		flex-direction: column;
		background-color: transparent;
		.group-dialog-top {
			border-radius: 48rpx;
			width: 100%;
			height: 424rpx;
			position: relative;
			.group-dialog-bc {
				width: 100%;
				height: 100%;
			}
			.group-dialog-pepole {
				width: 100%;
				height: 216rpx;
				position: absolute;
				top: 0;
				left: 0;
			}
		}
		.group-dialog-code-container {
			width: 430rpx;
			height: 430rpx;
			background-color: rgba(255, 255, 255, 1);
			position: absolute;
			left: 160rpx;
			top: 188rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 24px;
			box-shadow: 0px 4px 6.2px 0px rgba(0, 0, 0, 0.1);

			.group-dialog-code {
				width: 356rpx;
				height: 356rpx;
			}
		}

		.group-dialog-bottom {
			flex: 1;
			border-bottom-left-radius: 48rpx;
			border-bottom-right-radius: 48rpx;
			font-family: PingFang SC;
			font-size: 12px;
			font-weight: 500;
			line-height: 16.8px;
			text-align: left;
			color: rgba(185, 185, 185, 1);
			background-color: #fff;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: flex-end;
			padding-bottom: 50rpx;
		}
	}

	.group-icon-close {
		width: 56rpx;
		height: 56rpx;
		margin-top: 48rpx;
	}
}
.guards-video {
	display: flex;
	gap: 6px;
	margin-bottom: -8px;
	.guards-infos {
		width: 334rpx;
		height: 150rpx;
		.guards-infos-bg {
			width: 334rpx;
			height: 150rpx;
			position: absolute;
		}
		.guards-infos-texts {
			display: flex;
			margin-left: 28rpx;
			margin-top: 20rpx;
			flex-direction: column;
			position: relative;
			.guards-infos-texts-one {
				font-family: Douyin Sans;
				font-size: 14px;
				font-weight: 500;
				line-height: 16.83px;
				color: rgba(21, 21, 21, 0.8);
			}
			.guards-infos-texts-two {
				font-family: PingFang SC;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				color: rgba(0, 0, 0, 0.4);
			}
			.guards-infos-texts-go {
				width: 62rpx;
				height: 28rpx;
				margin-top: 24rpx;
			}
		}
	}
}
.chat-enter {
	position: fixed;
	width: 110rpx;
	height: 120rpx;
	top: 91.8%;
	right: 28rpx;
}
</style>
