<template>
	<view>
		<view class="table-content">
			<view class="table-item" v-for="item in tableData" :key="item.id">
				<slot :name="`active${item.id}`"></slot>
				<view
					v-if="item.id === 0"
					class="table-item-content"
					:style="{ height: props.tableData[item.id]?.styleData?.height }"
				>
					<view
						class="table-item-content table-background"
						:style="{ height: props.tableData[item.id]?.styleData?.heightFirst }"
					>
						+{{ item.point }}
					</view>
				</view>
				<view
					v-else-if="item.id === tableData.length - 1"
					class="table-item-content"
					:style="{ height: props.tableData[item.id]?.styleData?.height }"
				>
					<view
						class="table-item-content table-background"
						:style="{ height: props.tableData[item.id]?.styleData?.heightLast }"
					>
						+{{ item.point }}
					</view>
				</view>
				<view
					v-else
					class="table-item-content table-background"
					:style="{ height: props.tableData[item.id]?.styleData?.height }"
				>
					+{{ item.point }}
				</view>
				<slot :name="`${item.id}`"></slot>
			</view>
		</view>
	</view>
</template>
<script setup>
const props = defineProps({
	tableData: {
		type: Array,
		require: true
	}
})
console.log(props.tableData[0].styleData.height)
</script>
<style scoped lang="scss">
.table-content {
	flex-direction: column;
	justify-content: space-between;
	margin: 25px 11px 16px 12px;
	display: flex;
	color: rgba(0, 0, 0, 0.7);
	line-height: 19.6px;
	font-size: 14px;
	letter-spacing: 0.1px;
	border-right: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-left: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-radius: 8px;
}
.table-item {
	display: flex;
	flex-direction: row;
	flex: 1;
	justify-content: center;
}
.table-item:first-child .table-item-content:first-child {
	border-top: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-top-left-radius: 8px;
}
.table-item:first-child .table-item-content:last-child {
	border-top: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-top-right-radius: 8px;
}
.table-item:nth-child(1) .table-item-content:nth-child(2) {
	transform: translateY(-5px);
}
.table-item:last-child .table-item-content:last-child {
	border-bottom: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-bottom-right-radius: 8px;
}
.table-item:last-child .table-item-content:first-child {
	border-bottom: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-bottom-left-radius: 8px;
}
.table-item:last-child .table-item-content:nth-child(2) {
	transform: translateY(3px);
}
.table-item-content {
	display: flex;
	flex-direction: row;
	flex: 1;
	justify-content: center;
	align-items: center;
}
.table-background {
	background-color: rgba(255, 220, 160, 0.58);
	justify-content: center;
	font-family: Douyin Sans;
	font-size: 14px;
	font-weight: 700;
	line-height: 16.83px;
	color: rgba(255, 158, 49, 1);
}
.table-item:not(:last-child) {
	border-bottom: 0.5px rgba(0, 0, 0, 0.1) solid;
}
</style>
