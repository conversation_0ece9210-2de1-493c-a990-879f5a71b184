<template>
	<view
		class="container"
		:style="{ backgroundImage: `url(${cardUrls[props.cardData.bcUrl].bcUrl})` }"
	>
		<!-- card-title -->
		<view class="user-card-title-area">
			<text class="user-card-title"
				>水峪嘴<text>云村民</text
				><image :src="`${imgBaseUrl}home/home-user-text-icon.png`" class="textIcon"
			/></text>
			<text class="user-card-small">#我在古道有个窝</text>
		</view>
		<!-- card-area -->
		<view class="user-card-area">
			<view class="user-card-record">
				<text class="user-card-record-text">蹄窝守护</text>
				<text class="user-card-record-num">{{ props.cardData.userCardInfo.guardDays }}</text>
				<text class="user-card-record-after"> <text class="slash">/</text>500天</text>
			</view>
			<view class="user-card-progress">
				<progress
					class="user-card-progress-bar"
					:percent="(props.cardData.userCardInfo.guardDays / 500) * 100"
					stroke-width="3"
					active-color="#FFB555"
					background-color="#FFFFFF"
				/>
			</view>
		</view>
		<!-- card-bottom -->
		<view class="user-card-bottom">
			<view class="user-card-guard-infos">
				<img
					class="signIcon"
					:src="
						ifSign
							? `${imgBaseUrl}home/home-nav-sign-icon.png`
							: `${imgBaseUrl}home/home-nav-unsign-icon.png`
					"
					@click="handleClickSign"
				/>
				<view class="signTextArea">
					<view class="sign-text-title">{{ ifSign ? '今日签到完成' : '点此签到' }}</view>
					<view class="sign-text-content">签到 <text>+2守护值</text></view>
				</view>
			</view>
			<view class="user-card-guard-entry" @click="onEntryGuard">进入守护</view>
		</view>
		<!-- number-area -->
		<view class="number-area"> 编号{{ props.cardData.userCardInfo.hoofprintCode }} </view>
		<!-- plant-area -->
		<img
			:src="`${cardUrls[props.cardData.bcUrl].plantUrl}`"
			:style="{ width: `${cardUrls[props.cardData.bcUrl].width}` }"
			class="plant-img"
		/>
	</view>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useStore } from 'vuex'
import { imgBaseUrl } from '@/config'
import { cardUrls } from './config'

const store = useStore()

const props = defineProps({
	cardData: {
		type: Object,
		required: true
	}
})
const emit = defineEmits(['showGuardCard'])
const ifSign = computed(() => store.getters.signInfo.ifSign)

const onEntryGuard = () => {
	uni.navigateTo({
		url: '/pages/guard/index'
	})
}

const handleClickSign = async () => {
	emit('showGuardCard', true)
}
</script>
<style scoped lang="scss">
.container {
	background-size: cover;
	position: absolute;
	width: 100%;
	height: 362rpx;

	// card-title
	.user-card-title-area {
		display: flex;
		flex-direction: column;
		margin-left: 38rpx;
		margin-top: 40rpx;

		.user-card-title {
			position: relative;
			width: fit-content;
			padding-top: 0;
			font-size: 20px;
			color: #151515;
			font-family: Douyin Sans;
			font-size: 20.32px;
			line-height: 24.43px;
			text-align: left;
			text {
				font-family: Douyin Sans;
				text-align: left;
				color: rgba(107, 152, 52, 1);
			}
			.textIcon {
				width: 7.55px;
				height: 7.55px;
				position: absolute;
				top: 0;
				right: 0;
				transform: translate(50%, -50%);
			}
		}
		.user-card-small {
			font-family: PingFang SC;
			font-size: 12px;
			font-weight: 400;
			line-height: 14px;
			text-align: left;
			color: rgba(107, 152, 52, 1);
			margin-top: 2px;
		}
	}

	// card-area
	.user-card-area {
		position: absolute;
		bottom: 65.5px;
		margin-left: 38rpx;
		.user-card-record {
			display: flex;
			align-items: flex-end;
			align-items: baseline;
			.user-card-record-text {
				font-size: 16px;
				color: rgba(0, 0, 0, 0.4);
				margin-right: 8rpx;
				font-size: 12px;
				font-weight: 500;
				line-height: 14px;
				text-align: left;
			}
			.user-card-record-num {
				font-size: 26px;
				line-height: 26px;
				color: rgba(255, 146, 18, 1);
				font-family: PingFang SC;
				font-size: 16px;
				font-weight: 600;
				line-height: 14px;
				text-align: left;
			}
			.user-card-record-after {
				vertical-align: baseline;
				font-size: 14px;
				color: rgba(121, 194, 5, 1);
				font-family: PingFang SC;
				font-size: 8px;
				font-weight: 500;
				line-height: 14px;
				text-align: left;
				.slash {
					font-size: 10px;
					text-align: left;
					color: rgba(121, 194, 5, 1);
				}
			}
		}
		.user-card-progress {
			display: flex;
			align-items: center;
			margin-top: 10rpx;
			gap: 8px;
			.user-card-progress-bar {
				width: 246rpx;
			}
			.user-card-progress-text {
				font-size: 10px;
				color: #ffb555;
				line-height: 14px;
			}
		}
	}

	// card-bottom
	.user-card-bottom {
		position: absolute;
		bottom: 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		padding: 0 16px;
		box-sizing: border-box;
		.user-card-guard-infos {
			display: flex;
			align-items: center;
			gap: 4px;
			color: rgba(21, 21, 21, 0.7);
			font-size: 10px;
			line-height: 14px;
			.signIcon {
				width: 64rpx;
				height: 64rpx;
			}
			.signTextArea {
				margin-left: 16rpx;
				font-family: PingFang SC;
				font-weight: 500;

				.sign-text-title {
					font-size: 12px;
					line-height: 16.8px;
					text-align: left;
					color: rgba(21, 21, 21, 1);
				}
				.sign-text-content {
					margin-top: 4rpx;
					font-size: 10px;
					line-height: 14px;
					text-align: left;
					color: rgba(21, 21, 21, 0.4);
					text {
						font-family: PingFang SC;
						text-align: center;
						color: rgba(121, 194, 5, 1);
					}
				}
			}
		}
		.user-card-guard-entry {
			padding: 4px 12px;
			border-radius: 44px;
			border: 1px solid rgba(255, 255, 255, 0.79);
			background: linear-gradient(0deg, #ff9014 35.64%, #ffb555 101.14%);
			color: #fff;
			text-shadow: 0.5px 0.5px 0px #d2781f;
			font-size: 14px;
			font-weight: 600;
			line-height: 20px;
		}
	}

	// card-number
	.number-area {
		position: absolute;
		right: 32rpx;
		bottom: 114rpx;
		z-index: 1;
		background-color: rgba(18, 31, 3, 0.1);
		border-radius: 23px;
		color: #fff;
		padding: 0px 9px 1px 9px;
		font-family: PingFang SC;
		font-size: 10px;
		font-weight: 400;
		line-height: 14px;
		text-align: left;
		backdrop-filter: blur(10px);
	}

	// plant-area
	.plant-img {
		height: 298rpx;
		position: absolute;
		right: 8rpx;
		bottom: 106rpx;
	}
}
</style>
