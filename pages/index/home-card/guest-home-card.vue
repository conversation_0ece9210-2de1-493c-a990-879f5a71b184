<template>
	<view
		class="guest-home-card"
		:style="{ backgroundImage: `url(${imgBaseUrl}home/guest-home-card-bg.png)` }"
	>
		<image :src="`${imgBaseUrl}home/guest-home-card-slogan.png`" class="guest-home-card-slogan" />
		<view class="guest-home-card-content">
			<text>点击登录，了解更多云村精彩内容</text>
		</view>
		<view class="guest-home-card-login">
			<view class="guest-home-card-login-btn-bg">
				<image
					:src="`${imgBaseUrl}home/guest-home-card-btn.png`"
					class="guest-home-card-btn"
					@click="gotoRegister"
				/>
			</view>
		</view>
	</view>
</template>
<script setup lang="ts">
import { imgBaseUrl } from '@/config'

const gotoRegister = () => {
	uni.navigateTo({
		url: '/pages/register/index'
	})
}
</script>
<style scoped>
.guest-home-card {
	width: 100%;
	height: 418rpx;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	display: flex;
	flex-direction: column;
	z-index: 100;
}

.guest-home-card-slogan {
	width: 310rpx;
	height: 110rpx;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	margin-top: 36rpx;
	margin-left: 60rpx;
	margin-bottom: 20rpx;
}

.guest-home-card-content {
	font-size: 12px;
	color: #299cd7;
	margin-left: 92rpx;
	width: 234rpx;
	word-break: break-all;
	text-align: left;
}

.guest-home-card-login {
	width: 100%;
	height: 100%;
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: flex-end;
}

.guest-home-card-btn {
	width: 192rpx;
	height: 58rpx;
}

.guest-home-card-login-btn-bg {
	width: 100%;
	height: 94rpx;
	padding-bottom: 28rpx;
	background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fafafa 100%);
	display: flex;
	justify-content: center;
	align-items: flex-end;
}
</style>
