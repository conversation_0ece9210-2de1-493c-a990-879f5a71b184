<template>
	<view class="page-container" :style="`padding-top: ${navbarInfo.barHeight}px;`">
		<navbar
			title="等级权益"
			showNavBack
			:onNavClick="backToPreviousPage"
			:opacity="navOpacity"
			:eleColor="navEleColor"
		></navbar>
		<view class="content">
			<view class="content-top">
				<view
					class="message"
					:style="{
						backgroundImage: `url(${imgBaseUrl}index/index-message.png)`
					}"
				>
					<view class="message-text">
						<text>您当前的守护值是</text>
						<image class="text-image" :src="`${imgBaseUrl}index/index-guard-icon.png`"></image>
						<text class="text-value" @click="onToScorePage"> {{ point }}</text>
						<text>，快去增加守护值吧!</text>
					</view>
				</view>
			</view>
			<CharacterCard :equipmentsList="currentEquipmentsList" :isCard="true" class="image" />
			<view class="content-table">
				<view class="table-title">
					<view class="table-title-text">怎么获得守护值?</view>
					<image
						class="table-title-underline"
						:src="`${imgBaseUrl}index/index-underline.png`"
					></image>
				</view>
				<!-- 表格第一行添加这个属性table-border-top-left -->
				<GuardTable :tableData="tableData">
					<template v-slot:active0>
						<view class="table-item-content table-border-top-left">签到</view>
					</template>
					<template v-slot:active1>
						<view class="table-item-content">守护蹄窝</view>
					</template>
					<template v-slot:active2>
						<view class="table-item-content">分享小程序</view>
					</template>
					<template v-slot:active3>
						<view class="table-item-content">新用户扫码</view>
					</template>
					<template v-slot:active4>
						<view class="table-item-content table-column">
							<view>发布普通</view>
							<view>守护动态</view>
						</view>
					</template>
					<!-- 表格最后一行时添加这个属性table-border-bottom-left -->
					<template v-slot:active5>
						<view class="table-item-content table-column table-border-bottom-left">
							<view>发布精选</view>
							<view>守护动态</view>
						</view>
					</template>
					<!-- 表格第一行添加这个属性table-item-border-top -->
					<template v-slot:0>
						<view class="table-item-content table-border-top-right">每天一次</view>
					</template>
					<template v-slot:1>
						<view class="table-item-content">每天一次</view>
					</template>
					<template v-slot:2>
						<view class="table-item-content table-item-second">
							<view>每天不限次</view>
							<view>前三次可加分</view>
						</view>
					</template>
					<template v-slot:3>
						<view class="table-item-content table-item-second">
							<view>新用户扫码注册</view>
							<view>每天不限次数</view>
						</view>
					</template>
					<template v-slot:4>
						<view class="table-item-content table-item-second">
							<view>每天不限次</view>
							<view>前三次可加分</view>
						</view>
					</template>
					<!-- 表格最后一行时添加这个属性table-item-border-bottom -->
					<template v-slot:5>
						<view class="table-item-content table-item-second table-border-bottom-right">
							<view>每天不限次</view>
							<view>前三次可加分</view>
						</view>
					</template>
				</GuardTable>
			</view>
			<view class="content-explain">
				<view class="explain-title">
					<view class="explain-title-text">守护值有什么用?</view>
					<image
						class="explain-title-underline"
						:src="`${imgBaseUrl}index/index-underline.png`"
					></image>
					<image class="explain-title-smile" :src="`${imgBaseUrl}index/index-smile.png`"></image>
				</view>
				<view class="explain-text">
					<text class="explain-text-content">
						守护值是您作为云村民的虚拟积分，对应村民等级晋升机制，根据线上线下公益保护行为，
						云村民可从古道西风晋升至古道行者、古道热肠、古道匠人、古道先锋；守护值达到不同等级可兑换不同等级礼遇，
						如门票优惠、参观村使馆、参加青年夜话、成为荣誉村民、优先申请村民创业项目等。
					</text>
				</view>
			</view>
			<view class="content-button">
				<button @click="handleGoInterestLevel" class="content-button-left">
					<text class="button-text">了解等级权益</text>
				</button>
				<button @click="handleGoGuard" class="content-button-right">
					<text class="button-text">进入守护</text>
				</button>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { imgBaseUrl } from '../../config'
import CharacterCard from '../../pages/character/components/character-card/character.vue'
import { EquipmentApi } from '@/api/character'
import { IndexApi } from '@/api/index'
import { onPageScroll } from '@dcloudio/uni-app'
import { GuardTable } from './GuardTable.vue'
import tableData from './config.js'

const navEleColor = ref('#000000')
const navOpacity = ref(0)

const equeipmentApi = new EquipmentApi()
const indexApi = new IndexApi()
const currentEquipmentsList = ref([])
const store = useStore()
const navbarInfo = computed(() => store.getters.navbarInfo)
const point = ref(0)
function handleGoInterestLevel() {
	uni.navigateTo({
		url: '/pages/manage/interestLevel'
	})
}
function handleGoGuard() {
	uni.navigateTo({
		url: '/pages/guard/index'
	})
}

function backToPreviousPage() {
	uni.navigateBack({
		url: '/pages/index/index'
	})
}
const getMyCurrentEquipment = async () => {
	const res = await equeipmentApi.getCurrentEquipment()
	const data =
		res.data.map((item) => {
			return { ...item, status: 1 }
		}) || []
	currentEquipmentsList.value = data
}

const getGuardPoint = async () => {
	try {
		const { data } = await indexApi.getUserCardInfo()
		point.value = data.points
	} catch (error) {
		console.error(error)
	}
}

const onToScorePage = () => {
	uni.navigateTo({
		url: '/pages/score/index'
	})
}

onPageScroll((e) => {
	console.log('1')
	if (e.scrollTop <= 44) {
		navOpacity.value = e.scrollTop / 44
		navEleColor.value = '#000000'
	} else {
		navOpacity.value = 1
		navEleColor.value = '#29231D'
	}
})

onMounted(() => {
	getMyCurrentEquipment()
	getGuardPoint()
})
</script>
<style scoped lang="scss">
.page-container {
	height: 100vh;
	box-sizing: border-box;
	overflow-y: scroll;
	overflow-x: hidden;
	-ms-overflow-style: none;
	scrollbar-width: none;
}
.page-container::-webkit-scrollbar {
	display: none;
}

.content {
	width: 100%;
	display: flex;
	position: relative;
	flex-direction: column;
	background: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/index/index-guard-equity-bg.png');
	background-size: cover;
	background-repeat: repeat-y;
	.content-top {
		position: absolute;
		margin: 28px 110px 0px 27px;
		display: flex;
		align-items: center;
		flex-direction: column;
		z-index: 1;
		.message {
			display: flex;
			justify-content: center;
			z-index: 2;
			padding: 12px 12px 22px 12px;
			background-size: contain;
			background-repeat: no-repeat;
			background-position: center;
		}

		.message-text {
			font-size: 10px;
			font-weight: 400;
			line-height: 14px;
			z-index: 2;
			.text-image {
				width: 28.62rpx;
				height: 29.82rpx;
				margin: 0px 2px 0px 4px;
			}
			.text-value {
				font-size: 16px;
				font-weight: 500;
				margin-right: 2px;
				color: rgba(255, 157, 48, 1);
			}
		}
	}
	.content-table {
		position: relative;
		margin: 200rpx 16px 0px 16px;
		border-radius: 8px;
		z-index: 3;
		background: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/level-guard-bg.png');
		background-repeat: no-repeat;
		background-size: cover;
		.table-title {
			display: flex;
			margin-top: 13px;
			margin-left: 12px;
			z-index: 1;
		}
	}
	.content-explain {
		position: relative;
		margin: 18px 16px 0px 16px;
		background: rgba(255, 255, 255, 1);
		border-radius: 8px;
		.explain-title {
			height: 40px;
			display: flex;
			border-top-left-radius: 8px;
			border-top-right-radius: 8px;
			position: relative;
			z-index: 1;
			background: linear-gradient(
				90deg,
				rgba(255, 255, 255, 0.59) 0%,
				rgba(255, 220, 160, 0.59) 63%
			);
		}
		.explain-text {
			display: flex;
			margin: 18px 27px 21px 28px;
			.explain-text-content {
				font-size: 12px;
				font-weight: 400;
				line-height: 16.8px;
				color: rgba(0, 0, 0, 1);
				text-indent: 2em;
			}
		}
	}
	.content-button {
		display: flex;
		gap: 8px;
		margin-top: 106rpx;
		margin-bottom: 130rpx;
		// position: absolute;
		// bottom: 130rpx;
		// left: 50%;
		// transform: translateX(-50%);
		justify-content: center;
	}
}
.table-title-text {
	font-family: Douyin Sans;
	font-size: 16px;
	font-weight: 700;
	line-height: 19.23px;
	color: rgba(0, 0, 0, 1);
	z-index: 2;
}
.table-title-underline {
	margin-top: 14px;
	margin-left: 62px;
	width: 98rpx;
	height: 8rpx;
	position: absolute;
	z-index: 1;
}
.explain-title-underline {
	width: 98rpx;
	height: 8rpx;
	position: absolute;
	margin-top: 25px;
	margin-left: 80px;
	z-index: 1;
}
.explain-text-content {
	font-size: 12px;
	font-weight: 400;
	line-height: 16.8px;
	text-align: justified;
}
.explain-title-smile {
	width: 168rpx;
	height: 110rpx;
	right: 13px;
	position: absolute;
	bottom: 0px;
}
.content-button-left {
	white-space: nowrap;
	width: fit-content;
	height: 58rpx;

	margin: 0px;
	justify-content: center;
	display: flex;
	align-items: center;
	border-radius: 44px;
	background: linear-gradient(0deg, #ff9212 42.09%, #ffb763 101.14%);
	border-image-source: linear-gradient(
		93.14deg,
		rgba(255, 255, 255, 0.316) 9.18%,
		rgba(255, 255, 255, 0) 51.87%,
		rgba(255, 255, 255, 0.276) 89.73%
	);
}
.button-text {
	font-family: Douyin Sans;
	font-size: 14px;
	font-weight: 700;
	line-height: 16.83px;
	text-align: center;
	color: rgba(255, 255, 255, 1);
}
.content-button-right {
	white-space: nowrap;
	width: fit-content;
	margin: 0px;
	justify-content: center;
	display: flex;
	align-items: center;
	height: 58rpx;
	border-radius: 44px;
	background: linear-gradient(0deg, #ff9212 42.09%, #ffb763 101.14%);
	border-image-source: linear-gradient(
		93.14deg,
		rgba(255, 255, 255, 0.316) 9.18%,
		rgba(255, 255, 255, 0) 51.87%,
		rgba(255, 255, 255, 0.276) 89.73%
	);
}
.explain-title-text {
	margin-top: 12px;
	margin-left: 15px;
	z-index: 2;
	font-family: Douyin Sans;
	font-size: 16px;
	font-weight: 700;
	line-height: 19.23px;
	color: black;
}
.image {
	position: absolute;
	width: 384rpx;
	height: 286rpx;
	right: -30px;
	top: 13px;
}
.table-item-content {
	display: flex;
	flex-direction: row;
	flex: 1;
	justify-content: center;
	text-align: center;
	align-items: center;
}
.table-item-second {
	flex-direction: column;
}
.table-border-top-right {
	border-top: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-top-right-radius: 8px;
}
.table-border-bottom-right {
	border-bottom: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-bottom-right-radius: 8px;
}
.table-border-top-left {
	border-top: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-top-left-radius: 8px;
}
.table-border-bottom-left {
	border-bottom: 0.5px rgba(0, 0, 0, 0.1) solid;
	border-bottom-left-radius: 8px;
}
.table-column {
	flex-direction: column !important;
}
</style>
