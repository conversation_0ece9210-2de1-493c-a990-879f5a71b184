<template>
	<view class="uni-margin-wrap">
		<swiper
			class="swiper"
			circular
			:indicator-dots="false"
			:autoplay="true"
			:interval="2000"
			:duration="500"
			next-margin="-7px"
			previous-margin="-7px"
		>
			<swiper-item v-for="(item, index) in props.swiperData" :key="index">
				<view
					class="item"
					@click="item.eventFunction"
					:style="{ backgroundImage: `url(${item.bcUrl})`, marginRight: '8px', marginLeft: '8px' }"
				>
					<!-- Special item  -->
					<view v-if="item.text" class="have-text">
						<img class="icon-img" :src="item.iconUrl" alt="Image" />
						{{ item.text }}
					</view>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script setup>
const props = defineProps({
	swiperData: {
		type: Array,
		required: true
	}
})
</script>

<style lang="scss" scoped>
.uni-margin-wrap {
	width: 100%;
	height: 220rpx;
	border-radius: 8px;
	.swiper {
		width: 100%;
		height: 100%;
		.item {
			height: 100%;
			background-size: contain;
			background-repeat: no-repeat;
			// Special item
			.have-text {
				display: flex;
				flex: 1;
				align-items: flex-end;
				gap: 4px;

				font-family: PingFang SC;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				text-align: left;
				color: #fff;
				position: absolute;
				bottom: 10rpx;

				.icon-img {
					height: 14px;
					width: 25px;
					display: inline-block;
					margin-left: 36rpx;
				}
			}
		}
	}
}
</style>
