import { imgBaseUrl } from '../../config'

const tableData = [
	{
		id: 0,
		point: 2,
		styleData: {
			heightFirst: `48px`,
			height: `40px`
		}
	},
	{
		id: 1,
		point: 5,
		styleData: {
			height: `40px`
		}
	},
	{
		id: 2,
		point: 10,
		styleData: {
			height: `60px`
		}
	},
	{
		id: 3,
		point: 10,
		styleData: {
			height: `60px`
		}
	},
	{
		id: 4,
		point: 5,
		styleData: {
			heightLast: `66px`,
			height: `60px`
		}
	},
	{
		id: 5,
		point: 30,
		styleData: {
			heightLast: `66px`,
			height: `60px`
		}
	}
]

export const navItems = [
	{
		iconUrl: imgBaseUrl + 'home/home-nav-adopt-icon-new.png',
		text: '云守护',
		skipUrl: '/pages/guard/index'
	},
	{
		iconUrl: imgBaseUrl + 'home/home-nav-activity-icon-new.png',
		text: '云活动',
		skipUrl: '/pages/activity/index'
	},
	{
		iconUrl: imgBaseUrl + 'home/home-nav-guard-icon-new.png',
		text: '等级权益',
		skipUrl: '/pages/index/guardEquity'
	},
	{
		iconUrl: imgBaseUrl + 'home/home-nav-character-icon-new.png',
		text: '云村民证',
		skipUrl: '/pages/manage/character'
	}
]

export default tableData
