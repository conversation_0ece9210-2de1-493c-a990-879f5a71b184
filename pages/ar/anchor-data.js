class AnchorData {
  constructor(id, name, position, rotation, scale, assetId, src) {
    this.id = id
    this.name = name
    this.position = {
      x: position.x,
      y: position.y,
      z: position.z
    }
    this.rotation = {
      x: rotation.x * 180 / Math.PI,
      y: rotation.y * 180 / Math.PI,
      z: rotation.z * 180 / Math.PI
    }
    this.scale = scale
    this.isActive = true
    this.assetId = assetId
    this.src = src
  }
}

class AssetData {
  constructor(id, type, src, options = null) {
    this.id = id
    this.type = type
    this.src = src
    this.options = options
  }
}

export { AnchorData, AssetData }
