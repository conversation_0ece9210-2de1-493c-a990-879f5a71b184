import { imgBaseUrl } from '@/config'

export enum NoticeEnum {
	LocationFailed = 'locationFailed',
	ProjectLoadFailed = 'projectLoadFailed',
	DeviceMemoryLow = 'deviceMemoryLow',
	FrameRateLow = 'frameRateLow',
	ArIniting = 'arIniting',
	ArTrackingRestoring = 'arTrackingRestoring'
}

export const noticeConfigs = {
	[NoticeEnum.LocationFailed]: {
		icon: `${imgBaseUrl}visual/visual-notice-ar-failed.png`,
		title: '定位失败',
		tips: '请尝试换个位置、持稳手机重新手动定位',
		show: false,
		duration: 3000
	},
	[NoticeEnum.ProjectLoadFailed]: {
		icon: `${imgBaseUrl}visual/visual-notice-ar-failed.png`,
		title: '资产加载失败',
		tips: '请退出重新进入',
		show: false,
		duration: 3000
	},
	[NoticeEnum.DeviceMemoryLow]: {
		icon: `${imgBaseUrl}visual/visual-notice-ar-save.png`,
		title: '设备内存不足',
		tips: '建议重启微信释放内存',
		show: false,
		duration: 3000
	},
	[NoticeEnum.FrameRateLow]: {
		icon: `${imgBaseUrl}visual/visual-notice-ar-slow.png`,
		title: '帧率严重下降',
		tips: '建议重启微信',
		show: false,
		duration: 3000
	},
	[NoticeEnum.ArIniting]: {
		iconContent: true,
		icon: `${imgBaseUrl}visual/visual-notice-ar-init.png`,
		title: 'AR初始化中...',
		customContent: true,
		tips: '请轻微移动手机并对准特征丰富的平面区域',
		show: false,
		duration: 3000
	},
	[NoticeEnum.ArTrackingRestoring]: {
		icon: `${imgBaseUrl}visual/visual-notice-ar-reset.png`,
		title: 'AR追踪重试...',
		tips: '继续等待可能要较长时间，您也可退出重新进入',
		show: false,
		duration: 3000
	}
}
