<template>
	<view class="container" :style="{ paddingTop: navBarInfo.navHeight + 'px' }">
		<navbar
			:title="locationName"
			show-nav-back
			is-landscape
			:opacity="1"
			:on-nav-click="onNavBack"
		/>
		<xr-ar-plane
			id="main-frame"
			ref="arRef"
			disable-scroll
			:width="frameParams.renderWidth"
			:height="frameParams.renderHeight"
			:style="frameStyle"
			:initial-vps-tracked="initialVpsTracked"
			:transform-matrix="transformMatrix"
			:on-ar-ready="handleArReady"
			:on-ar-init="handleArInit"
			:on-ar-tracked="handleArTracked"
			:on-ar-lost="handleArLost"
			:on-camera-pose-tick="handleCameraPoseTick"
			:asset-list="assetList"
			:on-asset-list-loaded="handleAssetListLoaded"
		/>
		<!-- 重定位按钮 -->
		<image
			v-if="!showExperienceButton"
			:src="`${imgBaseUrl}visual/visual-refresh-btn.png`"
			class="relocate-button"
			:style="{
				top: navBarInfo.navHeight + 16 + 'px'
			}"
			@click="requestVps"
		/>
		<!-- 按钮容器 -->
		<view class="button-container">
			<view v-if="showExperienceButton" class="vps-button" @click="startExperience">
				<text>开始体验</text>
			</view>
			<image
				v-if="showScreenshotButton"
				:src="`${imgBaseUrl}visual/visual-ar-screenshot-btn.png`"
				class="screenshot-button"
				@click="takeScreenshot"
			/>
		</view>

		<!-- 截图预览组件 -->
		<screenshot-preview
			:visible="showScreenshotPreview"
			:screenshot-data="screenshotData"
			:location-name="locationName"
			@close="closeScreenshotPreview"
			@save="handleScreenshotSave"
		/>

		<!-- 保存用的隐藏canvas -->
		<canvas canvas-id="saveCanvas" class="save-canvas" :style="{ width: '420px', height: '236px' }">
		</canvas>
		<custom-notice v-model:notice="noticeConfig" />
	</view>
</template>

<script setup>
import { reactive, computed, ref } from 'vue'
import { useStore } from 'vuex'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import {
	calculateTransformationMatrix,
	radianToAngle,
	matrixLeftToRightMatrixY,
	matrixToPose
} from '@/wxcomponents/utils/transformUtils'
import { yuvToImage, cropImageByRatio, saveBufferToFile } from '@/wxcomponents/utils/yuvImageUtils'
import { AnchorData, AssetData } from './anchor-data'
import { FormData } from '@/utils/form-data/formData'
import VpsService from '@/service/vps'
import performanceMonitor from '@/utils/performanceMonitor'
import ScreenshotPreview from '@/components/screenshot-preview/screenshot-preview.vue'
import CustomNotice from '@/components/notice/notice.vue'
import { noticeConfigs, NoticeEnum } from './settings/config'
import { imgBaseUrl } from '@/config'

const store = useStore()

const xr = wx.getXrFrameSystem()
const arRef = ref(null)
const lastVpsRoamingSuccess = ref(false)
const arTracked = ref(false)
const transformMatrix = ref(null)
const initialVpsTracked = ref(false)
const userPose = ref(null)
const arRawInfo = ref(null)
const anchorList = ref([])
const currentProjectId = ref('1939')
const currentLongitude = ref('116.329943')
const currentLatitude = ref('39.992194')
const assetList = ref([])
const assetListLoaded = ref(false)
const noticeConfig = ref(noticeConfigs[NoticeEnum.ArIniting])
const locationName = ref('')

// vps按钮相关状态
const isVpsExperienceClicked = ref(false)
// 截图相关状态
const showScreenshotPreview = ref(false)
const screenshotData = ref('')

const RequestStatus = {
	IDLE: 'idle',
	PENDING: 'pending',
	SUCCESS: 'success',
	FAIL: 'fail',
	ABORTED: 'aborted'
}

const requestVpsTask = ref(null)
const requestStatus = ref(RequestStatus.IDLE)

// 添加倒计时相关的状态
const countdownText = ref('开始体验')
const isCountingDown = ref(false)
const vpsRetryCount = ref(0)
const MAX_RETRY_COUNT = 2

const frameParams = reactive({
	width: 0,
	height: 0,
	renderWidth: 0,
	renderHeight: 0
})

const navBarInfo = computed(() => store.getters.navbarInfo)
const showExperienceButton = computed(
	() => !initialVpsTracked.value && !isVpsExperienceClicked.value
)
const showScreenshotButton = computed(() => initialVpsTracked.value && !showScreenshotPreview.value)
const frameStyle = computed(() => ({
	width: frameParams.width + 'px',
	height: frameParams.height + 'px',
	top: 0,
	left: 0,
	display: 'block'
}))

onLoad(async (options) => {
	currentProjectId.value = options.projectId
	locationName.value = options.locationName
	try {
		const locationObj = JSON.parse(options.location)
		currentLongitude.value = locationObj.longitude
		currentLatitude.value = locationObj.latitude
	} catch (error) {
		console.error('location is not a valid JSON string', error)
	}
	const { windowWidth, windowHeight, pixelRatio } = uni.getWindowInfo()

	console.log(
		'windowWidth: ',
		windowWidth,
		'windowHeight: ',
		windowHeight,
		'pixelRatio: ',
		pixelRatio
	)
	frameParams.width = windowWidth
	frameParams.height = windowHeight
	frameParams.renderWidth = windowWidth * pixelRatio
	frameParams.renderHeight = windowHeight * pixelRatio

	uni.showLoading({
		title: '资产加载中'
	})

	// 预加载项目下所有资产
	await loadProjectAssets(currentProjectId.value)

	// 初始化性能监控
	initPerformanceMonitor()
})

onUnload(() => {
	// 取消进行中的VPS请求
	if (requestVpsTask.value && requestStatus.value === RequestStatus.PENDING) {
		requestVpsTask.value.abort()
		requestVpsTask.value = null
	}

	// 停止性能监控
	performanceMonitor.stopMonitoring()

	// 清理AR相关引用
	cleanupArResources()

	// 强制垃圾回收
	if (typeof wx !== 'undefined' && wx.triggerGC) {
		wx.triggerGC()
	}
})

const onNavBack = () => {
	uni.navigateBack({
		delta: 1
	})
}

const tryGetFirstGlb = (modelUrls) => {
	const firstGlb = (modelUrls || []).find(
		(url) => url.toLowerCase().endsWith('.glb') || url.toLowerCase().endsWith('.gltf')
	)
	return firstGlb
}

<<<<<<< .merge_file_r1Kvsx
/**
 * 优化的资产加载函数，添加内存管理
 */
const loadProjectAssets = async (projectId) => {
	try {
		// 在加载新资产前，先清理旧资产
		if (assetList.value.length > 0) {
			console.log('清理旧资产列表...')
			assetList.value = []
			// 触发垃圾回收
			if (typeof wx !== 'undefined' && wx.triggerGC) {
				wx.triggerGC()
			}
		}

		const { data: assetsData } = await VpsService.getAssetList(projectId)
		assetList.value = (assetsData || [])
			.filter((asset) => tryGetFirstGlb(asset.models))
			.map((asset) => new AssetData(asset.assetId, 'gltf', tryGetFirstGlb(asset.models)))
	} catch (error) {
		console.error(`加载项目 ${projectId} 资产时出错:`, error)
		assetList.value = []
		setCustomNotice(NoticeEnum.ProjectLoadFailed)
	}
}

=======
>>>>>>> .merge_file_XjJjR5
// AR摄像头已启用
const handleArReady = async () => {
	setCustomNotice(NoticeEnum.ArIniting)
}

// 初次预加载资产列表成功
const handleAssetListLoaded = async () => {
	assetListLoaded.value = true
	uni.hideLoading()
}

// SLAM追踪初始化完毕
const handleArInit = () => {
	arTracked.value = true
	uni.hideLoading()
}

// SLAM丢失
const handleArLost = () => {
	arTracked.value = false
	setCustomNotice(NoticeEnum.ArTrackingRestoring)
}
// SLAM丢失后，AR再次初始化成功
const handleArTracked = async () => {
	arTracked.value = true
	uni.hideLoading()
	if (initialVpsTracked.value) {
		await requestVps()
	}
}

// 性能监控相关函数
const initPerformanceMonitor = () => {
	// 重置性能监控器状态
	performanceMonitor.reset()

	// 设置性能监控回调
	performanceMonitor.setCallbacks({
		onPerformanceAlert: (type, data) => {
			handlePerformanceAlert(type, data)
		},
		onMemoryWarning: (warningCount) => {
			console.warn(`内存警告 #${warningCount}，尝试清理资源`)
			// 主动清理一些可释放的资源
			cleanupOptionalResources()
		}
	})

	// 开始监控
	performanceMonitor.startMonitoring()
	console.log('性能监控已初始化')
}

const handlePerformanceAlert = (type) => {
	if (type === 'critical_fps' || type === 'memory_warning') {
		setCustomNotice(NoticeEnum.DeviceMemoryLow)
		return
	}

	if (type === 'low_fps') {
		setCustomNotice(NoticeEnum.FrameRateLow, false)
		return
	}
}

/**
 * 清理可选资源，在内存警告时调用
 */
const cleanupOptionalResources = () => {
	// 清理截图数据
	if (screenshotData.value) {
		screenshotData.value = ''
		console.log('已清理截图数据')
	}

	// 清理历史AR原始数据
	if (arRawInfo.value) {
		// 保留最新的，清理历史缓存
		console.log('已清理AR原始数据缓存')
	}

	// 强制垃圾回收
	if (typeof wx !== 'undefined' && wx.triggerGC) {
		wx.triggerGC()
		console.log('已触发垃圾回收')
	}
}

// 开始体验
const startExperience = () => {
	isVpsExperienceClicked.value = true
	requestVps()
}

// 用于记录VPS纠偏后的相机位姿
const handleCameraPoseTick = (evt) => {
	//console.log('handleCameraPoseTick: '+JSON.stringify(evt));
	const { cameraPos, cameraQuat, arRawData } = evt

	if (!arRawInfo.value) {
		arRawInfo.value = arRawData
	}
	userPose.value = {
		position: { x: cameraPos.x, y: cameraPos.y, z: cameraPos.z },
		quaternion: cameraQuat
	}
}
// 申请初始定位
const requestVps = async () => {
	if (!arTracked.value) {
		console.log('AR未追踪，跳过VPS请求')
		return
	}

	// 检查是否有进行中的请求
	if (requestStatus.value === RequestStatus.PENDING) {
		console.log('VPS请求进行中，跳过重复请求')
		return
	}

	//此处使用原始相机的位姿，是因为原始相机位姿在经历1.与相机原点纠偏操作，2并对齐UniCity的右手系位姿一致
	let queryPose = {
		position: xr.Vector3.createFromNumber(
			userPose.value.position.x,
			userPose.value.position.y,
			userPose.value.position.z
		),
		quaternion: userPose.value.quaternion
	}

	try {
		requestVpsTask.value = await saveCurrentFrame(queryPose)
	} catch (error) {
		console.error('VPS请求失败:', error)
		requestStatus.value = RequestStatus.FAIL
	}
}

const saveCurrentFrame = async (queryPose) => {
	let imageBuffer = null
	let formData = null

	try {
		console.warn('saveCurrentFrame: ', arRawInfo.value)
		if (!arRawInfo.value) {
			console.log('arRawInfo is empty')
			return
		}
		uni.showLoading({
			title: '定位中...'
		})

		const { yBuffer, uvBuffer, width, height, intrinsics } = arRawInfo.value
		// 图像处理，注意内存管理
		imageBuffer = await yuvToImage(yBuffer, uvBuffer, width, height)

		const vpsIntrinsics = [
			intrinsics[0],
			intrinsics[4],
			intrinsics[6],
			intrinsics[7],
			width,
			height
		]

		formData = new FormData()
		formData.appendFile('file', imageBuffer, 'pic.jpg')
		formData.append('latitude', currentLatitude.value)
		formData.append('longitude', currentLongitude.value)
		formData.append('projectId', currentProjectId.value)
		formData.append('userId', '6') // TODO: 替换真实用户ID
		formData.append('type', 2)
		formData.append('intrinsics', vpsIntrinsics)

		const requestData = formData.getData()
		console.warn('requestData: ', JSON.stringify(requestData), requestData)
		requestStatus.value = RequestStatus.PENDING
		const { data: vpsInfo } = await VpsService.vpsRoam({
			params: requestData.buffer,
			contentType: requestData.contentType
		})

		console.warn('vpsInfo: ', vpsInfo)
		requestStatus.value = RequestStatus.SUCCESS

		const vpsPosition = { x: vpsInfo.tcw[0], y: vpsInfo.tcw[1], z: vpsInfo.tcw[2] }
		const vpsQuaternion = xr.Quaternion.createFromNumber(
			vpsInfo.qcw[1],
			vpsInfo.qcw[2],
			vpsInfo.qcw[3],
			vpsInfo.qcw[0]
		)
		const vpsPose = {
			position: xr.Vector3.createFromNumber(vpsPosition.x, vpsPosition.y, vpsPosition.z),
			quaternion: vpsQuaternion
		}
		const cameraEuler = queryPose.quaternion.toEulerAngles()
		console.log(
			'--------queryPose的值是------: position: x: ' +
				queryPose.position.x +
				', y: ' +
				queryPose.position.y +
				', z: ' +
				queryPose.position.z +
				', rotation: x: ' +
				radianToAngle(cameraEuler.x) +
				', y: ' +
				radianToAngle(cameraEuler.y) +
				', z: ' +
				radianToAngle(cameraEuler.z)
		)

		console.log(
			' --------vpsPose的值是------:' +
				JSON.stringify(vpsPose.position) +
				',' +
				'vpsPose rotation:' +
				JSON.stringify(vpsPose.quaternion.toEulerAngles())
		)

		transformMatrix.value = calculateTransformationMatrix(queryPose, vpsPose)
		vpsRetryCount.value = 0
		lastVpsRoamingSuccess.value = true

		uni.hideLoading()
		uni.showToast({
			title: '定位成功',
			icon: 'success'
		})
		const vpsAnchorList = vpsInfo.deltaPositionList
		const anchors = []
		vpsAnchorList.forEach((anchor) => {
			const anchorPositionArray = JSON.parse(anchor.position)
			const anchorRotationArray = JSON.parse(anchor.rotation)
			const anchorScaleArray = JSON.parse(anchor.scale)
			//从云测返回的anchor位姿
			const vpsAnchorPose = {
				position: xr.Vector3.createFromNumber(
					anchorPositionArray[0],
					anchorPositionArray[1],
					anchorPositionArray[2]
				),
				quaternion: xr.Quaternion.createFromNumber(
					anchorRotationArray[1],
					anchorRotationArray[2],
					anchorRotationArray[3],
					anchorRotationArray[0]
				)
			}
			console.log('--------Anchor的VPSPose是------：' + JSON.stringify(vpsAnchorPose))
			const scale = { x: anchorScaleArray[0], y: anchorScaleArray[1], z: anchorScaleArray[2] }

			let vpsAnchorMatrix = xr.Matrix4.composeTQS(
				vpsAnchorPose.position,
				vpsAnchorPose.quaternion,
				xr.Vector3.ONE
			)

			let Tmatrix = new xr.Matrix4()
			Tmatrix.setArray(transformMatrix.value)
			console.log('--------Anchor的转换矩阵是------：' + JSON.stringify(matrixToPose(Tmatrix)))
			let slam_anchor_matrix = Tmatrix.multiply(vpsAnchorMatrix)

			let convertedMatrix = matrixLeftToRightMatrixY(slam_anchor_matrix)

			let convertedPose = matrixToPose(convertedMatrix)

			const convertedEuler = convertedPose.quaternion.toEulerAngles()
			const transformedPose2 = {
				position: {
					x: convertedPose.position.x,
					y: convertedPose.position.y,
					z: convertedPose.position.z
				},
				rotation: { x: convertedEuler.x, y: convertedEuler.y, z: convertedEuler.z }
			}
			//console.log('transformedPose2: ' + JSON.stringify(transformedPose2))
			const anchorUrl = tryGetFirstGlb(anchor.models)
			if (anchorUrl) {
				const anchorData = new AnchorData(
					anchor.anchorId,
					'anchor-' + anchor.anchorId,
					transformedPose2.position,
					transformedPose2.rotation,
					scale,
					anchor.assetId,
					anchorUrl
				)
				anchors.push(anchorData)
			}
		})
		initialVpsTracked.value = true
		anchorList.value = anchors
		await refreshAnchorList()
	} catch (err) {
		console.log(JSON.stringify(err) + ', retry saveCurrentFrame')
		uni.hideLoading()
		requestStatus.value = RequestStatus.FAIL

		// 检查重试次数
		if (vpsRetryCount.value < MAX_RETRY_COUNT) {
			vpsRetryCount.value++

			// setCustomNotice(NoticeEnum.LocationFailed)

			lastVpsRoamingSuccess.value = false

			// 直接重试，不设置延迟
			await saveCurrentFrame(queryPose)
		} else {
			// 达到最大重试次数
			setCustomNotice(NoticeEnum.LocationFailed)

			// 重置计数器，为下一次手动尝试做准备
			vpsRetryCount.value = 0
			lastVpsRoamingSuccess.value = false
		}
	} finally {
		// 清理临时资源，防止内存泄漏
		if (imageBuffer) {
			imageBuffer = null
		}
		if (formData) {
			formData = null
		}
		// 触发垃圾回收
		if (typeof wx !== 'undefined' && wx.triggerGC) {
			wx.triggerGC()
		}
	}
}

const refreshAnchorList = async () => {
	if (!arRef.value) {
		console.log('arRef is undefined')
		return
	}

	if (anchorList.value && anchorList.value.length > 0) {
		if (!assetListLoaded.value) {
			uni.showLoading({ title: '资产加载中' })
		}
		const anchorSpawnPromises = anchorList.value.map((anchor) => {
			console.log(
				'anchor' +
					anchor.id +
					' position: ' +
					JSON.stringify(anchor.position) +
					', rotation: x: ' +
					anchor.rotation.x +
					', y: ' +
					anchor.rotation.y +
					', z: ' +
					anchor.rotation.z
			)
			return arRef.value.spawnAnchorItem(anchor)
		})
		Promise.all(anchorSpawnPromises)
			.then(() => {
				assetListLoaded.value = true
			})
			.catch(() => {
				setCustomNotice(NoticeEnum.ProjectLoadFailed)
				assetListLoaded.value = false
			})
			.finally(() => {
				uni.hideLoading()
			})
	} else {
		console.log('anchorList is empty')
	}
}

const setCustomNotice = (noticeType, show = true) => {
	noticeConfig.value = { ...noticeConfigs[noticeType], show }
}

const takeScreenshot = async () => {
	if (!arRef.value) {
		console.log('arRef is undefined')
		return
	}
	try {
		// 获取截图
		const base64 = await arRef.value.takeScreenshot()
		screenshotData.value = base64
		// 显示截图预览
		showScreenshotPreview.value = true
	} catch (error) {
		console.error('截图失败:', error)
		uni.showToast({
			title: '截图失败',
			icon: 'error'
		})
	}
}

// 关闭截图预览
const closeScreenshotPreview = () => {
	showScreenshotPreview.value = false
}

// 处理截图保存
const handleScreenshotSave = (savedPath) => {
	console.log('截图已保存:', savedPath)
	closeScreenshotPreview()
}

/**
 * 清理AR相关资源，防止内存泄漏
 */
const cleanupArResources = () => {
	console.log('开始清理AR相关资源...')

	// 清理引用变量
	transformMatrix.value = null
	userPose.value = null
	arRawInfo.value = null
	anchorList.value = []
	assetList.value = []
	screenshotData.value = ''

	// 重置状态
	initialVpsTracked.value = false
	lastVpsRoamingSuccess.value = false
	arTracked.value = false
	assetListLoaded.value = false
	isVpsExperienceClicked.value = false
	showScreenshotPreview.value = false

	// 重置VPS相关状态
	requestStatus.value = RequestStatus.IDLE
	vpsRetryCount.value = 0
	countdownText.value = '开始体验'
	isCountingDown.value = false

	console.log('AR相关资源清理完成')
}
<<<<<<< .merge_file_r1Kvsx
=======

/**
 * 优化的资产加载函数，添加内存管理
 */
const loadProjectAssetsOptimized = async (projectId) => {
	try {
		// 在加载新资产前，先清理旧资产
		if (assetList.value.length > 0) {
			console.log('清理旧资产列表...')
			assetList.value = []
			// 触发垃圾回收
			if (typeof wx !== 'undefined' && wx.triggerGC) {
				wx.triggerGC()
			}
		}

		const { data: assetsData } = await VpsService.getAssetList(projectId)
		assetList.value = (assetsData || [])
			.filter((asset) => tryGetFirstGlb(asset.models))
			.map((asset) => new AssetData(asset.assetId, 'gltf', tryGetFirstGlb(asset.models)))

		console.log(`成功加载 ${assetList.value.length} 个资产`)
	} catch (error) {
		console.error(`加载项目 ${projectId} 资产时出错:`, error)
		assetList.value = []
		setCustomNotice(NoticeEnum.ProjectLoadFailed)
	}
}
>>>>>>> .merge_file_XjJjR5
</script>

<style scoped>
.screenshot-image {
	position: absolute;
	top: 32rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 840rpx;
	height: 466rpx;
	z-index: 1000;
}

.container {
	width: 100vw;
	height: 100vh;
	position: relative;
	box-sizing: border-box;
}

.relocate-button {
	position: absolute;
	right: 21px;
	width: 48px;
	height: 48px;
	z-index: 1000;
}

.button-container {
	display: flex;
	justify-content: center;
	align-items: flex-end;
	padding-bottom: 24px;
	box-sizing: border-box;
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 72px;
	z-index: 1000;
}

.screenshot-button {
	width: 64px;
	height: 64px;
}

.vps-button {
	width: 109px;
	height: 36px;
	border-radius: 44px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #ffffff;
	font-size: 17px;
	font-weight: 500;
	letter-spacing: 1px;
	box-shadow: 1px 1px 4.5px 0px #40720426;
	font-family: Douyin Sans;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/visual/visual-ar-save-btn.png');
	background-size: cover;
	background-repeat: no-repeat;
}

.save-canvas {
	position: absolute;
	top: -9999px;
	left: -9999px;
	opacity: 0;
}
</style>
