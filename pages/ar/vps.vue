<template>
	<view class="container" :style="{ paddingTop: navBarInfo.navHeight + 'px' }">
		<navbar
			:title="locationName"
			show-nav-back
			is-landscape
			:opacity="1"
			:on-nav-click="onNavBack"
		/>
		<xr-ar-plane
			id="main-frame"
			ref="arRef"
			disable-scroll
			:width="frameParams.renderWidth"
			:height="frameParams.renderHeight"
			:style="frameStyle"
			:initial-vps-tracked="initialVpsTracked"
			:transform-matrix="transformMatrix"
			:on-ar-ready="handleArReady"
			:on-ar-init="handleArInit"
			:on-ar-tracked="handleArTracked"
			:on-ar-lost="handleArLost"
			:on-camera-pose-tick="handleCameraPoseTick"
			:asset-list="assetList"
			:on-asset-list-loaded="handleAssetListLoaded"
		/>
		<!-- 重定位按钮 -->
		<image
			:src="`${imgBaseUrl}/visual/visual-refresh-btn.png`"
			class="relocate-button"
			@click="requestVps"
		/>
		<!-- 按钮容器 -->
		<view class="button-container">
			<!-- VPS定位按钮 -->
			<!-- <view v-if="!initialVpsTracked" class="vps-button" @click="startCountdown">
				<text>{{ countdownText }}</text>
			</view> -->
			<view
				v-if="!initialVpsTracked && !isVpsExperienceClicked"
				class="vps-button"
				@click="startExperience"
			>
				<text>开始体验</text>
			</view>
			<image
				:src="`${imgBaseUrl}/visual/visual-ar-screenshot-btn.png`"
				class="screenshot-button"
				@click="takeScreenshot"
			/>
		</view>

		<!-- 截图预览组件 -->
		<screenshot-preview
			:visible="showScreenshotPreview"
			:screenshot-data="screenshotData"
			:location-name="locationName"
			@close="closeScreenshotPreview"
			@save="handleScreenshotSave"
		/>

		<!-- 保存用的隐藏canvas -->
		<canvas canvas-id="saveCanvas" class="save-canvas" :style="{ width: '420px', height: '236px' }">
		</canvas>
	</view>
</template>

<script setup>
import { reactive, computed, ref } from 'vue'
import { useStore } from 'vuex'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import {
	calculateTransformationMatrix,
	radianToAngle,
	matrixLeftToRightMatrixY,
	matrixToPose
} from '@/wxcomponents/utils/transformUtils'
import { yuvToImage, cropImageByRatio, saveBufferToFile } from '@/wxcomponents/utils/yuvImageUtils'
import { AnchorData, AssetData } from './anchor-data'
import { FormData } from '@/utils/form-data/formData'
import VpsService from '@/service/vps'
import performanceMonitor from '@/utils/performanceMonitor'
import ScreenshotPreview from '@/components/screenshot-preview/screenshot-preview.vue'

const store = useStore()

const xr = wx.getXrFrameSystem()
const arRef = ref(null)
const lastVpsRoamingSuccess = ref(false)
const arTracked = ref(false)
const transformMatrix = ref(null)
const initialVpsTracked = ref(false)
const userPose = ref(null)
const arRawInfo = ref(null)
const anchorList = ref([])
const currentProjectId = ref('1939')
const currentLongitude = ref('116.329943')
const currentLatitude = ref('39.992194')
const assetList = ref([])
const assetListLoaded = ref(false)

// vps按钮相关状态
const isVpsExperienceClicked = ref(false)
// 截图相关状态
const showScreenshotPreview = ref(false)
const screenshotData = ref('')

const RequestStatus = {
	IDLE: 'idle',
	PENDING: 'pending',
	SUCCESS: 'success',
	FAIL: 'fail',
	ABORTED: 'aborted'
}

const requestVpsTask = ref(null)
const requestStatus = ref(RequestStatus.IDLE)

// 添加倒计时相关的状态
const countdownText = ref('开始体验')
const isCountingDown = ref(false)
const countdownInterval = ref(null)
const vpsRetryCount = ref(0)
const MAX_RETRY_COUNT = 2

const frameParams = reactive({
	width: 0,
	height: 0,
	renderWidth: 0,
	renderHeight: 0
})

const navBarInfo = computed(() => store.getters.navbarInfo)

const frameStyle = computed(() => ({
	width: frameParams.width + 'px',
	height: frameParams.height + 'px',
	top: 0,
	left: 0,
	display: 'block'
}))

onLoad(async (options) => {
	console.log('options', options)
	const { projectId, location, locationName } = options
	currentProjectId.value = projectId
	locationName.value = locationName
	try {
		const locationObj = JSON.parse(location)
		currentLongitude.value = locationObj.longitude
		currentLatitude.value = locationObj.latitude
	} catch (error) {
		console.error('location is not a valid JSON string', error)
	}
	const { windowWidth, windowHeight, pixelRatio } = uni.getWindowInfo()
	frameParams.width = windowWidth
	frameParams.height = windowHeight
	frameParams.renderWidth = windowWidth * pixelRatio
	frameParams.renderHeight = windowHeight * pixelRatio

	uni.showLoading({
		title: '资产加载中'
	})

	// 预加载项目下所有资产
	await loadProjectAssets(currentProjectId.value)

	// 初始化性能监控
	initPerformanceMonitor()
})

onUnload(() => {
	if (countdownInterval.value) {
		clearInterval(countdownInterval.value)
		countdownInterval.value = null
	}
	if (requestVpsTask.value && requestStatus.value === RequestStatus.PENDING) {
		requestVpsTask.value.abort()
	}
	// 停止性能监控
	performanceMonitor.stopMonitoring()
})

const onNavBack = () => {
	uni.navigateBack({
		delta: 1
	})
}

const tryGetFirstGlb = (modelUrls) => {
	const firstGlb = (modelUrls || []).find(
		(url) => url.toLowerCase().endsWith('.glb') || url.toLowerCase().endsWith('.gltf')
	)
	return firstGlb
}

const loadProjectAssets = async (projectId) => {
	try {
		const { data: assetsData } = await VpsService.getAssetList(projectId)
		assetList.value = (assetsData || [])
			.filter((asset) => tryGetFirstGlb(asset.models))
			.map((asset) => new AssetData(asset.assetId, 'gltf', tryGetFirstGlb(asset.models)))
	} catch (error) {
		console.error(`加载项目 ${projectId} 资产时出错:`, error)
		assetList.value = []
		uni.showToast({
			title: '加载项目资产失败',
			icon: 'none',
			duration: 2000
		})
	}
}

// AR摄像头已启用
const handleArReady = async () => {
	uni.showLoading({
		title: 'AR初始化中'
	})
}

// 初次预加载资产列表成功
const handleAssetListLoaded = async () => {
	assetListLoaded.value = true
	uni.hideLoading()
}

// SLAM追踪初始化完毕
const handleArInit = () => {
	arTracked.value = true
	uni.hideLoading()
}

// SLAM丢失
const handleArLost = () => {
	arTracked.value = false
	uni.showLoading({
		title: 'AR追踪中'
	})
}
// SLAM丢失后，AR再次初始化成功
const handleArTracked = async () => {
	arTracked.value = true
	uni.hideLoading()
	if (initialVpsTracked.value) {
		await requestVps()
	}
}

// 性能监控相关函数
const initPerformanceMonitor = () => {
	// 设置性能监控回调
	performanceMonitor.setCallbacks({
		onPerformanceAlert: (type, data) => {
			handlePerformanceAlert(type, data)
		}
	})

	// 开始监控
	performanceMonitor.startMonitoring()
	console.log('性能监控已初始化')
}

const handlePerformanceAlert = (type, data) => {
	let title = '性能提醒'
	let content = data.message
	let showCancel = true

	switch (type) {
		case 'critical_fps':
			title = '严重性能警告'
			showCancel = false
			break
		case 'low_fps':
			title = '性能警告'
			break
		case 'memory_warning':
			title = '内存不足警告'
			showCancel = false
			break
	}

	if (type === 'low_fps') {
		wx.showToast({
			title: content,
			icon: 'none',
			duration: 2000
		})
		return
	}

	wx.showModal({
		title,
		content,
		showCancel,
		confirmText: '知道了',
		cancelText: '忽略',
		success: (res) => {
			if (res.confirm) {
				console.log('用户确认性能警告')
			}
		}
	})
}

// 开始体验
const startExperience = () => {
	isVpsExperienceClicked.value = true
	requestVps()
		.then(() => {
			// 定位完成后重置按钮文字
			setTimeout(() => {
				countdownText.value = 'VPS定位'
				isCountingDown.value = false
			}, 1000)
		})
		.catch((err) => {
			console.error('VPS定位失败:', err)
			countdownText.value = 'VPS定位'
			isCountingDown.value = false
		})
}

// 用于记录VPS纠偏后的相机位姿
const handleCameraPoseTick = (evt) => {
	//console.log('handleCameraPoseTick: '+JSON.stringify(evt));
	const { cameraPos, cameraQuat, arRawData } = evt

	if (!arRawInfo.value) {
		arRawInfo.value = arRawData
	}
	userPose.value = {
		position: { x: cameraPos.x, y: cameraPos.y, z: cameraPos.z },
		quaternion: cameraQuat
	}
}
// 申请初始定位
const requestVps = async () => {
	if (!arTracked.value) {
		return
	}
	//此处使用原始相机的位姿，是因为原始相机位姿在经历1.与相机原点纠偏操作，2并对齐UniCity的右手系位姿一致
	let queryPose = {
		position: xr.Vector3.createFromNumber(
			userPose.value.position.x,
			userPose.value.position.y,
			userPose.value.position.z
		),
		quaternion: userPose.value.quaternion
	}

	requestVpsTask.value = await saveCurrentFrame(queryPose)
}

const saveCurrentFrame = async (queryPose) => {
	try {
		console.warn('saveCurrentFrame: ', arRawInfo.value)
		if (!arRawInfo.value) {
			console.log('arRawInfo is empty')
			return
		}
		uni.showLoading({
			title: '定位中...'
		})
		const { yBuffer, uvBuffer, width, height, intrinsics } = arRawInfo.value
		console.warn('yBuffer: ', yBuffer)
		const imageBuffer = await yuvToImage(yBuffer, uvBuffer, width, height)
		console.warn('imageBuffer: ', imageBuffer)
		const vpsIntrinsics = [
			intrinsics[0],
			intrinsics[4],
			intrinsics[6],
			intrinsics[7],
			width,
			height
		]

		const formData = new FormData()
		formData.appendFile('file', imageBuffer, 'pic.jpg')
		formData.append('latitude', currentLatitude.value)
		formData.append('longitude', currentLongitude.value)
		formData.append('projectId', currentProjectId.value)
		formData.append('userId', '6') // TODO: 替换真实用户ID
		formData.append('type', 2)
		formData.append('intrinsics', vpsIntrinsics)

		const requestData = formData.getData()
		console.warn('requestData: ', JSON.stringify(requestData), requestData)
		requestStatus.value = RequestStatus.PENDING
		const { data: vpsInfo } = await VpsService.vpsRoam({
			params: requestData.buffer,
			contentType: requestData.contentType
		})

		console.warn('vpsInfo: ', vpsInfo)
		requestStatus.value = RequestStatus.SUCCESS

		const vpsPosition = { x: vpsInfo.tcw[0], y: vpsInfo.tcw[1], z: vpsInfo.tcw[2] }
		const vpsQuaternion = xr.Quaternion.createFromNumber(
			vpsInfo.qcw[1],
			vpsInfo.qcw[2],
			vpsInfo.qcw[3],
			vpsInfo.qcw[0]
		)
		const vpsPose = {
			position: xr.Vector3.createFromNumber(vpsPosition.x, vpsPosition.y, vpsPosition.z),
			quaternion: vpsQuaternion
		}
		const cameraEuler = queryPose.quaternion.toEulerAngles()
		console.log(
			'--------queryPose的值是------: position: x: ' +
				queryPose.position.x +
				', y: ' +
				queryPose.position.y +
				', z: ' +
				queryPose.position.z +
				', rotation: x: ' +
				radianToAngle(cameraEuler.x) +
				', y: ' +
				radianToAngle(cameraEuler.y) +
				', z: ' +
				radianToAngle(cameraEuler.z)
		)

		console.log(
			' --------vpsPose的值是------:' +
				JSON.stringify(vpsPose.position) +
				',' +
				'vpsPose rotation:' +
				JSON.stringify(vpsPose.quaternion.toEulerAngles())
		)

		transformMatrix.value = calculateTransformationMatrix(queryPose, vpsPose)
		vpsRetryCount.value = 0
		lastVpsRoamingSuccess.value = true

		uni.hideLoading()
		uni.showToast({
			title: '定位成功',
			icon: 'success'
		})
		const vpsAnchorList = vpsInfo.deltaPositionList
		const anchors = []
		vpsAnchorList.forEach((anchor) => {
			const anchorPositionArray = JSON.parse(anchor.position)
			const anchorRotationArray = JSON.parse(anchor.rotation)
			const anchorScaleArray = JSON.parse(anchor.scale)
			//从云测返回的anchor位姿
			const vpsAnchorPose = {
				position: xr.Vector3.createFromNumber(
					anchorPositionArray[0],
					anchorPositionArray[1],
					anchorPositionArray[2]
				),
				quaternion: xr.Quaternion.createFromNumber(
					anchorRotationArray[1],
					anchorRotationArray[2],
					anchorRotationArray[3],
					anchorRotationArray[0]
				)
			}
			console.log('--------Anchor的VPSPose是------：' + JSON.stringify(vpsAnchorPose))
			const scale = { x: anchorScaleArray[0], y: anchorScaleArray[1], z: anchorScaleArray[2] }

			let vpsAnchorMatrix = xr.Matrix4.composeTQS(
				vpsAnchorPose.position,
				vpsAnchorPose.quaternion,
				xr.Vector3.ONE
			)

			let Tmatrix = new xr.Matrix4()
			Tmatrix.setArray(transformMatrix.value)
			console.log('--------Anchor的转换矩阵是------：' + JSON.stringify(matrixToPose(Tmatrix)))
			let slam_anchor_matrix = Tmatrix.multiply(vpsAnchorMatrix)

			let convertedMatrix = matrixLeftToRightMatrixY(slam_anchor_matrix)

			let convertedPose = matrixToPose(convertedMatrix)

			const convertedEuler = convertedPose.quaternion.toEulerAngles()
			const transformedPose2 = {
				position: {
					x: convertedPose.position.x,
					y: convertedPose.position.y,
					z: convertedPose.position.z
				},
				rotation: { x: convertedEuler.x, y: convertedEuler.y, z: convertedEuler.z }
			}
			//console.log('transformedPose2: ' + JSON.stringify(transformedPose2))
			const anchorUrl = tryGetFirstGlb(anchor.models)
			if (anchorUrl) {
				const anchorData = new AnchorData(
					anchor.anchorId,
					'anchor-' + anchor.anchorId,
					transformedPose2.position,
					transformedPose2.rotation,
					scale,
					anchor.assetId,
					anchorUrl
				)
				anchors.push(anchorData)
			}
		})
		initialVpsTracked.value = true
		anchorList.value = anchors
		await refreshAnchorList()
	} catch (err) {
		console.log(JSON.stringify(err) + ', retry saveCurrentFrame')
		uni.hideLoading()
		requestStatus.value = RequestStatus.FAIL

		// 检查重试次数
		if (vpsRetryCount.value < MAX_RETRY_COUNT) {
			vpsRetryCount.value++

			uni.showToast({
				title: `定位失败`,
				icon: 'none',
				duration: 1000
			})

			lastVpsRoamingSuccess.value = false

			// 直接重试，不设置延迟
			await saveCurrentFrame(queryPose)
		} else {
			// 达到最大重试次数
			uni.showToast({
				title: '定位失败',
				icon: 'error',
				duration: 2000
			})

			// 重置计数器，为下一次手动尝试做准备
			vpsRetryCount.value = 0
			lastVpsRoamingSuccess.value = false

			// 更新按钮文字
			countdownText.value = 'VPS定位'
		}
	}
}

const refreshAnchorList = async () => {
	if (!arRef.value) {
		console.log('arRef is undefined')
		return
	}

	if (anchorList.value && anchorList.value.length > 0) {
		if (!assetListLoaded.value) {
			uni.showLoading({ title: '资产加载中' })
		}
		const anchorSpawnPromises = anchorList.value.map((anchor) => {
			console.log(
				'anchor' +
					anchor.id +
					' position: ' +
					JSON.stringify(anchor.position) +
					', rotation: x: ' +
					anchor.rotation.x +
					', y: ' +
					anchor.rotation.y +
					', z: ' +
					anchor.rotation.z
			)
			return arRef.value.spawnAnchorItem(anchor)
		})
		Promise.all(anchorSpawnPromises)
			.then(() => {
				assetListLoaded.value = true
			})
			.catch((err) => {
				uni.showToast({ title: '部分资产加载失败', icon: 'none' })
				assetListLoaded.value = false
			})
			.finally(() => {
				uni.hideLoading()
			})
	} else {
		console.log('anchorList is empty')
	}
}

const takeScreenshot = async () => {
	if (!arRef.value) {
		console.log('arRef is undefined')
		return
	}
	try {
		// 获取截图
		const base64 = await arRef.value.takeScreenshot()
		screenshotData.value = base64
		// 显示截图预览
		showScreenshotPreview.value = true
	} catch (error) {
		console.error('截图失败:', error)
		uni.showToast({
			title: '截图失败',
			icon: 'error'
		})
	}
}

// 关闭截图预览
const closeScreenshotPreview = () => {
	showScreenshotPreview.value = false
}

// 处理截图保存
const handleScreenshotSave = (savedPath) => {
	console.log('截图已保存:', savedPath)
	closeScreenshotPreview()
}
</script>

<style scoped>
.screenshot-image {
	position: absolute;
	top: 32rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 840rpx;
	height: 466rpx;
	z-index: 1000;
}

.container {
	width: 100vw;
	height: 100vh;
	position: relative;
}

.relocate-button {
	position: absolute;
	top: 16px;
	right: 21px;
	width: 48px;
	height: 48px;
}

.button-container {
	display: flex;
	justify-content: center;
	align-items: flex-end;
	padding-bottom: 24px;
}

.screenshot-button {
	width: 64px;
	height: 64px;
}

.vps-button {
	width: 109px;
	height: 36px;
	border-radius: 44px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #ffffff;
	font-size: 17px;
	font-weight: 500;
	letter-spacing: 1px;
	box-shadow: 1px 1px 4.5px 0px #40720426;
	font-family: Douyin Sans;
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/visual/visual-ar-save-btn.png');
	background-size: cover;
	background-repeat: no-repeat;
}

.save-canvas {
	position: absolute;
	top: -9999px;
	left: -9999px;
	opacity: 0;
}
</style>
