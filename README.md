## 水峪嘴村小程序

### 初始化项目
1. 申请水峪嘴村小程序开发者权限
2. 安装微信开发者工具，登录开发微信号
3. 安装HBuilderX
4. HBuilderX - 运行 - 运行到小程序模拟器 - 微信开发者工具

### 项目发布
1. 微信开发者工具 - 上传
2. 微信开发者平台 - 设置为体验版

### 目录结构

```
├── App.vue                   // 入口页面
├── api                       // 接口目录，项目中所有常规接口定义都放在这里（除去一些内置请求或者流式请求）；文件命名及分类按照pages下对应目录创建，如果有通用的则抽象到common.js
│   └── app.js                // 登录校验接口
├── components                // 页面组件，定义参考uniapp，uniapp中包含的组件建议直接使用，需要定制的再创建组件
├── config                    // 项目通用配置，一般用于保存项目接口地址以及图片地址、AppID等
├── index.html                // 入口页面，一般不做改动
├── main.js                   // 入口js，引入一些库或者工具做初始化
├── manifest.json             // uniapp配置文件，详情见官方文档
├── pages                     // 页面，页面下的目录及文件路径对应pages.json中的路由及tabs配置，一般按照tab拆分，特定可以拆分出完整流程的业务单独拆分（如guard）
│   ├── guard
│   ├── index
│   ├── manage
│   └── visual
├── pages.json                // 项目配置文件，配置路由、权限及默认tabbar，可参考uniapp文档
├── static
├── store                     // vuex文件
├── uni.promisify.adaptor.js  // 用于处理路由拦截，检测用户登录状态并在未登录时进行跳转。主要涉及到了`switchTab`拦截器的设置和在main.js中的导入使用。
├── uni.scss
├── uni_modules               // uniapp插件，不可删除，不可添加到gitignore
├── unpackage                 // 打包文件
├── .gitignore                // git忽略文件
└── utils                     // 通用函数
```