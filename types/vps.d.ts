import { FormData } from '@/utils/form-data/formData'
import { PageParams, IPaginationBase } from './base'

export interface RoamingParams {
	file: FormData
	projectId: string
	userId: string
	type: number
	intrinsics: number[]
	latitude: number
	longitude: number
}

export interface RoamingResponseData {
	projectId: number
	qcw: number[]
	tcw: number[]
	deltaPositionList: deltaPositionListList[]
}

export interface deltaPositionListList {
	anchorId: string
	anchorName: string
	anchorType: number
	anchorUrl: string
	assetId: string
	delta: number[]
	scale: number[]
	position: number[]
	rotation: number[]
}

// export interface AssetParams {
//     projectId: number
// }

export interface AssetResponseData {
	anchorId: string
	anchorType: number
	assetId: number
	delta: string
	isShow: number
	latitude: number
	longitude: number
	models: string[]
	placementUser: string
	position: string
	rotation: string
	scale: string
	textContent: string
	textStyle: string
}
