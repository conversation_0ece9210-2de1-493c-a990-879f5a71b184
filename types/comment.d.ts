import type { IPaginationBase } from './base'
export interface ICommentQuery {
	articleId: number
	pageNum: number
	pageSize: number
}

export interface ICommentItem {
	id: number
	content: string
	postTime: string
	likes: number
	userId: string
	avatarPic: string
	nickName: string
	liked: number
}

export interface IAddComments {
	articleId: number
	content: string
}

export type ICommentData = IPaginationBase<ICommentItem>
