// 守护API类型定义文件

import { PageParams, IPaginationBase } from './base'

export interface GuardMaintainParams {
	eventId: number
	guardType: number
	hoofprintId: number
}

export interface GuardIssueParams {
	title: string
	content: string
	hoofprintId: number
	latitude?: number
	location?: string
	longitude?: number
	urls?: string[]
	isLocal: number
}

export interface GuardIssueRecordListParams {
	pageNum: number
	pageSize: number
	onlyMe: number
	timeLimit: number
	isFeatured: number
	isLatest?: number
}

export interface PageQueryParams {
	pageNum: string
	pageSize: number
}

export interface GuardIssueResponseData {
	volunteer: string
	isVolunteer: boolean
}

export interface GuardBaseResponseData {
	guardMember: number
	hoofprintCode: string
	hoofprintId: number
	imageUrl: string
}
export interface GuardApplyResponseData extends GuardBaseResponseData {
	claimTime: string
}

export interface GuardEventResponseData extends GuardBaseResponseData {
	eventId: number
	guardType: number
	points: number
}

export interface GuardResponseData {
	points: number
	userId: string
	userName: string
}
export interface GuardMemberResponseData extends GuardResponseData {
	avatarPic: string
	hoofprintId: number
	level: string
	guardDays: number
	orderNo: number
}

export interface GuardArticleItem {
	id: number
	title: string
	content: string
	original: string
	postTime: string
	visit: number
	files: MediaFile[]
	coverUrl: string
}

export interface MediaFile {
	name: string
	type: string
	url: string
}

export interface GuardIdCardResponseData {
	identityNo: string
	username: string
	gender: number
	city: string
	career: string
	label: string
	avatarPic: string
}

export interface GuardRecordsResponse {
	actionTime: string
	points: number
	guardType: number
	userId: string
	userName: string
	liked: number
}
export interface GuardRecordItem {
	avatarPic: string
	content: string
	hoofprintId: number
	id: number
	isFeatured: number
	isTop: number
	likes: number
	liked: number
	location: string
	nickName: string
	postTime: string
	title: string
	urls: string[]
	userName: string
	visit: number
	comments: number
}

export interface ScoreHistoryParams extends PageParams {
	conditions?: Condition[]
	offset?: number
	orderByList?: OrderBy[]
	orderByStr?: string
	userId?: string
}

export interface ScoreHistoryItem {
	actionTime: string
	guardType: number
	points: number
	currentPoints: number
	user: {
		avatarPic: string
		userId: string
		userName: string
	}
}

export type ScoreHistoryResponse = IPaginationBase<ScoreHistoryItem>

export type GuardHoofprintEventData = {
	eventId: number
	eventType: number
	guardMember: number
	hoofprintCode: string
	hoofprintId: number
	imageUrl: string
	points: number
}

export interface GuardIssueRecordItem {
	avatarPic: string
	comments: number
	id: number
	title: string
	content: string
	urls: string[]
	hoofprintId: number
	location: string
	postTime: string
	likes: number
	visit: number
	isTop: number
	isFeatured: number
	nickName: string
	userName: string
	liked: number
}

export type GuardNewsResponse = IPaginationBase<GuardArticleItem>

export type GuardIssueRecordResponse = IPaginationBase<GuardIssueRecordItem>
