// 声明uni-app内置组件，使其在TypeScript中不会报错
declare namespace JSX {
	interface IntrinsicElements {
		view: any
		text: any
		input: any
		textarea: any
		image: any
		img: any
		button: any
		b: any
		span: any
		progress: {
			percent: number
			'stroke-width'?: string
			'active-color'?: string
			'background-color'?: string
		}
		// 其他uni-app组件...
	}
}

// 声明uni-popup的类型
declare interface UniPopupInstance {
	open: () => void
	close: () => void
}
