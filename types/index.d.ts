// 首页API类型定义文件
export interface IndexCardResponseData {
	claimTime: string
	guardDays: number
	hoofprintId: number
	hoofprintCode: string
	imageUrl: string
	points: number
	globalUrl: string
}

export interface IndexTrendsResponseData {
	id: number
	title: string
	content: string
	url: string[]
	hoofprintId: number
	location: string
	likes: number
	postTime: string
	visit: number
	liked: number
	isTop: number
	isFeatured: number
}
