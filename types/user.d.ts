// 用户API类型定义文件

export interface UserProfile {
	// 这里添加用户资料类型
	[key: string]: any
}

export interface UserRegisteredResponse {
	// 用户注册状态响应
	[key: string]: any
}

export interface UserInviteCode {
	// 邀请码类型
	[key: string]: any
}

export interface UserInviteCount {
	// 邀请计数类型
	[key: string]: any
}

export interface UserEditInfoParams {
	// 编辑用户信息参数
	[key: string]: any
}

export interface UserEditInfoResponse {
	// 编辑用户信息响应
	[key: string]: any
}
