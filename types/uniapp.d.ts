// types/uniapp.d.ts
//
// !!! 注意：此文件是为了解决在当前项目中无法正确解析 'import { ... } from "@dcloudio/uni-app"' 的问题而手动创建的声明文件。
// 它可能与官方 @dcloudio/types 包的更新存在差异或冲突。
// 如果未来 @dcloudio/types 更新并解决了模块导出问题，应优先使用官方类型并移除此文件。

// 声明image和img组件以修复类型错误
declare namespace JSX {
	interface IntrinsicElements {
		image: any
		img: any
		button: any
		b: any
		span: any
	}
}

// 声明uni-popup的类型
declare interface UniPopupInstance {
	open: () => void
	close: () => void
}

// 定时器类型声明
declare type Timeout = ReturnType<typeof setTimeout>

// 未在此处导入 '@dcloudio/types'，因为它可能干扰模块声明的解析。
// 全局的 uni 对象及其类型应由安装的 @dcloudio/types 包通过 tsconfig.json 的 types 字段提供。

declare module '@dcloudio/uni-app' {
	/**
	 * 页面加载时触发。一个页面只会调用一次，可以在 onLoad 的参数中获取打开当前页面路径中的参数。
	 * @param query 打开当前页面路径中的参数。
	 *              由于引用官方 UniApp.OnLoadOptions 可能导致解析问题，这里使用通用类型。
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#onLoad
	 */
	export function onLoad(callback: (query: Record<string, string | undefined>) => void): void

	/**
	 * 页面显示/切入前台时触发。
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#onShow
	 */
	export function onShow(callback: () => void): void

	/**
	 * 页面初次渲染完成时触发。一个页面只会调用一次，代表页面已经准备妥当，可以和视图层进行交互。
	 * 注意：此生命周期仅在非 nvue 页面中使用。
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#onReady
	 */
	export function onReady(callback: () => void): void

	/**
	 * 页面隐藏/切入后台时触发。 如 navigateTo 或底部 tab 切换到其他页面，小程序切入后台等。
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#onHide
	 */
	export function onHide(callback: () => void): void

	/**
	 * 页面卸载时触发。如 redirectTo 或 navigateBack 到其他页面时。
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#onUnload
	 */
	export function onUnload(callback: () => void): void

	/**
	 * 监听用户下拉刷新事件。需要在 Ppages.json 中配置 "enablePullDownRefresh": true。
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#onpulldownrefresh
	 */
	export function onPullDownRefresh(callback: () => void): void

	/**
	 * 页面上拉触底事件的处理函数。
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#onreachbottom
	 */
	export function onReachBottom(callback: () => void): void

	/**
	 * 用户点击右上角转发。
	 * @param options 转发配置项
	 * @see https://uniapp.dcloud.net.cn/api/plugins/share.html#onShareAppMessage
	 */
	export function onShareAppMessage(callback: (options?: any) => any): void // options 和返回值的类型可以更具体

	/**
	 * 页面滚动触发事件的处理函数。
	 * @param options 滚动事件参数
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#onpagescroll
	 */
	export function onPageScroll(callback: (options: { scrollTop: number }) => void): void

	/**
	 * 页面尺寸改变时触发，详见 响应显示区域变化。
	 * @param options 尺寸信息
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#onresize
	 */
	export function onResize(callback: (options: any) => void): void // options 类型可以更具体

	/**
	 * 点击 tab 时触发。
	 * @param item 点击的 tabItem 配置项
	 * @see https://uniapp.dcloud.net.cn/tutorial/page.html#ontabitemtap
	 */
	export function onTabItemTap(callback: (item: any) => void): void // item 类型可以更具体

	/**
	 * 获取当前页面栈的实例，以数组形式按栈的顺序给出，第一个元素为首页，最后一个元素为当前页面。
	 * @returns 页面实例数组
	 */
	export function getCurrentPages(): Array<any> // Page 实例的类型可以尝试定义得更具体，例如包含 route, options 等属性

	// --- 如有需要，可以在这里继续添加其他从 '@dcloudio/uni-app' 导出的函数或类型 ---
	// 例如：
	// export function createSSRApp(App: any): any;
	// export function ref(value: any): any; // 通常会从 'vue' 导入 ref
	// export function reactive(target: any): any; // 通常会从 'vue' 导入 reactive
}
