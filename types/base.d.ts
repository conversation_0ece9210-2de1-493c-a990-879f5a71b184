export interface Condition {
	key?: string
	value?: string
}

export interface OrderBy {
	field?: string
	order?: string
}

export interface PageParams {
	pageNum: number
	pageSize: number
}

export interface PageListResponse<T> {
	endRow: number
	hasNextPage: boolean
	hasPreviousPage: boolean
	isFirstPage: boolean
	isLastPage: boolean
	navigatePages: number
	navigatepageNums: number[]
	pageNum: number
	pageSize: number
	pages: number
	size: number
	startRow: number
	total: number
	navigateFirstPage: number
	navigateLastPage: number
	nextPage: number
	prePage: number
	list: T[]
}

export enum ResponseCode {
	SUCCESS = 1,
	ERROR = 0
}

export interface PageResponse<T> {
	code?: ResponseCode
	data?: T
	msg?: string
}

export interface IPaginationBase<T> {
	endRow: number
	hasNextPage: boolean
	hasPreviousPage: boolean
	isFirstPage: boolean
	isLastPage: boolean
	nextPage: number
	pageNum: number
	pageSize: number
	pages: number
	prePage: number
	total: number
	list: T[]
}

export interface IPopupMethods {
	openPopup: () => void
	closePopup: () => void
}

export enum DataStatus {
	LOADING = 'loading',
	NOMORE = 'noMore',
	MORE = 'more'
}
