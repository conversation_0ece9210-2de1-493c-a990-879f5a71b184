export interface IUniPopupActions {
	open: (type?: string) => void
	close: () => void
}

// Progress 组件的属性类型
export interface ProgressProps {
	percent: number
	showInfo?: boolean
	strokeWidth?: number | string
	activeColor?: string
	backgroundColor?: string
	active?: boolean
	activeMode?: string
	duration?: number
}

// 扩展 JSX 的 IntrinsicElements
// declare namespace JSX {
// 	interface IntrinsicElements {
// 		progress: Partial<ProgressProps>
// 	}
// }

// declare module '@vue/runtime-core' {
// 	export interface GlobalComponents {
// 		Progress: {
// 			new (): {
// 				$props: ProgressProps
// 			}
// 		}
// 	}
// }
