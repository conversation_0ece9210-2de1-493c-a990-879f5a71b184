// 活动API类型定义文件

import { Condition, OrderBy, PageParams, PageResponse } from './base'

export enum ActivityStatus {
	UN_START = 0,
	IN_PROGRESS = 1,
	ENDED = 2,
	CANCELLED = 3
}

export enum ActivityAttachmentType {
	POSTER = 1,
	COVER = 2
}

// 基础附件类型
export interface ActivityAttachment {
	url: string
	name: string
	orderNo: number
	type: ActivityAttachmentType
}

// 创建活动时使用的附件类型，不需要orderNo和type
export interface ActivityCreateAttachment extends Omit<ActivityAttachment, 'orderNo' | 'type'> {
	type: string
}

export interface ActivityBaseInfo {
	id: number
	name: string
	activityTime: string
	isRegistered: 1 | 0
	registrationDeadline: string
	status: ActivityStatus
	attachments: ActivityAttachment[]
}

export interface ActivityListParams extends PageParams {
	conditions?: Condition[]
	offset?: number
	orderByList?: OrderBy[]
	orderByStr?: string
}

export type ActivityListResponseData = PageListResponse<ActivityBaseInfo>

export interface ActivityDetailInfo extends ActivityBaseInfo {
	benefits: string
	description: string
	organizer: string
	notes: string
	place: string
	recruitmentQuota: number
	registrationCount: number
}

export interface ActivityRegisterParams {
	activityId?: number
	phone?: string
}

// 使用Omit排除不需要的字段，然后覆盖attachments字段类型
export interface ActivityCreateParams
	extends Omit<ActivityDetailInfo, 'id' | 'isRegistered' | 'status' | 'attachments'> {
	attachments: ActivityCreateAttachment[]
}

export interface ActivityUpdateParams extends ActivityCreateParams {
	id: number
}

export type ActivityListResponse = PageResponse<ActivityListResponseData>

export type ActivityDetailResponse = PageResponse<ActivityDetailInfo>
