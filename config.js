let BASE_URL = ''
let baseMapUrl = 'https://3dgs-unicity.bdnrc.org.cn?splatUrl='
// eslint-disable-next-line no-undef
if (process.env.NODE_ENV == 'development') {
	BASE_URL = 'https://shuiyuzui.bdnrc.org.cn' // qe环境,公司服务器
	baseMapUrl = 'https://3dgs-unicity.bdnrc.org.cn?splatUrl='
} else {
	BASE_URL = 'https://shuiyuzui.bdnrc.org.cn' // 生产环境
	baseMapUrl = 'https://3dgs-unicity.bdnrc.org.cn?splatUrl='
}

const config = {
	base_url: BASE_URL,
	appId: 'wx6361d1155b715fbf',
	appKey: ''
}
const agentUrl = 'https://shuiyuzui-dev.bdnrc.org.cn/agent'
const imgBaseUrl = 'https://shuiyuzui-cdn.bdnrc.org.cn/static/'
const uploadImgPreviewUrl = 'https://shuiyuzuiimg.bdnrc.org.cn'

const uploadBasePath = '/oss/api/v1/oss/'

const AccessKey_ID = 'LTAI5tHit13nusTgqPS1KLcn'
const AccessKey_Secret = '******************************'
const AppKey = 'm0lbKolP2hgTecPS'

export {
	BASE_URL,
	config,
	imgBaseUrl,
	uploadBasePath,
	baseMapUrl,
	uploadImgPreviewUrl,
	AccessKey_ID,
	AccessKey_Secret,
	AppKey,
	agentUrl
}
