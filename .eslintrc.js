module.exports = {
	env: {
		browser: true,
		es2021: true
	},
	parser: 'vue-eslint-parser',
	parserOptions: {
		ecmaVersion: 2020,
		sourceType: 'module',
		parser: '@typescript-eslint/parser',
		extraFileExtensions: ['.vue']
	},
	extends: [
		'eslint:recommended',
		'plugin:vue/vue3-recommended',
		'plugin:@typescript-eslint/recommended',
		'prettier'
	],
	ignorePatterns: ['!**/*'],
	plugins: ['vue', '@typescript-eslint'],
	rules: {
		'vue/multi-word-component-names': 'off',
		'vue/no-mutating-props': 'off',
		'vue/no-v-model-argument': 'off'
	},
	globals: {
		uni: 'writable',
		wx: 'writable',
		getCurrentPages: 'writable'
	},
	overrides: [
		{
			files: ['*.d.ts'],
			rules: {
				'@typescript-eslint/no-unused-vars': 'off', // 声明文件中允许未使用的变量
				'@typescript-eslint/no-explicit-any': 'off' // 有时在声明文件中需要 any
			}
		}
	]
}
