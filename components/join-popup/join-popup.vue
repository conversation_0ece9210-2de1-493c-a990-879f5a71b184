<template>
	<uni-popup
		ref="popupRef"
		:safe-area="true"
		mask-background-color="rgba(0, 0, 0, 0.8)"
		:mask-click="false"
		:is-mask-click="false"
	>
		<view class="popup-container">
			<img :src="`${imgBaseUrl}share/share-join-content.png`" class="popup-content" />
			<img
				:src="`${imgBaseUrl}home/home-group-icon-close.png`"
				class="close-icon"
				@click="closePopup()"
			/>
			<view class="join-num">又有{{ inviteCount }}位新用户</view>
			<view class="gurad-scores">守护值+{{ scores }}</view>
		</view>
	</uni-popup>
</template>
<script>
export default {
	options: { styleIsolation: 'shared' }
}
</script>
<script setup>
import { ref, computed } from 'vue'
import { imgBaseUrl } from '../../config'

const props = defineProps({
	inviteCount: {
		type: Number,
		default: 0
	}
})
const scores = computed(() => {
	return props.inviteCount * 10
})

const popupRef = ref(null)

const openPopup = () => {
	popupRef.value.open('center')
}
const closePopup = () => {
	popupRef.value.close()
	uni.showTabBar()
}

defineExpose({
	openPopup,
	closePopup
})
</script>
<style scoped lang="scss">
:deep(.uni-popup__wrapper) {
	width: fit-content;
	height: fit-content;
}

.popup-container {
	display: flex;
	flex-direction: column;
	position: relative;
	left: 50%;
	transform: translateX(-50%);
	background-size: cover;
	width: 100%;
	height: 100%;
	.popup-content {
		width: 434rpx;
		height: 378rpx;
	}
	.close-icon {
		width: 56rpx;
		height: 56rpx;
		margin-top: 48rpx;
		position: relative;
		left: 50%;
		transform: translateX(-50%);
	}
	.join-num {
		position: absolute;
		font-size: 14px;
		font-weight: 500;
		color: #407204;
		top: 180rpx;
		left: 120rpx;
	}
	.gurad-scores {
		position: absolute;
		font-size: 12px;
		font-weight: 500;
		color: #555555;
		top: 291rpx;
		left: 178rpx;
	}
}
</style>
