<template>
	<view class="card-contain">
		<view class="card-content">
			<view class="card-top">
				<image :src="`${currentScenicData?.snapshotFileUrl}`" class="card-img"></image>
				<view class="card-right">
					<view class="card-right-top">
						<view class="card-right-top-left">
							<view class="card-title">{{ currentScenicData?.name }}</view>
							<view class="card-top-icons">
								<image
									:src="`${imgBaseUrl}component/scene-card-3d-icon.png`"
									class="card-top-icon"
								></image>
								<image
									v-if="currentScenicData?.arTag === 1"
									:src="`${imgBaseUrl}component/scene-card-ar-icon.png`"
									class="card-top-icon"
								></image>
							</view>
						</view>
						<image
							:src="`${imgBaseUrl}museum/cloud/card-cancel-icon.png`"
							class="card-cancel-icon"
							@click="handleCancel"
						></image>
					</view>
					<view class="card-desc">{{ currentScenicData?.description }}</view>
				</view>
			</view>
			<view class="card-bottom">
				<view class="card-bottom-left">
					<image
						:src="`${imgBaseUrl}component/scene-card-notice-icon.png`"
						class="card-tips-icon"
					></image>
					<view class="card-tips-text">场景浏览会消耗较多流量</view>
				</view>
				<view class="card-bottom-right">
					<image
						:src="`${imgBaseUrl}component/scene-card-3d-btn.png`"
						class="card-btn-3d"
						@click="handleEnterScene"
					/>
					<image
						v-if="currentScenicData?.arTag === 1"
						:src="`${imgBaseUrl}component/scene-card-ar-btn.png`"
						class="card-btn-ar"
						@click="handleEnterAR"
					/>
				</view>
			</view>
		</view>
	</view>
</template>
<script setup lang="ts">
import { imgBaseUrl } from '@/config'
import { ScenicData } from '@/types/scenic'

const props = withDefaults(
	defineProps<{
		currentScenicData: ScenicData
	}>(),
	{
		currentScenicData: () => ({}) as ScenicData
	}
)

const emit = defineEmits(['handleClose', 'handleEnterScene', 'handleEnterAr'])

const handleCancel = () => {
	emit('handleClose')
}
const handleEnterScene = () => {
	emit('handleEnterScene', props.currentScenicData)
}
const handleEnterAR = () => {
	console.log('handleEnterAR', props.currentScenicData)
	emit('handleEnterAr', props.currentScenicData)
}
</script>
<style scoped>
.card-contain {
	width: 100vw;
	padding-left: 36rpx;
	padding-right: 36rpx;
	padding-bottom: 32rpx;
	box-sizing: border-box;
	position: absolute;
	bottom: 0;
	left: 0;
}

.card-content {
	display: flex;
	flex-direction: column;
	border-radius: 8px;
	padding: 20rpx;
	background: linear-gradient(180deg, #ffffff 0%, #f6f1d9 100%);
}

.card-top {
	display: flex;
	gap: 16rpx;
}

.card-img {
	width: 194rpx;
	height: 194rpx;
	border-radius: 8px;
}

.card-right {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.card-right-top {
	display: flex;
	justify-content: space-between;
}

.card-right-top-left {
	display: flex;
	gap: 10rpx;
	height: 38rpx;
	align-items: center;
}

.card-title {
	font-family: Douyin Sans;
	font-size: 16px;
	font-weight: 500;
	color: #4d1f00;
}

.card-top-icons {
	display: flex;
	align-items: center;
	height: 38rpx;
	gap: 4rpx;
}

.card-top-icon {
	width: 46rpx;
	height: 28rpx;
}

.card-cancel-icon {
	width: 32rpx;
	height: 32rpx;
}

.card-desc {
	flex: 1;
	font-size: 12px;
	line-height: 18px;
	color: #151515cc;
}

.card-tips-icon {
	width: 28.8rpx;
	height: 32rpx;
}

.card-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.card-bottom-left {
	display: flex;
	align-items: center;
	gap: 4rpx;
}

.card-tips-text {
	font-size: 10px;
	color: #0000008c;
}

.card-bottom-right {
	display: flex;
	gap: 12rpx;
}
.card-btn-3d {
	width: 218rpx;
	height: 68rpx;
}
.card-btn-ar {
	width: 130rpx;
	height: 68rpx;
}
</style>
