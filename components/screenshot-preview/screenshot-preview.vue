<template>
	<view v-if="visible" class="screenshot-overlay" :style="computedOverlayStyle">
		<view id="screenshotContainer" class="screenshot-container">
			<image
				:src="`${imgBaseUrl}screenshot-preview-close-icon.png`"
				class="close-icon"
				@click="handleClose"
			/>
			<view class="image-area">
				<image :src="screenshotData" mode="aspectFill" class="screenshot-image" />
			</view>
			<view class="info-bar">
				<view class="text-info">
					<text class="title">京西水峪嘴云村民</text>
					<text class="details">{{ currentDateTime }} | 京西古道，{{ locationName }}</text>
				</view>
				<image :src="infoBarBgBase64" class="info-bar-bg" />
				<image :src="QrCodeBase64" class="qr-code" />
			</view>
		</view>
		<!-- 只有绘制完成后才显示操作按钮 -->

		<view v-if="isCanvasReady" class="save-btn" @click="handleSave">
			<image :src="`${imgBaseUrl}visual/screenshot-preview-download-icon.png`" class="save-btn" />
		</view>

		<!-- 隐藏的保存用canvas -->
		<!-- <canvas
			canvas-id="saveCanvas"
			id="saveCanvas"
			class="save-canvas"
			:style="{ width: '840px', height: '472px' }"
		></canvas> -->
	</view>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useStore } from 'vuex'
import dayjs from 'dayjs'
import { QrCodeBase64, infoBarBgBase64 } from './settings/config'
import { imgBaseUrl } from '@/config'

const store = useStore()

const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	},
	screenshotData: {
		type: String,
		default: ''
	},
	locationName: {
		type: String,
		default: ''
	}
})

const emit = defineEmits(['close', 'save'])

// 状态管理
const isCanvasReady = ref(false)
const generatedImagePath = ref('')

const computedOverlayStyle = computed(() => {
	const navBarInfo = store.getters.navbarInfo
	return {
		top: `${navBarInfo.navHeight}px`,
		height: `calc(100vh - ${navBarInfo.navHeight}px)`
	}
})

const currentDateTime = computed(() => {
	return dayjs().format('YYYY-MM-DD HH:mm:ss')
})

// 监听visible变化，自动开始绘制
watch(
	() => props.visible,
	(newVal) => {
		if (newVal) {
			// 重置状态
			isCanvasReady.value = false
			generatedImagePath.value = ''
			// 延迟一点开始绘制，确保DOM已渲染
			setTimeout(() => {
				startCanvasDrawing()
			}, 100)
		}
	}
)

// 监听截屏数据变化，重新绘制canvas
watch(
	() => props.screenshotData,
	(newVal, oldVal) => {
		// 只有在组件可见且截屏数据真正发生变化时才重新绘制
		if (props.visible && newVal && newVal !== oldVal) {
			console.log('截屏数据变化，重新绘制canvas')
			// 重置状态
			isCanvasReady.value = false
			generatedImagePath.value = ''
			// 延迟一点开始绘制
			setTimeout(() => {
				startCanvasDrawing()
			}, 100)
		}
	}
)

const handleClose = () => {
	emit('close')
}

const drawImgFromBase64 = (filePath, base64Data, drawCallback) => {
	return new Promise((resolve) => {
		uni.getFileSystemManager().writeFile({
			filePath,
			data: base64Data.replace(/^data:image\/[^;]+;base64,/, ''),
			encoding: 'base64',
			success: () => {
				drawCallback()
				resolve(true)
			},
			fail: (err) => {
				console.error('写入文件失败:', err)
				resolve(false) // 失败也resolve，不阻断其他操作
			}
		})
	})
}

// 绘制信息条的函数
const drawInfoBar = (ctx, canvasWidth, imageHeight, infoBarHeight) => {
	console.log('开始绘制信息条')

	// 绘制信息条背景
	ctx.setFillStyle('#FFFFFF')
	ctx.fillRect(0, imageHeight, canvasWidth, infoBarHeight)

	// 绘制标题
	ctx.setFillStyle('#000000')
	ctx.setFontSize(8)
	ctx.setTextBaseline('top')
	ctx.fillText('京西水峪嘴云村民', 16, imageHeight + 17)

	// 绘制详情信息
	ctx.setFillStyle('#000000')
	ctx.setFontSize(6)
	const details = `${currentDateTime.value} | 京西古道，${props.locationName}`
	ctx.fillText(details, 16, imageHeight + 30)

	const drawImgPromises = [
		drawImgFromBase64(`${wx.env.USER_DATA_PATH}/info-bar-bg-temp.png`, infoBarBgBase64, () => {
			ctx.drawImage(
				`${wx.env.USER_DATA_PATH}/info-bar-bg-temp.png`,
				0,
				imageHeight,
				canvasWidth,
				infoBarHeight
			)
		}),
		drawImgFromBase64(`${wx.env.USER_DATA_PATH}/qr-code-temp.png`, QrCodeBase64, () => {
			const qrSize = 109
			const qrX = canvasWidth - qrSize - 3
			const qrY = imageHeight - 58
			ctx.drawImage(`${wx.env.USER_DATA_PATH}/qr-code-temp.png`, qrX, qrY, qrSize, qrSize)
		})
	]

	Promise.allSettled(drawImgPromises).then(() => {
		console.log('信息条绘制完成，开始导出')
		exportCanvasImage(ctx)
	})
}

// 导出Canvas为图片文件
const exportCanvasImage = (ctx) => {
	ctx.draw(false, () => {
		setTimeout(() => {
			uni.canvasToTempFilePath({
				canvasId: 'saveCanvas',
				width: 420,
				height: 290,
				destWidth: 420,
				destHeight: 290,
				success: (result) => {
					console.log('Canvas导出成功:', result.tempFilePath)
					generatedImagePath.value = result.tempFilePath
					isCanvasReady.value = true
				},
				fail: (err) => {
					console.error('Canvas导出失败:', err)
					uni.showToast({
						title: '生成图片失败',
						icon: 'error'
					})
					// 即使失败也显示按钮，让用户可以重试或关闭
					isCanvasReady.value = true
				}
			})
		}, 500)
	})
}

// 自动开始Canvas绘制
const startCanvasDrawing = async () => {
	try {
		console.log('开始自动绘制图片，截图数据:', props.screenshotData?.substring(0, 50))

		// 使用固定的canvas尺寸
		const canvasWidth = 420
		const canvasHeight = 290
		const infoBarHeight = 54
		const imageHeight = canvasHeight - infoBarHeight

		const canvasId = 'saveCanvas'
		const ctx = uni.createCanvasContext(canvasId)

		// 清空canvas内容，确保每次都是新内容
		ctx.clearRect(0, 0, canvasWidth, canvasHeight)

		// 设置canvas背景
		ctx.setFillStyle('#FFFFFF')
		ctx.fillRect(0, 0, canvasWidth, canvasHeight)

		// 绘制主图片（如果有截图数据）
		if (props.screenshotData) {
			console.log('开始绘制主图片')
			// 使用时间戳生成唯一的临时文件名，避免缓存问题
			const timestamp = Date.now()
			const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_screenshot_${timestamp}.jpg`

			// 先把base64数据保存为临时文件，然后绘制
			uni.getFileSystemManager().writeFile({
				filePath: tempFilePath,
				data: props.screenshotData.replace(/^data:image\/[^;]+;base64,/, ''),
				encoding: 'base64',
				success: () => {
					console.log('临时文件写入成功:', tempFilePath)
					ctx.drawImage(tempFilePath, 0, 0, canvasWidth, imageHeight)

					// 绘制底部信息条
					drawInfoBar(ctx, canvasWidth, imageHeight, infoBarHeight)
				},
				fail: (err) => {
					console.error('写入临时文件失败:', err)
					// 如果写入失败，直接绘制信息条
					drawInfoBar(ctx, canvasWidth, imageHeight, infoBarHeight)
				}
			})
		} else {
			console.log('没有截图数据，直接绘制信息条')
			drawInfoBar(ctx, canvasWidth, imageHeight, infoBarHeight)
		}
	} catch (error) {
		console.error('绘制图片时出错:', error)
		uni.showToast({
			title: '生成失败',
			icon: 'error'
		})
		isCanvasReady.value = true // 出错也要显示按钮
	}
}

// 保存到相册
const handleSave = async () => {
	if (!generatedImagePath.value) {
		uni.showToast({
			title: '图片还未生成完成',
			icon: 'error'
		})
		return
	}

	try {
		await uni.saveImageToPhotosAlbum({
			filePath: generatedImagePath.value
		})
		uni.showToast({
			title: '保存成功',
			icon: 'success'
		})
		emit('save', generatedImagePath.value)
	} catch (error) {
		console.error('保存到相册失败:', error)
		uni.showToast({
			title: '保存失败',
			icon: 'error'
		})
	}
}

// 分享
const handleShare = async () => {
	if (!generatedImagePath.value) {
		uni.showToast({
			title: '图片还未生成完成',
			icon: 'error'
		})
		return
	}

	try {
		await wx.shareFileMessage({
			filePath: generatedImagePath.value
		})
		uni.showToast({
			title: '分享成功',
			icon: 'success'
		})
	} catch (error) {
		console.error('分享失败:', error)
		uni.showToast({
			title: '分享失败',
			icon: 'error'
		})
	}
}
</script>

<style scoped>
.screenshot-overlay {
	position: absolute;
	left: 0;
	padding-top: 16px;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.6);
}

.screenshot-container {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	width: 420px;
	height: 290px;
	background-color: #ffffff;
	border-radius: 4px;
	overflow: hidden;
}

.image-area {
	position: relative;
	height: 236px;
}

.screenshot-image {
	width: 100%;
	height: 100%;
	display: block;
}

.info-bar {
	width: 100%;
	height: 54px;
	background-color: #ffffff;
	padding: 16px 12px;
	position: relative;
	box-sizing: border-box;
}

.text-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-betweens;
	color: #000000;
}

.title {
	font-size: 8px;
}

.details {
	font-size: 6px;
}

.info-bar-bg {
	width: 100%;
	height: 54px;
	position: absolute;
	top: 0;
	right: 0;
}

.qr-code {
	width: 109px;
	height: 109px;
	position: absolute;
	bottom: 3px;
	right: 3px;
}

.close-icon {
	position: absolute;
	top: 12px;
	right: 12px;
	width: 26px;
	height: 26px;
	z-index: 1000;
}

.loading-text {
	color: #ffffff;
	font-size: 16px;
}

.save-btn {
	position: absolute;
	top: 16px;
	right: 16px;
	width: 32px;
	height: 32px;
	border-radius: 16px;
	box-shadow: 1px 1px 4.5px 0px #40720426;
}

/* 隐藏的保存用canvas */
.save-canvas {
	position: absolute;
	top: -9999px;
	left: -9999px;
}
</style>
