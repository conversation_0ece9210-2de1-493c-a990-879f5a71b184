<template>
	<view v-if="visible" class="screenshot-overlay" :style="computedOverlayStyle">
		<view id="screenshotContainer" class="screenshot-container">
			<image
				:src="`${imgBaseUrl}screenshot-preview-close-icon.png`"
				class="close-icon"
				@click="handleClose"
			/>
			<view class="image-area">
				<image :src="screenshotData" mode="aspectFill" class="screenshot-image" />
			</view>
			<view class="info-bar">
				<view class="text-info">
					<text class="title">京西水峪嘴云村民</text>
					<text class="details">{{ currentDateTime }} | 京西古道，{{ locationName }}</text>
				</view>
				<image :src="infoBarBgBase64" class="info-bar-bg" />
				<image :src="QrCodeBase64" class="qr-code" />
			</view>
		</view>
		<view class="handle-area">
			<view class="save-btn" @click="handleSave">
				<text class="handle-text">保存</text>
			</view>
			<view class="share-btn" @click="handleClose">
				<text class="handle-text">分享</text>
			</view>
		</view>

		<!-- 隐藏的保存用canvas -->
		<!-- <canvas
			canvas-id="saveCanvas"
			class="save-canvas"
			:style="{ width: '840px', height: '472px' }"
		></canvas> -->
	</view>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import dayjs from 'dayjs'
import { QrCodeBase64, infoBarBgBase64 } from './settings/config'
import { imgBaseUrl } from '@/config'

const store = useStore()

const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	},
	screenshotData: {
		type: String,
		default: ''
	},
	locationName: {
		type: String,
		default: ''
	}
})

const emit = defineEmits(['close', 'save'])

const computedOverlayStyle = computed(() => {
	const navBarInfo = store.getters.navbarInfo
	return {
		top: `${navBarInfo.navHeight}px`,
		height: `calc(100vh - ${navBarInfo.navHeight}px)`
	}
})

const currentDateTime = computed(() => {
	return dayjs().format('YYYY-MM-DD HH:mm:ss')
})

const handleClose = () => {
	emit('close')
}

const exportImage = (ctx) => {
	// 绘制完成，导出图片
	ctx.draw(false, () => {
		setTimeout(() => {
			uni.canvasToTempFilePath({
				canvasId: 'saveCanvas',
				success: (result) => {
					console.log('Canvas导出成功:', result.tempFilePath)
					// 保存到相册
					uni.saveImageToPhotosAlbum({
						filePath: result.tempFilePath,
						success: () => {
							console.log('保存到相册成功')
							uni.showToast({
								title: '保存成功',
								icon: 'success'
							})
							emit('save', result.tempFilePath)
						},
						fail: (err) => {
							console.error('保存到相册失败:', err)
							uni.showToast({
								title: '保存失败',
								icon: 'error'
							})
						}
					})
				},
				fail: (err) => {
					console.error('Canvas导出失败:', err)
					uni.showToast({
						title: '生成图片失败',
						icon: 'error'
					})
				}
			})
		}, 500) // 等待绘制完成
	})
}

const drawImgFromBase64 = (filePath, base64Data, drawCallback) => {
	return new Promise((resolve) => {
		uni.getFileSystemManager().writeFile({
			filePath,
			data: base64Data.replace(/^data:image\/[^;]+;base64,/, ''),
			encoding: 'base64',
			success: () => {
				drawCallback()
				resolve(true)
			},
			fail: (err) => {
				console.error('写入文件失败:', err)
				resolve(false) // 失败也resolve，不阻断其他操作
			}
		})
	})
}

// 绘制信息条的函数
const drawInfoBar = (ctx, canvasWidth, imageHeight, infoBarHeight) => {
	console.log('开始绘制信息条')

	// 绘制信息条背景
	ctx.setFillStyle('#FFFFFF')
	ctx.fillRect(0, imageHeight, canvasWidth, infoBarHeight)

	// 绘制标题
	ctx.setFillStyle('#484848')
	ctx.setFontSize(8)
	ctx.setTextBaseline('top')
	ctx.fillText('京西水峪嘴云村民', 12, imageHeight + 8)

	// 绘制详情信息
	ctx.setFillStyle('#484848')
	ctx.setFontSize(6)
	const details = `${currentDateTime.value} | 京西古道，${props.locationName}`
	ctx.fillText(details, 12, imageHeight + 20)

	const drawImgPromises = [
		drawImgFromBase64(`${wx.env.USER_DATA_PATH}/info-bar-bg-temp.png`, infoBarBgBase64, () => {
			ctx.drawImage(
				`${wx.env.USER_DATA_PATH}/info-bar-bg-temp.png`,
				canvasWidth - 204,
				imageHeight,
				204,
				infoBarHeight
			)
		}),
		drawImgFromBase64(`${wx.env.USER_DATA_PATH}/qr-code-temp.png`, QrCodeBase64, () => {
			const qrSize = 48
			const qrX = canvasWidth - qrSize - 24
			const qrY = imageHeight - 20
			ctx.drawImage(`${wx.env.USER_DATA_PATH}/qr-code-temp.png`, qrX, qrY, qrSize, qrSize)
		})
	]

	Promise.allSettled(drawImgPromises).then(() => {
		console.log('信息条绘制完成，开始导出')
		exportImage(ctx)
	})
}

const handleSave = async () => {
	try {
		console.log('开始保存图片，截图数据:', props.screenshotData?.substring(0, 50))

		// 使用固定的canvas尺寸（对应600rpx x 280rpx）
		const canvasWidth = 420 // 600rpx / 2 = 300px
		const canvasHeight = 236 // 280rpx / 2 = 140px
		const infoBarHeight = 36 // 60rpx / 2 = 30px
		const imageHeight = canvasHeight - infoBarHeight // 110px

		const canvasId = 'saveCanvas'
		const ctx = uni.createCanvasContext(canvasId)

		// 设置canvas背景
		ctx.setFillStyle('#FFFFFF')
		ctx.fillRect(0, 0, canvasWidth, canvasHeight)

		// 绘制主图片（如果有截图数据）
		if (props.screenshotData) {
			console.log('开始绘制主图片')
			// 先把base64数据保存为临时文件，然后绘制
			uni.getFileSystemManager().writeFile({
				filePath: `${wx.env.USER_DATA_PATH}/temp_screenshot.jpg`,
				data: props.screenshotData.replace(/^data:image\/[^;]+;base64,/, ''),
				encoding: 'base64',
				success: () => {
					console.log('临时文件写入成功')
					ctx.drawImage(
						`${wx.env.USER_DATA_PATH}/temp_screenshot.jpg`,
						0,
						0,
						canvasWidth,
						imageHeight
					)

					// 绘制底部信息条
					drawInfoBar(ctx, canvasWidth, imageHeight, infoBarHeight)
				},
				fail: (err) => {
					console.error('写入临时文件失败:', err)
					// 如果写入失败，直接绘制信息条
					drawInfoBar(ctx, canvasWidth, imageHeight, infoBarHeight)
				}
			})
		} else {
			console.log('没有截图数据，直接绘制信息条')
			drawInfoBar(ctx, canvasWidth, imageHeight, infoBarHeight)
		}
	} catch (error) {
		console.error('保存图片时出错:', error)
		uni.showToast({
			title: '保存失败',
			icon: 'error'
		})
	}
}
</script>

<style scoped>
.screenshot-overlay {
	position: absolute;
	left: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16px;
	padding-top: 16px;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.6);
}

.screenshot-container {
	position: relative;
	width: 420px;
	height: 236px;
	background-color: #ffffff;
	border-radius: 4px;
	overflow: hidden;
}

.image-area {
	position: relative;
	height: 200px;
}

.screenshot-image {
	width: 100%;
	height: 100%;
	display: block;
}

.info-bar {
	width: 100%;
	height: 36px;
	background-color: #ffffff;
	padding: 8px 12px;
	position: relative;
	box-sizing: border-box;
}

.text-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-betweens;
	color: #484848;
}

.title {
	font-size: 8px;
}

.details {
	font-size: 6px;
}

.info-bar-bg {
	width: 204px;
	height: 36px;
	position: absolute;
	top: 0;
	right: 0;
}

.qr-code {
	width: 48px;
	height: 48px;
	position: absolute;
	bottom: 8px;
	right: 24px;
}

.close-icon {
	position: absolute;
	top: 12px;
	right: 12px;
	width: 26px;
	height: 26px;
}

.handle-area {
	display: flex;
	width: 100%;
	justify-content: center;
	gap: 10px;
}

.save-btn,
.share-btn {
	width: 109px;
	height: 36px;
	border-radius: 44px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #ffffff;
	font-size: 17px;
	font-weight: 500;
	letter-spacing: 1px;
	box-shadow: 1px 1px 4.5px 0px #40720426;
	font-family: Douyin Sans;
}

.save-btn {
	background-image: url('https://shuiyuzui-cdn.bdnrc.org.cn/static/visual/visual-ar-save-btn.png');
	background-size: cover;
	background-repeat: no-repeat;
}

.share-btn {
	border: 1px solid #ffffff;
}

/* 隐藏的保存用canvas */
.save-canvas {
	position: absolute;
	top: -9999px;
	left: -9999px;
}
</style>
