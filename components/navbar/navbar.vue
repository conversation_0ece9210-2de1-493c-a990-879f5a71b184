<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { imgBaseUrl } from '../../config'
// TODO: 抽取Hook。https://ask.dcloud.net.cn/question/180260

const store = useStore()

const props = defineProps({
	title: {
		type: String,
		default: ''
	},
	showNavBack: {
		type: Boolean,
		default: false
	},
	onNavClick: {
		type: Function,
		default: null
	},
	opacity: {
		type: Number,
		default: 0
	},
	eleColor: {
		type: String,
		default: '#151515'
	},
	navBackIcon: {
		type: String,
		default: imgBaseUrl + 'nav/nav-bar-back-icon.svg'
	},
	isLandscape: {
		type: Boolean,
		default: false
	}
})
const navbarBackIcon = computed(() => {
	// TODO: fix：navBackIcon未定义
	// eslint-disable-next-line
	return props.navBackIcon ? props.navBackIcon : navBackIcon
})
const navbarInfo = computed(() => store.getters.navbarInfo)
</script>

<template>
	<view
		class="navbar"
		:style="`height: ${props.isLandscape ? navbarInfo.navHeight : navbarInfo.barHeight}px; padding-top: ${
			props.isLandscape ? 0 : navbarInfo.statusBarHeight
		}px; background: rgba(255, 255, 255, ${opacity}); ${
			opacity === 1 && 'box-shadow: 0px 2px 2px 0px rgba(77, 31, 0, 0.02);'
		}`"
	>
		<!-- TODO: fix: 现阶段不支持传入颜色显示svg，右上角图标需要改成可配置 -->
		<image v-if="showNavBack" :src="navbarBackIcon" class="navbar-back" @click="onNavClick"></image>
		<view class="navbar-title" :style="`height: ${navbarInfo.navHeight}px; color: ${eleColor}`">{{
			title
		}}</view>
	</view>
</template>

<style lang="scss" scoped>
.navbar {
	margin: 0;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	width: 100%;
	position: fixed;
	top: 0;
	z-index: 999;

	&-back {
		width: 24px;
		height: 24px;
		padding-left: 16px;
		position: fixed;
	}

	&-title {
		font-size: 16px;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		flex: 1;
	}
}
</style>
