<template>
	<view class="spot-img-container" :style="computedSpotStyle()">
		<view class="spot-icon" :style="computedSpotIconStyle()" @click.stop="emitOpenPopup">
			<image
				v-if="spotInfo.arTag === 1"
				:src="`${imgBaseUrl}visual/visual-ar-tag.png`"
				class="spot-img-container-ar-tag"
			/>
		</view>
	</view>
</template>

<script setup>
import { imgBaseUrl } from '@/config'

const props = defineProps({
	spotInfo: {
		type: Object,
		default: () => ({})
	}
})

const emit = defineEmits(['clickedSceneInfo'])

const emitOpenPopup = () => {
	const message = {
		clickedSceneCode: props.spotInfo.code
	}
	emit('clickedSceneInfo', message)
}
const computedSpotStyle = () => {
	const { top, left } = props.spotInfo.position
	const { width, height } = props.spotInfo.size
	return {
		width: `${width}rpx`,
		height: `${height}rpx`,
		top: `${top}vh`,
		left: `${left}vw`
	}
}

const computedSpotIconStyle = () => {
	const { top, left, sceneIconBgUrl } = props.spotInfo.sceneIconInfo
	return {
		top: `${top}rpx`,
		left: `${left}rpx`,
		backgroundImage: `url(${imgBaseUrl}${sceneIconBgUrl})`
	}
}
</script>

<style scoped lang="scss">
.spot-img-container {
	position: absolute;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	.spot-btn {
		position: absolute;
		bottom: 3rpx;
		background-size: cover;
		background-repeat: no-repeat;
	}
	.spot-icon {
		position: absolute;
		width: 96rpx;
		height: 100rpx;
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}
}

.spot-img-container-ar-tag {
	position: absolute;
	width: 36rpx;
	height: 36rpx;
	bottom: 13rpx;
	right: -24rpx;
}
</style>
