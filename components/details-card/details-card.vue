<template>
	<view class="claim-content">
		<!-- claim-area -->
		<view
			class="claim-area"
			:style="{
				backgroundImage: `url(${props.claimData.imageUrl}`
			}"
		>
			<img :src="`${imgBaseUrl}home/home-claim-ribbon.png`" class="claim-img-ribbon" />
			<img :src="`${imgBaseUrl}home/home-claim-medal.png`" class="claim-img-medal" />
			<view
				:style="{
					backgroundImage: `url(${imgBaseUrl}home/home-claim-bottom.png)`
				}"
				class="claim-img-bottom"
			>
				<text class="claim-hoofprintCode"
					>蹄窝编号：<text class="claim-hoofprintCode-content">{{
						props.claimData.hoofprintCode
					}}</text></text
				>
				<text class="claim-text-content" v-if="isClaimSkipFlag"
					>{{
						props.claimData.userName
					}}，感谢你参与古道蹄窝守护活动，你可以开始云守护蹄窝啦！</text
				>
				<text class="claim-text-content" v-else>古道“蹄窝”不仅仅是石头，更是岁月和文明的刻痕</text>

				<text class="claim-content-bottom">守护时间：{{ props.claimData.claimTime }}</text>
				<text class="claim-content-bottom"> 蹄窝位置：水峪嘴村京西古道景区 </text>
				<view class="code-container">
					<image :src="codeUrl" class="two-dimensional-barcode" />
				</view>

				<text class="claim-text-logo">蹄窝证</text>
			</view>
		</view>
	</view>
</template>
<script setup>
import { imgBaseUrl } from '../../config'
import { UserApi } from '../../api/user'
import { onMounted, ref } from 'vue'

const userApi = new UserApi()
const codeUrl = ref(null)

const props = defineProps({
	claimData: {
		type: Object,
		Required: true
	},
	isClaimSkipFlag: {
		type: Boolean,
		required: true
	}
})

const getCode = async () => {
	const res = await userApi.getUserInviteCode()
	codeUrl.value = res.data.url || ''
}
onMounted(() => {
	getCode()
})
</script>
<style scoped lang="scss">
.claim-content {
	margin: 0 94rpx;

	// claim-area
	.claim-area {
		width: 100%;
		height: 768rpx;
		background-size: cover;
		position: relative;
		border-radius: 48rpx;
		background-position: center -201px;
		margin-top: 64rpx;

		.claim-img-ribbon {
			position: absolute;
			left: 0;
			top: 0;
			z-index: 2;
			width: 196rpx;
			height: 170rpx;
		}

		.claim-img-medal {
			position: absolute;
			right: 0;
			bottom: 258rpx;
			z-index: 3;
			height: 220rpx;
			width: 220rpx;
		}

		.claim-img-bottom {
			position: absolute;
			bottom: 0;
			z-index: 2;
			width: 100%;
			height: 384rpx;
			background-size: cover;
			display: flex;
			flex-direction: column;
			padding: 0 40rpx;
			box-sizing: border-box;

			.claim-hoofprintCode {
				margin-top: 64rpx;
				font-family: Douyin Sans;
				font-size: 18px;
				line-height: 21.64px;
				text-align: left;
				color: rgba(102, 122, 91, 1);

				.claim-hoofprintCode-content {
					color: rgba(125, 191, 0, 1);
				}
			}

			.claim-text-content {
				font-family: PingFang SC;
				font-size: 12px;
				font-weight: 400;
				line-height: 16.8px;
				text-align: left;
				margin: 16rpx 0 36rpx 0;
				color: rgba(21, 21, 21, 0.7);
			}

			.claim-content-bottom {
				color: rgba(102, 122, 91, 1);
				font-family: PingFang SC;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				text-align: left;
				margin-bottom: 16rpx;
			}

			.code-container {
				position: absolute;
				width: 150rpx;
				height: 150rpx;
				right: 26rpx;
				bottom: 38rpx;
				border: 1px solid rgba(7, 193, 96, 1);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				overflow: hidden;
				.two-dimensional-barcode {
					width: 150rpx;
					height: 150rpx;
				}
			}

			.claim-text-logo {
				position: absolute;
				bottom: 24rpx;
				left: 50%;
				transform: translateX(-50%);

				font-family: PingFang SC;
				font-size: 10px;
				font-weight: 400;
				line-height: 14px;
				text-align: center;

				color: rgba(128, 153, 115, 1);
			}
		}
	}

	.claim-area::before {
		content: '';
		position: absolute;
		top: -2px;
		right: -2px;
		bottom: -2px;
		left: -2px;
		border-radius: 48rpx;
		background: linear-gradient(180deg, rgba(249, 255, 241, 1) 0%, rgba(249, 255, 241, 1) 100%);
		z-index: -1;
		box-shadow:
			0 0 6px 1px rgba(205, 255, 124, 0.6),
			0 0 12px 2px rgba(249, 255, 241, 0.6);
	}
}
</style>
