<template>
	<view v-if="notice.show" class="notice-container">
		<template v-if="notice.iconContent">
			<slot name="icon"></slot>
		</template>
		<template v-else>
			<image :src="notice.icon" class="notice-icon" />
		</template>
		<template v-if="notice.customContent">
			<slot name="content"></slot>
		</template>
		<template v-else>
			<text class="notice-title">{{ notice.title }}</text>
		</template>
		<text class="notice-tips">{{ notice.tips }}</text>
	</view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface NoticeProps {
	icon: string
	title: string
	tips: string
	show: boolean
	duration: number
	manualShow?: boolean
	customContent?: boolean
	iconContent?: boolean
}
const notice = defineModel<NoticeProps>('notice', {
	default: () => ({
		icon: '',
		title: '',
		tips: '',
		show: false,
		duration: 3000,
		manualShow: false,
		customContent: false,
		iconContent: false
	})
})

// eslint-disable-next-line no-undef
const timer = ref<NodeJS.Timeout | null>(null)

// 清理定时器的函数
const clearTimer = () => {
	if (timer.value) {
		clearTimeout(timer.value)
		timer.value = null
	}
}

// 同时监听show和manualShow属性的变化
watch(
	() => [notice.value.show, notice.value.manualShow],
	([showVal, manualShowVal]) => {
		if (manualShowVal) return
		// 清理之前的定时器
		clearTimer()

		// 只有当show为true且manualShow为false时才设置自动关闭定时器
		if (showVal && !manualShowVal) {
			// 显示时设置新的定时器
			timer.value = setTimeout(() => {
				notice.value.show = false
			}, notice.value.duration)
		}
	},
	{ immediate: true } // 添加immediate确保初始化时也会执行一次
)
</script>

<style scoped>
.notice-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8px;
	padding: 16px;
	background-color: rgba(0, 0, 0, 0.6);
	color: #ffffff;
	border-radius: 8px;
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 1000;
}

.notice-icon {
	width: 32px;
	height: 32px;
}

.notice-title {
	font-size: 16px;
	font-weight: 500;
	letter-spacing: 0.02em;
}

.notice-tips {
	font-size: 12px;
	font-weight: 400;
	letter-spacing: 0.02em;
}
</style>
