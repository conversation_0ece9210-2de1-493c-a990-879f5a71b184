<template>
	<uni-popup
		ref="popupRef"
		:safe-area="true"
		mask-background-color="rgba(0, 0, 0, 0.8)"
		:mask-click="props.isMaskClick"
		:is-mask-click="props.isMaskClick"
	>
		<view
			class="popup-container"
			:style="{
				backgroundImage: `url(${props.styleData.bcUrl})`,
				width: props.styleData.width,
				height: props.styleData.height
			}"
		>
			<view class="popup-top" :style="popupTopStyle">
				{{ props.title ? props.title : '' }}<view :class="{ 'bc-block': props.title !== '' }"></view
			></view>
			<view
				class="popup-content"
				:class="props.content ? 'normal-content' : ''"
				:style="popupContentStyle"
			>
				{{ props.content ? props.content : '' }}
				<slot
					name="content-top"
					:style="props.slotStyleData.contentTop ? slotStyleData.contentTop : {}"
				></slot>
				<slot
					name="content-info"
					:style="props.slotStyleData.contentInfo ? slotStyleData.contentInfo : {}"
				></slot>
				<slot
					name="content-bottom"
					:style="props.slotStyleData.contentBottom ? slotStyleData.contentBottom : {}"
				></slot>
			</view>
			<view class="popup-footer" :style="popupFooterStyle">
				<button
					v-if="props.cancelText"
					class="login-btn"
					@click="
						props.isCustomizable && props.cancelFuntion ? props.cancelFuntion() : closePopup()
					"
				>
					{{ props.cancelText }}
				</button>
				<button
					v-if="props.sumbitText"
					class="login-btn"
					@click="
						props.isCustomizable && props.submitFunction ? props.submitFunction() : closePopup()
					"
				>
					{{ props.sumbitText }}
				</button>
			</view>
		</view>
		<img
			v-if="props.isCloseIcon"
			:src="`${imgBaseUrl}home/home-group-icon-close.png`"
			class="close-icon"
			@click="closePopup()"
		/>
	</uni-popup>
</template>
<script>
export default {
	options: { styleIsolation: 'shared' }
}
</script>
<script setup>
/**
 * title：标题
 * content：提示内容
 * cancelText：取消按钮内容
 * sumbitText：提交或确定按钮内容
 * styleData：外部弹窗样式
 * positionData：自定义内容区域位置定位 采用postion进行定位 （ 默认 ）
 * positionMarginData：自定义内容区域位置定位 采用magin进行定位 （需要添加 !important  字段覆盖）
 * isCustomizable:：是否自定义函数
 * isMaskClick：是否能够点击遮罩
 * isCloseIcon：是否需要关闭Icon
 * cancelFuntion：自定义取消函数
 * submitFunction：自定义确定函数
 * slotStyleData：插槽样式
 */

/**
 * 示例 props 配置：
 *
 * title: 标题
 * content: "这是一个弹窗提示内容"
 * cancelText: "取消"
 * sumbitText: "确定"
 * styleData: 外部弹窗样式配置
 * {
 *   bcUrl: "https://example.com/background-image.png", // 背景图片
 *   width: "300px",                                    // 弹窗宽度
 *   height: "400px"                                    // 弹窗高度
 * }
 * positionData: 采用 position 定位的内容区域位置
 * {
 *   popupTop: "50px",                                  // 弹窗距离顶部的位置
 *   popupContent: "100px",                             // 弹窗内容区的位置
 *   popupFooter: "20px"                                // 弹窗底部区域的位置
 * }
 * positionMarginData: 采用 margin 定位的内容区域位置（需要 !important）
 * {
 *   popupTop: { marginTop: "50px !important" },        // 顶部区域的外边距
 *   popupContent: { marginBottom: "10px !important" }, // 内容区域的外边距
 *   popupFooter: { marginBottom: "20px !important" }   // 底部区域的外边距
 * }
 * isCustomizable: true，表示是否使用自定义函数
 * isMaskClick: false，表示遮罩不可点击关闭弹窗
 * isCloseIcon: true，表示需要显示关闭图标
 * cancelFuntion: 自定义取消按钮函数
 * function () {
 *   console.log("取消操作");
 * }
 * submitFunction: 自定义确定按钮函数
 * function () {
 *   console.log("确定操作");
 * }
 * slotStyleData: 插槽样式配置
 * {
 *   popupContent: {
 *     color: "red",
 *     fontSize: "16px"
 *   },
 *   contentTop: {
 *     padding: "20px",
 *     backgroundColor: "#f5f5f5"
 *   },
 *   contentInfo: {
 *     textAlign: "center",
 *     lineHeight: "1.5"
 *   },
 *   contentBottom: {
 *     margin: "10px",
 *     borderTop: "1px solid #ddd"
 *   }
 * }
 */

import { ref, computed } from 'vue'
import { imgBaseUrl } from '../../config'
const props = defineProps({
	title: {
		type: String,
		Required: true,
		default: ''
	},
	content: {
		type: String,
		Required: true,
		default: ''
	},
	cancelText: {
		type: String,
		Required: true,
		default: null
	},
	sumbitText: {
		type: String,
		Required: true,
		default: null
	},
	styleData: {
		type: Object,
		default: () => ({
			bcUrl: '',
			width: '',
			height: ''
		})
	},
	positionData: {
		type: Object,
		default: () => ({
			popupTop: '',
			popupContent: '',
			popupFooter: ''
		})
	},
	positionMarginData: {
		type: Object,
		default: () => ({
			popupTop: {},
			popupContent: {},
			popupFooter: {}
		})
	},
	isCustomizable: {
		type: Boolean,
		Required: false,
		default: false
	},
	isMaskClick: {
		type: Boolean,
		Required: true,
		default: true
	},
	isCloseIcon: {
		type: Boolean,
		default: false
	},
	cancelFuntion: {
		type: Function,
		Required: false,
		default: null
	},
	submitFunction: {
		type: Function,
		Required: false,
		default: null
	},
	slotStyleData: {
		type: Object,
		default: () => ({
			popupContent: {},
			contentTop: {},
			contentInfo: {},
			contentBottom: {}
		})
	}
})

const popupRef = ref(null)
// 弹窗顶部区样式
const popupTopStyle = computed(() => {
	return {
		...(props.positionData.popupTop ? { bottom: props.positionData.popupTop } : {}),
		...(props.positionMarginData.popupTop ? props.positionMarginData.popupTop : {})
	}
})
// 弹窗内容区样式
const popupContentStyle = computed(() => {
	return {
		...(props.positionData.popupContent ? { bottom: props.positionData.popupContent } : {}),
		...(props.slotStyleData.popupContent ? props.slotStyleData.popupContent : {}),
		...(props.positionMarginData.popupContent ? props.positionMarginData.popupContent : {})
	}
})
// 弹窗底部区样式
const popupFooterStyle = computed(() => {
	return {
		...(props.positionData.popupFooter ? { bottom: props.positionData.popupFooter } : {}),
		...(props.positionMarginData.popupFooter ? props.positionMarginData.popupFooter : {})
	}
})

const openPopup = () => {
	popupRef.value.open('center')
}
const closePopup = () => {
	popupRef.value.close()
}

defineExpose({
	openPopup,
	closePopup
})
</script>
<style scoped lang="scss">
:deep(.uni-popup__wrapper) {
	width: fit-content;
	height: fit-content;
}

.popup-container {
	display: flex;
	flex-direction: column;
	position: relative;
	left: 50%;
	transform: translateX(-50%);
	font-weight: 500;
	font-family: PingFang SC;
	background-size: cover;

	.popup-top {
		width: 100%;
		text-align: center;
		font-size: 17px;
		line-height: 24px;
		position: absolute;
		bottom: 250rpx;
		.bc-block {
			position: absolute;
			background-color: rgba(195, 243, 90, 1);
			width: 240rpx;
			height: 30rpx;
			left: 50%;
			top: 24rpx;
			z-index: -1;
			transform: translateX(-50%);
		}
	}
	.popup-content {
		width: 100%;
		color: rgba(0, 0, 0, 0.55);
		padding-top: 5rpx;
		text-align: center;
		font-size: 12px;
		font-weight: 400;
		line-height: 19.6px;
		position: absolute;
		bottom: 174rpx;
	}
	.popup-footer {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		gap: 20rpx;
		height: 72rpx;
		position: absolute;
		bottom: 70rpx;

		.login-btn {
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 0;
			background-color: transparent;
			border-radius: 88rpx;
			height: 100%;
			min-width: 238rpx;

			font-size: 17px;
			line-height: 23.8px;
			text-align: center;

			&:first-child {
				color: rgba(98, 171, 11, 1);
				border: 1px solid rgba(98, 171, 11, 1);
			}
			&:last-child {
				background: linear-gradient(0deg, #ff9212 42.09%, #ffb763 101.14%);
				border: 1px solid rgba(255, 183, 99, 1);
				color: #fff;
			}
		}
	}
}

.close-icon {
	width: 56rpx;
	height: 56rpx;
	margin-top: 48rpx;
	position: relative;
	left: 50%;
	transform: translateX(-50%);
}
</style>
