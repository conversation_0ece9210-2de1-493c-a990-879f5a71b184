<template>
	<view
		class="container"
		:style="{ backgroundImage: `url(${imgBaseUrl}register/register-generate-bc.png)` }"
	>
		<img :src="`${imgBaseUrl}register/register-addVillage-title.png`" class="img-top" />
		<img :src="`${imgBaseUrl}landing/landing-img-bottom.png`" class="img-bottom" />
	</view>
</template>

<script setup>
import { imgBaseUrl } from '../../config'
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	width: 100vw;
	background-size: cover;
	.img-top {
		position: absolute;
		width: 522rpx;
		height: 200rpx;
		top: 334rpx;
		left: 50%;
		transform: translateX(-50%);
	}
	.img-bottom {
		width: 572rpx;
		height: 306rpx;
		position: absolute;
		bottom: 40rpx;
		right: 26rpx;
	}
}
</style>
