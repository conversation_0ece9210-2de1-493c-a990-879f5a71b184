import VueX from 'vuex'
import App from './App'
import store from './store'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import share from './utils/share.js'
export function createApp() {
	const app = createSSRApp(App)
	app.mixin(share)
	app.use(store)
	return {
		app,
		VueX
	}
}
// #endif
