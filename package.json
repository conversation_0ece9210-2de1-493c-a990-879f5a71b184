{"scripts": {"prepare": "husky install", "commit": "lint-staged --allow-empty && git commit", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.ts,.tsx --fix", "format": "prettier --write ."}, "dependencies": {"@dcloudio/uni-ui": "^1.5.6", "dayjs": "^1.11.13", "stylelint": "^16.9.0", "vue": "^3.0.0", "vuex": "^4.0.2"}, "devDependencies": {"@dcloudio/types": "^3.4.15", "@types/node": "^20.12.5", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@uni-helper/uni-app-types": "^1.0.0-alpha.6", "@uni-helper/uni-ui-types": "1.0.0-alpha.6", "eslint": "8.4.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "^9.28.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "sass": "^1.79.3", "sass-loader": "^10.1.1", "stylelint-config-standard": "^36.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite-plugin-require-transform": "^1.0.21", "vue-tsc": "^2.2.10"}, "lint-staged": {"*.{vue,js,jsx,ts,tsx}": "eslint --fix", "*.{vue,js,jsx,ts,tsx,json,css,scss,md}": "prettier --write", "**/*.{ts,tsx,vue}": ["bash -c 'pnpm type-check'"]}, "engines": {"node": ">=18.0.0 <21.0.0", "pnpm": ">=8.0.0 <9.0.0"}}