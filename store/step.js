const step = {
	namespaced: true,
	state: {
		stepFlag: 1
	},
	mutations: {
		ADD_STEPFLAG(state) {
			state.stepFlag++
		},
		DEC_STEPFLAG(state) {
			if (state.stepFlag > 1) {
				state.stepFlag--
			}
		},
		RESET_STEP(state) {
			state.stepFlag = 1
		}
	},
	actions: {
		addStepFlag({ commit }) {
			commit('ADD_STEPFLAG')
		},
		decStepFlag({ commit }) {
			commit('DEC_STEPFLAG')
		},
		resetStep({ commit }) {
			commit('RESET_STEP')
		}
	},
	getters: {
		stepFlag: (state) => state.stepFlag
	}
}

export default step
