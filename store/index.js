import { createStore } from 'vuex'
import step from './step.js'
const store = createStore({
	state: {
		navbarInfo: {
			statusBarHeight: 0,
			navHeight: 0,
			barHeight: 0
		},
		userInfo: {
			userId: '',
			userName: '',
			avatarPic: '',
			city: '',
			gender: 0,
			guardDays: 0,
			level: '',
			points: 0,
			nextLevelPoints: 0,
			registerTime: '',
			pinned: false
		},
		userRegistered: false,
		hoofPrintId: {},
		userLogin: true,
		signInfo: {
			date: '',
			ifSign: false
		},
		sharePageFlag: false,
		mapCount: 0
	},
	mutations: {
		SET_NAVBAR_INFO: (state, navbarInfo) => {
			state.navbarInfo = navbarInfo
		},
		SET_HOOFPRINT_ID: (state, hoofPrintId) => {
			state.hoofPrintId = hoofPrintId
		},
		SET_USER_INFO: (state, userInfo) => {
			state.userInfo = userInfo
		},
		SET_USER_REGISTERED: (state, userRegistered) => {
			state.userRegistered = userRegistered
		},
		SET_USER_LOGIN: (state, userLogin) => {
			state.userLogin = userLogin
		},
		SET_SIGN_INFO: (state, signInfo) => {
			state.signInfo = signInfo
		},
		SET_SHARE_PAGE_FLAG: (state, sharePageFlag) => {
			state.sharePageFlag = sharePageFlag
		},
		UPDATE_USER_INFO(state, payload) {
			state.userInfo = { ...state.userInfo, ...payload }
		},
		SET_MAP_COUNT(state, mapCount) {
			state.mapCount = mapCount
		},
		CLEAR_STATE: (state) => {
			state.userInfo = {
				userId: '',
				userName: '',
				avatarPic: '',
				city: '',
				gender: 0,
				guardDays: 0,
				level: '1',
				points: 0,
				nextLevelPoints: 0
			}
			// state.userRegistered = false
			state.hoofPrintId = {}
			state.userLogin = false
			state.signInfo = {
				date: '',
				ifSign: false
			}
			state.mapCount = 0
		}
	},
	actions: {
		setNavbarInfo({ commit }, navbarInfo) {
			commit('SET_NAVBAR_INFO', navbarInfo)
		},
		saveHoofPrintId({ commit }, hoofPrintId) {
			commit('SET_HOOFPRINT_ID', hoofPrintId)
		},
		setUserInfo({ commit }, userInfo) {
			commit('SET_USER_INFO', userInfo)
		},
		setUserRegistered({ commit }, userRegistered) {
			commit('SET_USER_REGISTERED', userRegistered)
		},
		setUserLogin({ commit }, userLogin) {
			commit('SET_USER_LOGIN', userLogin)
		},
		setSignInfo({ commit }, signInfo) {
			commit('SET_SIGN_INFO', signInfo)
		},
		setSharePageFlag({ commit }, sharePageFlag) {
			commit('SET_SHARE_PAGE_FLAG', sharePageFlag)
		},
		setMapCount({ commit }, mapCount) {
			commit('SET_MAP_COUNT', mapCount)
		},
		clearState({ commit }) {
			commit('CLEAR_STATE')
		}
	},
	getters: {
		navbarInfo: (state) => state.navbarInfo,
		userInfo: (state) => state.userInfo,
		userRegistered: (state) => state.userRegistered,
		userLogin: (state) => state.userLogin,
		signInfo: (state) => state.signInfo,
		sharePageFlag: (state) => state.sharePageFlag,
		mapCount: (state) => state.mapCount
	},
	modules: {
		step
	}
})

export default store
