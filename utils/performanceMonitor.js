/**
 * 性能监控工具
 * 监控帧率和内存使用情况，在性能不佳时提示用户
 */

class PerformanceMonitor {
  constructor() {
    this.isMonitoring = false
    this.frameCount = 0
    this.lastTime = 0
    this.currentFPS = 0
    this.fpsHistory = []
    this.memoryWarningCount = 0
    this.lastMemoryWarning = 0

    // 性能阈值配置
    this.config = {
      lowFPSThreshold: 20,        // 低帧率阈值
      criticalFPSThreshold: 15,   // 严重低帧率阈值
      fpsCheckInterval: 1000,     // FPS检查间隔(ms)
      memoryWarningCooldown: 10000, // 内存警告冷却时间(ms)
      maxFPSHistory: 10,          // 保存的FPS历史记录数量
    }

    // 回调函数
    this.onFPSUpdate = null
    this.onMemoryWarning = null
    this.onPerformanceAlert = null

    this.init()
  }

  /**
   * 初始化监控器
   */
  init() {
    // 监听内存警告
    if (typeof wx !== 'undefined' && wx.onMemoryWarning) {
      wx.onMemoryWarning(this.handleMemoryWarning.bind(this))
    }
  }

  /**
   * 获取当前时间（兼容微信小程序）
   */
  getCurrentTime() {
    if (typeof performance !== 'undefined' && performance.now) {
      return performance.now()
    } else if (typeof wx !== 'undefined' && wx.getPerformance) {
      return wx.getPerformance().now()
    } else {
      return Date.now()
    }
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.frameCount = 0
    this.lastTime = this.getCurrentTime()
    this.fpsHistory = []

    console.log('性能监控已启动')
    this.requestFrame()
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false

    // 清理回调引用，防止内存泄漏
    this.onFPSUpdate = null
    this.onMemoryWarning = null
    this.onPerformanceAlert = null

    console.log('性能监控已停止')
  }

  /**
   * 请求下一帧
   */
  requestFrame() {
    if (!this.isMonitoring) return

    // 兼容微信小程序环境
    const requestFrame = typeof requestAnimationFrame !== 'undefined'
      ? requestAnimationFrame
      : (callback) => setTimeout(callback, 16) // 约60fps

    requestFrame(() => {
      this.updateFPS()
      this.requestFrame()
    })
  }

  /**
   * 更新FPS计算
   */
  updateFPS() {
    this.frameCount++
    const currentTime = this.getCurrentTime()
    const deltaTime = currentTime - this.lastTime

    // 每秒计算一次FPS
    if (deltaTime >= this.config.fpsCheckInterval) {
      this.currentFPS = Math.round((this.frameCount * 1000) / deltaTime)
      this.frameCount = 0
      this.lastTime = currentTime

      // 保存FPS历史
      this.fpsHistory.push(this.currentFPS)
      if (this.fpsHistory.length > this.config.maxFPSHistory) {
        this.fpsHistory.shift()
      }

      // 触发FPS更新回调
      if (this.onFPSUpdate) {
        this.onFPSUpdate(this.currentFPS)
      }

      // 检查FPS性能
      this.checkFPSPerformance()
    }
  }

  /**
   * 检查FPS性能
   */
  checkFPSPerformance() {
    if (this.fpsHistory.length < 3) return // 需要足够的历史数据

    const avgFPS = this.getAverageFPS()

    if (avgFPS <= this.config.criticalFPSThreshold) {
      this.triggerPerformanceAlert('critical_fps', {
        currentFPS: this.currentFPS,
        averageFPS: avgFPS,
        message: `帧率严重下降，SLAM追踪可能不稳定，建议重启微信`
      })
    } else if (avgFPS <= this.config.lowFPSThreshold) {
      this.triggerPerformanceAlert('low_fps', {
        currentFPS: this.currentFPS,
        averageFPS: avgFPS,
        message: `帧率下降，可能影响AR体验`
      })
    }
  }

  /**
   * 处理内存警告
   */
  handleMemoryWarning() {
    const currentTime = Date.now()

    // 防止频繁触发警告
    if (currentTime - this.lastMemoryWarning < this.config.memoryWarningCooldown) {
      return
    }

    this.lastMemoryWarning = currentTime
    this.memoryWarningCount++

    console.warn('收到内存警告，当前警告次数:', this.memoryWarningCount)

    // 触发内存警告回调
    if (this.onMemoryWarning) {
      this.onMemoryWarning(this.memoryWarningCount)
    }

    // 根据警告次数调整消息严重程度
    if (this.memoryWarningCount >= 3) {
      let message = '内存严重不足，建议立即重启微信释放内存'
      // 触发性能警告
      this.triggerPerformanceAlert('memory_warning', {
        warningCount: this.memoryWarningCount,
        message: message
      })
    }
    // 尝试触发垃圾回收
    if (typeof wx !== 'undefined' && wx.triggerGC) {
      wx.triggerGC()
      console.log('已触发垃圾回收以释放内存')
    }
  }

  /**
   * 触发性能警告
   */
  triggerPerformanceAlert(type, data) {
    if (this.onPerformanceAlert) {
      this.onPerformanceAlert(type, data)
    }
  }

  /**
   * 获取平均FPS
   */
  getAverageFPS() {
    if (this.fpsHistory.length === 0) return 0
    const sum = this.fpsHistory.reduce((a, b) => a + b, 0)
    return Math.round(sum / this.fpsHistory.length)
  }

  /**
   * 获取性能状态
   */
  getPerformanceStatus() {
    const avgFPS = this.getAverageFPS()
    let status = 'good'

    if (avgFPS <= this.config.criticalFPSThreshold || this.memoryWarningCount > 0) {
      status = 'critical'
    } else if (avgFPS <= this.config.lowFPSThreshold) {
      status = 'warning'
    }

    return {
      status,
      currentFPS: this.currentFPS,
      averageFPS: avgFPS,
      memoryWarningCount: this.memoryWarningCount,
      isMonitoring: this.isMonitoring
    }
  }

  /**
   * 设置回调函数
   */
  setCallbacks({ onFPSUpdate, onMemoryWarning, onPerformanceAlert }) {
    if (onFPSUpdate) this.onFPSUpdate = onFPSUpdate
    if (onMemoryWarning) this.onMemoryWarning = onMemoryWarning
    if (onPerformanceAlert) this.onPerformanceAlert = onPerformanceAlert
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 重置监控数据
   */
  reset() {
    this.frameCount = 0
    this.currentFPS = 0
    this.fpsHistory = []
    this.memoryWarningCount = 0
    this.lastMemoryWarning = 0
  }

  /**
   * 获取内存使用情况（如果支持）
   */
  getMemoryUsage() {
    if (typeof wx !== 'undefined' && wx.getPerformance) {
      const performance = wx.getPerformance()
      if (performance.memory) {
        return {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
          usagePercentage: Math.round((performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize) * 100)
        }
      }
    }
    return null
  }

  /**
   * 主动检查内存使用情况
   */
  checkMemoryUsage() {
    const memoryInfo = this.getMemoryUsage()
    if (memoryInfo && memoryInfo.usagePercentage > 80) {
      console.warn('内存使用率过高:', memoryInfo.usagePercentage + '%')
      this.triggerPerformanceAlert('high_memory_usage', {
        memoryInfo,
        message: `内存使用率达到${memoryInfo.usagePercentage}%，建议清理资源`
      })
    }
    return memoryInfo
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.stopMonitoring()
    this.reset()

    // 移除内存警告监听
    if (typeof wx !== 'undefined' && wx.offMemoryWarning) {
      wx.offMemoryWarning(this.handleMemoryWarning.bind(this))
    }
  }
}

// 创建单例实例
const performanceMonitor = new PerformanceMonitor()

export default performanceMonitor
