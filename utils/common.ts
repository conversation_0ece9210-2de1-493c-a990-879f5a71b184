import { useStore } from 'vuex'

/**
 * 游客模式检查处理函数
 * @param callback 用户已注册时的回调函数
 * @returns 返回一个函数，用于执行游客模式检查
 */
export const handleGuestMode = <T extends (...args: any[]) => void | Promise<void>>(
	callback: T
) => {
	const store = useStore()
	return (...args: Parameters<T>) => {
		const userRegistered = store.getters.userRegistered
		if (!userRegistered) {
			uni.navigateTo({
				url: '/pages/register/index'
			})
			return
		}
		return callback(...args)
	}
}
