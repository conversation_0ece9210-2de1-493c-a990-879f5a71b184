import { config, agentUrl } from '../config.js'

interface RequestOptions {
	url: string
	data?: any
	method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
	useOrigin?: boolean
	withoutAuth?: boolean
	isAgent?: boolean,
	contentType?: string
}

interface StreamRequestOptions extends RequestOptions {
	onChunkReceived?: (chunk: string) => void
}

// 扩展 uni 类型定义
interface UniRequestTaskWithChunk extends UniApp.RequestTask {
	onChunkReceived: (callback: (res: { data: ArrayBuffer }) => void) => void
}

// 扩展 uni 类型定义
interface UniResponseData {
	code?: number | string
	data?: any
	msg?: string
	message?: string
	error?: string
	status?: string | number
}

class HTTP {
	private baseUrl: string

	constructor() {
		this.baseUrl = config.base_url
	}

	request({
		url,
		data = {},
		method = 'GET',
		useOrigin = false,
		withoutAuth = false,
		isAgent = false,
		contentType = 'application/json'
	}: RequestOptions): Promise<any> {
		return new Promise((resolve, reject) => {
			this._request(url, resolve, reject, data, method, useOrigin, withoutAuth, isAgent, contentType)
		})
	}

	streamRequest({
		url,
		data = {},
		method = 'GET',
		onChunkReceived,
		useOrigin = false,
		withoutAuth = false,
		contentType = 'application/json'
	}: StreamRequestOptions): Promise<any> {
		return new Promise((resolve, reject) => {
			this._streamRequest(
				url,
				resolve,
				reject,
				data,
				method,
				onChunkReceived,
				useOrigin,
				withoutAuth,
				contentType
			)
		})
	}

	formdataRequest({
		url,
		data,
		method = 'POST',
		useOrigin = false,
		withoutAuth = false,
		isAgent = false,
		contentType = 'multipart/form-data'
	}: RequestOptions): Promise<any> {
		// 这个方法在 http.js 中实现，TypeScript 这里只是声明
		throw new Error('formdataRequest should be implemented in http.js')
	}

	private _streamRequest(
		url: string,
		resolve: (value: any) => void,
		reject: (reason?: any) => void,
		data: any = {},
		method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
		onChunkReceived?: (chunk: string) => void,
		useOrigin = false,
		withoutAuth = false,
		contentType = 'application/json'
	): any {
		console.log(useOrigin)
		const requestTask = uni.request({
			url: `${agentUrl}${url}`,
			method: method,
			data: data,
			header: {
				'content-type': contentType,
				appId: config.appId,
				...(!withoutAuth ? { Authorization: `Bearer ${uni.getStorageSync('AuthTokens')}` } : {})
			},
			responseType: 'text',
			enableChunked: true,
			success: (res) => {
				resolve(res)
			},
			fail: (err: any) => {
				reject()
				this._show_error(err.msg || err.message || 'Request failed')
			}
		})

		// 使用类型断言解决类型问题
		;(requestTask as UniRequestTaskWithChunk).onChunkReceived((res: { data: ArrayBuffer }) => {
			try {
				const uint8Array = new Uint8Array(res.data)
				const resText = decodeURIComponent(
					escape(String.fromCharCode.apply(null, Array.from(uint8Array)))
				)
				if (onChunkReceived) onChunkReceived(resText)
			} catch (error) {
				console.warn(error)
			}
		})

		return requestTask
	}

	private _request(
		url: string,
		resolve: (value: any) => void,
		reject: (reason?: any) => void,
		data: any = {},
		method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
		useOrigin = false,
		withoutAuth?: boolean,
		isAgent = false,
		contentType = 'application/json'
	): void {
		uni.request({
			url: isAgent ? `${agentUrl}${url}` : `${this.baseUrl}${url}`,
			method: method,
			data: data,
			header: {
				'content-type': contentType,
				appId: config.appId,
				...(!withoutAuth ? { Authorization: `Bearer ${uni.getStorageSync('AuthTokens')}` } : {})
			},
			success: (res: any) => {
				if (res.data) {
					if (!useOrigin) {
						const resData = res.data as UniResponseData
						const _success = resData.code === 1 || resData.code === 200 || resData.code === '200 OK'
						if (_success) {
							resolve(res.data)
						} else {
							if (isAgent) {
								reject(resData.error)
								const error_code = resData.status
								const _message = resData.error
								this._show_error(error_code, _message)
							} else {
								reject(resData.msg || resData.message)
								const error_code = resData.code
								const _message = resData.msg || resData.message
								this._show_error(error_code, _message)
							}
						}
					} else {
						resolve(res.data)
					}
				} else {
					reject(res.msg || res.message || 'Response data is empty')
				}
			},
			fail: (err: any) => {
				reject()
				this._show_error(err.msg || err.message || 'Request failed')
			}
		})
	}

	private _show_error(error_code?: string | number, _message?: string): void {
		uni.showToast({
			title: `${_message || error_code}`,
			icon: 'none',
			duration: 2000
		})
	}
}

export { HTTP }
