export function isPointInPolygon(latitude, longitude, polygon) {
	let count = 0
	for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
		let xi = polygon[i][0],
			yi = polygon[i][1]
		let xj = polygon[j][0],
			yj = polygon[j][1]
		let intersect =
			yi > longitude != yj > longitude && latitude < ((xj - xi) * (longitude - yi)) / (yj - yi) + xi
		if (intersect) {
			count++
		}
	}
	return count % 2 !== 0
}
export function findCommonUrl(str1, str2) {
	const end = Math.min(str1.length, str2.length)
	let i = 0
	while (i < end && str1[i] === str2[i]) {
		i++
	}
	return str1.substring(0, i)
}
