export function isPointInPolygon(latitude, longitude, polygon) {
	let count = 0
	for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
		let xi = polygon[i][0],
			yi = polygon[i][1]
		let xj = polygon[j][0],
			yj = polygon[j][1]
		let intersect =
			yi > longitude != yj > longitude && latitude < ((xj - xi) * (longitude - yi)) / (yj - yi) + xi
		if (intersect) {
			count++
		}
	}
	return count % 2 !== 0
}
export function findCommonUrl(str1, str2) {
	const end = Math.min(str1.length, str2.length)
	let i = 0
	while (i < end && str1[i] === str2[i]) {
		i++
	}
	return str1.substring(0, i)
}

// 经纬度转换为米的常数因子（在北京附近40°N左右）
// 纬度1度约等于111km，经度1度约等于85km
const LAT_TO_METERS = 111000; // 纬度1度对应的米数
const LNG_TO_METERS = 85000;  // 经度1度对应的米数（在北京附近）

/**
 * 判断新的经纬度点是否在圆内，并计算到圆心的距离
 * @param {Object} center - 圆心 {longitude: number, latitude: number}
 * @param {number} radiusInMeters - 圆半径（米）
 * @param {Object} point - 要检查的点 {longitude: number, latitude: number}
 * @return {Object} 返回结果 {isInside: boolean, distanceInMeters: number}
 */
export function checkPointInCircle(center, radiusInMeters, point) {
  // 计算平面距离（近似）
  const latDiff = (point.latitude - center.latitude) * LAT_TO_METERS;
  const lngDiff = (point.longitude - center.longitude) * LNG_TO_METERS;

  const distanceInMeters = Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);

  return {
    isInside: distanceInMeters <= radiusInMeters,
    distanceInMeters
  };
}
