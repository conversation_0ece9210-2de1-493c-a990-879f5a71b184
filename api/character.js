import { HTTP } from '@/utils/http.js'
const baseUrl = '/village/api/v1/equipment'

class EquipmentApi extends HTTP {
	getEquipmentType() {
		return this.request({
			url: `${baseUrl}/types`,
			method: 'GET'
		})
	}
	getEquipmentList(query) {
		return this.request({
			url: `${baseUrl}/list/${query}`,
			method: 'GET'
		})
	}
	getCurrentEquipment() {
		return this.request({
			url: `${baseUrl}/my`,
			method: 'GET'
		})
	}
	saveEquipment(data) {
		return this.request({
			url: `${baseUrl}/used`,
			method: 'post',
			data
		})
	}
}
export { EquipmentApi }
