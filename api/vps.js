import { HTTP } from '@/utils/http.js'

const HOST = 'https://unicity3dev-api.bdnrc.org.cn'
const baseUrl = '/xr/api/v1/ar'
const AUTH_TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIxOjExMzIzMDMzOTI2MTU4NzA0NjQiLCJyblN0ciI6InlxZEZveVhxclJid0NEM1FNMjU0MGpUOUYwY0o2RUFvIiwiaWF0IjoxNzUwODM2NjU5LCJpc3MiOiJCRE5SQy5PUkcuQ04iLCJqdGkiOiI3YjFjNjYxYzhlYWY0NDM0ODExYmI3MTY3YzRiYWU1MiIsInVzZXIiOiIxMTMyMzAzMzkyNjE1ODcwNDY0JHd3d3d3JDEiLCJ0eXBlIjoiVVNFUiJ9.ODU7ug2VWkoU73jhLj0xEHSppVmMfk0BZmNUi0hD2Ig'

class VpsApi extends HTTP {
  // TODO: 替换Uniapp请求方法
  async vpsRoam(formData) {
    // console.log('vpsRoam: '+JSON.stringify(formData.getData()))
    // console.log('formData.getData().buffer is null: '+!formData.getData().buffer)
    return new Promise((resolve, reject) => {
      let data = formData.getData();
      wx.request({
        url: `${HOST}${baseUrl}/roaming`,
        method: 'POST',
        data: data.buffer,
        header: {
        'Content-Type': data.contentType,
        // TODO: 使用真实Token
        'Authorization': `Bearer ${AUTH_TOKEN}`
        },
        success: (res) => {
          if (res.data && res.data.code === 1) {
          console.log('vps tracking success: '+JSON.stringify(res.data))
          resolve(res.data.data); // 成功时返回助手回复
          } else {
          console.log('vps tracking failed: '+JSON.stringify(res.data))
          reject(new Error('未能定位成功'));
          }
        },
        fail: (error) => {
          reject(error); // 请求失败时返回错误
        }
      })
    })
     //  if (res && res.code === 1) {
     //    console.log('vps tracking success: '+JSON.stringify(res))
     //    return res.data; // 成功时返回助手回复
     //  } else {
      // console.log('vps tracking failed: '+JSON.stringify(res))
     //    throw new Error('未能定位成功');
     //  }
  }

  getAssetList(projectId) {
  	  return new Promise((resolve, reject) => {
  	  		  wx.request({
  	  		    url: `${HOST}${baseUrl}/project/asset/${projectId}`,
  	  		    method: 'GET',
  	  			header: {
  	  				'Authorization': `Bearer ${AUTH_TOKEN}`
  	  			},
  	  			success: (res) => {
  	  				if (res.data) {
  	  					console.log('获取资产列表成功：'+JSON.stringify(res.data))
  	  					resolve(res.data.data)
  	  				} else {
  	  					console.log('获取资产列表失败：'+JSON.stringify(res.data))
  	  					reject(new Error('获取资产列表失败'))
  	  				}
  	  			},
  	  			fail: (error) => {
  	  				reject(error)
  	  			}
  	  		  })
  	  })
  }
}

export { VpsApi }
