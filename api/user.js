import { HTTP } from '@/utils/http.js'

const baseUrl = '/village/api/v1/user'

class UserApi extends HTTP {
	getUserProfile() {
		return this.request({
			url: `${baseUrl}/profile`,
			method: 'GET'
		})
	}
	checkUserRegistered(openId) {
		return this.request({
			url: `/village/api/user/registered`,
			method: 'POST',
			data: {
				openId
			}
		})
	}

	getUserInviteCode() {
		return this.request({
			url: '/village/api/v1/invite/code',
			method: 'GET'
		})
	}

	getUserInviteCount() {
		return this.request({
			url: `${baseUrl}/invite/count`,
			method: 'GET'
		})
	}
}
export { UserApi }
