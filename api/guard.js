import { HTTP } from '@/utils/http.js'

const baseVisualUrl = '/village/api/v1/guard'

class GuardApi extends HTTP {
	/**
	 * @description:  submitGuardRecord
	 * @param {Object} data
	 * @param {string} data.title 标题
	 * @param {string} data.content 文章内容
	 * @param {number} data.hoofprintId 蹄窝id
	 * @param {string=} data.latitude 纬度
	 * @param {string=} data.location 所在位置
	 * @param {string=} data.longitude 经度
	 * @param {string[]=} data.urls 图片URL
	 * @return {Promise}
	 */
	deleteGuardRecord(id) {
		return this.request({
			url: `${baseVisualUrl}`,
			method: 'DELETE',
			data: {
				id
			}
		})
	}
	guardLikes(id) {
		return this.request({
			url: `${baseVisualUrl}/likes`,
			method: 'POST',
			data: {
				id
			}
		})
	}
	guardUnLikes(id) {
		return this.request({
			url: `${baseVisualUrl}/unlikes`,
			method: 'POST',
			data: {
				id
			}
		})
	}
	submitGuardRecord(data) {
		return this.request({
			url: `${baseVisualUrl}/issue`,
			method: 'POST',
			data
		})
	}
	getGuardApply() {
		return this.request({
			url: `${baseVisualUrl}/apply`,
			method: 'POST'
		})
	}
	getGuardSignin(hoofprintId) {
		return this.request({
			url: `${baseVisualUrl}/signin`,
			method: 'POST',
			data: {
				hoofprintId
			}
		})
	}
	getGuardMaintain({ eventId, guardType, hoofprintId }) {
		return this.request({
			url: `${baseVisualUrl}/maintain`,
			method: 'POST',
			data: {
				eventId,
				guardType,
				hoofprintId
			}
		})
	}
	getGuardHoofprint() {
		return this.request({
			url: `${baseVisualUrl}/hoofprint/event`,
			method: 'GET'
		})
	}
	getGuardMemberList(hoofprintId) {
		return this.request({
			url: `${baseVisualUrl}/members`,
			method: 'post',
			data: {
				hoofprintId
			}
		})
	}
	getGuardRecordList() {
		return this.request({
			url: `${baseVisualUrl}/records`,
			method: 'GET'
		})
	}
	getGuardIssue({ title, content, hoofprintId, latitude, location, longitude, urls, isLocal }) {
		return this.request({
			url: `${baseVisualUrl}/issue`,
			method: 'POST',
			data: {
				title,
				content,
				hoofprintId,
				latitude,
				location,
				longitude,
				urls,
				isLocal
			}
		})
	}
	getGuardIssueRecordList({ pageNum, pageSize, onlyMe, timeLimit, isFeatured, isLatest }) {
		return this.request({
			url: `${baseVisualUrl}/issue/records`,
			method: 'POST',
			data: {
				pageNum,
				pageSize,
				onlyMe,
				timeLimit,
				isFeatured,
				isLatest
			}
		})
	}

	getCheckApplied() {
		return this.request({
			url: `${baseVisualUrl}/check_applied`,
			method: 'get'
		})
	}

	getCheckVolunteer() {
		return this.request({
			url: `${baseVisualUrl}/volunteer`,
			method: 'post'
		})
	}

	getVideoNews({ pageNum, pageSize }) {
		return this.request({
			url: `${baseVisualUrl}/news`,
			method: 'post',
			data: {
				pageNum,
				pageSize
			}
		})
	}

	getUserIdCard() {
		return this.request({
			url: `${baseVisualUrl}/idcard`,
			method: 'get'
		})
	}

	getGuardShare() {
		return this.request({
			url: `${baseVisualUrl}/share`,
			method: 'get'
		})
	}

	setGuardArticleToTop(id) {
		return this.request({
			url: `${baseVisualUrl}/article/pinned`,
			method: 'POST',
			data: {
				id
			}
		})
	}

	cancleGuardArticleToTop(id) {
		return this.request({
			url: `${baseVisualUrl}/article/pinned`,
			method: 'DELETE',
			data: {
				id
			}
		})
	}

	setGuardArticleFeatured(id) {
		return this.request({
			url: `${baseVisualUrl}/article/featured`,
			method: 'POST',
			data: {
				id
			}
		})
	}

	cancelGuardArticleFeatured(id) {
		return this.request({
			url: `${baseVisualUrl}/article/featured`,
			method: 'DELETE',
			data: {
				id
			}
		})
	}
}

export { GuardApi }
