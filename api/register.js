import { HTTP } from '@/utils/http.js'

const baseIndex = '/auth/api'

class RegisterApi extends HTTP {
	// Register API
	register(data) {
		return this.request({
			url: `${baseIndex}/reg/wx`,
			method: 'post',
			data
		})
	}

	// UserName Check API
	userNameJudgment(userName) {
		return this.request({
			url: `${baseIndex}/reg/${userName}/available`,
			method: 'post',
			data: {
				userName
			}
		})
	}

	// Get Tag API
	getTag() {
		return this.request({
			url: '/village/api/v1/index/labels',
			method: 'get'
		})
	}
}

export default new RegisterApi()
