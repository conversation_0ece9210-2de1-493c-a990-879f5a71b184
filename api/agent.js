import { HTTP } from '@/utils/http.js'
const baseUrl = '/api/v1'

class AgentApi extends HTTP {
	orderStream(data, onChunkReceived) {
		return this.streamRequest({
			url: `${baseUrl}/order/stream`,
			method: 'POST',
			data,
			onChunkReceived
		})
	}

	chatStream(data, onChunkReceived) {
		return this.streamRequest({
			url: `${baseUrl}/dialog/stream`,
			method: 'POST',
			data,
			onChunkReceived
		})
	}
	// 清除对话内容
	clearDialogService(data) {
		return this.request({
			url: `${baseUrl}/dialog/reset`,
			method: 'post',
			data,
			isAgent: true
		})
	}

	// 获取推荐问题
	getRecommendQuestion(data) {
		return this.request({
			url: `${baseUrl}/recommendation_question`,
			method: 'post',
			data,
			isAgent: true
		})
	}

	// 获取知识问答
	getKnowledgeQuestion(data) {
		return this.request({
			url: `${baseUrl}/questions`,
			method: 'post',
			data,
			isAgent: true
		})
	}

	getRelatedContent(data) {
		return this.request({
			url: `${baseUrl}/related_content`,
			method: 'post',
			data,
			isAgent: true
		})
	}
}
export { AgentApi }
