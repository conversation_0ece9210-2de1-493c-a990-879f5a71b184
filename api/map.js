import { HTTP } from '@/utils/http.js'

const baseIndex = '/village/api/v1'

class mapApi extends HTTP {
	getScenicPoi() {
		return this.request({
			url: `${baseIndex}/scenic/poi`,
			method: 'GET'
		})
	}
	getMuseumPoi() {
		return this.request({
			url: `${baseIndex}/scenic/museum`,
			method: 'GET'
		})
	}
}
export const getMuseumUrl = (museumName) => {
	// https://shuiyuzui-cdn.bdnrc.org.cn/model/20240926/qiusiting/point_cloud.splat
	return `http://192.168.1.97:8080/?splatUrl=https%3A%2F%2Fshuiyuzui-cdn.bdnrc.org.cn%2Fyunbowu%2F20241128%2F${encodeURIComponent(museumName)}`
}
export default new mapApi()
