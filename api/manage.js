import { HTTP } from '@/utils/http.js'

const baseIndex = '/auth/api'

class ManageApi extends HTTP {
	// Logout API
	logout() {
		return this.request({
			url: `${baseIndex}/logout`,
			method: 'delete'
		})
	}

	// Deregister API
	deregister() {
		return this.request({
			url: `${baseIndex}/deregister`,
			method: 'delete'
		})
	}

	editInfo(data) {
		return this.request({
			url: `/village/api/v1/user`,
			method: 'put',
			data
		})
	}
}

export default new ManageApi()
