{
	"pages": [
		{
			"path": "pages/startup/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/activity/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/activity/apply",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/guardEquity",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/visual/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/visual/scene",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/manage/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/manage/settings",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/manage/about",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/manage/aboutmini",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/manage/aboutterms",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/manage/aboutprivacy",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/manage/interestLevel",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/manage/edit",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/manage/character",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/guard/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/guard/submit",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/guard/monitor",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/guard/records",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/register/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/character/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/volunteer/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/score/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/museum/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/museum/map",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/museum/museumScene",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/activity/form",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/activity/list",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/chat/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/ar/vps",
			"style": {
				"navigationStyle": "custom",
				"pageOrientation": "landscape",
				"usingComponents": {
					"xr-ar-plane": "/wxcomponents/xr-ar-plane/index"
				}
			}
		}
	],
	"tabBar": {
		"borderStyle": "white",
		"color": "#4D1F00",
		"selectedColor": "#D2781F",
		"spacing": "2px",
		"height": "42px",
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "/static/tab-index-icon.png",
				"selectedIconPath": "/static/tab-index-selected-icon.png"
			},
			{
				"pagePath": "pages/visual/index",
				"text": "云游古道",
				"iconPath": "/static/tab-visual-icon.png",
				"selectedIconPath": "/static/tab-visual-selected-icon.png"
			},
			{
				"pagePath": "pages/museum/index",
				"text": "云博物",
				"iconPath": "/static/tab-museum-icon.png",
				"selectedIconPath": "/static/tab-museum-selected-icon.png"
			},
			{
				"pagePath": "pages/manage/index",
				"text": "我的",
				"iconPath": "/static/tab-manage-icon.png",
				"selectedIconPath": "/static/tab-manage-selected-icon.png"
			}
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"pageOrientation": "portrait"
	},
	"uniIdRouter": {},
	"permission": {
		"scope.userLocation": {
			"desc": "您的位置信息将用于小程序位置接口的效果展示"
		},
		"scope.camera": {
			"desc": "您的摄像头将用于拍摄上传图片"
		},
		"scope.writePhotosAlbum": {
			"desc": "将读取您的相册将用于上传图片"
		}
	},
	"condition": {
		//模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "", //模式名称
				"path": "", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到
			}
		]
	},
	"requiredPrivateInfos": ["getLocation"]
}
