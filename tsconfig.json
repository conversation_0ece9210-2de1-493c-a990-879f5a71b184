// tsconfig.json
{
	"compilerOptions": {
		/* 基本选项 */
		"target": "ESNext", // 指定 ECMAScript 目标版本。ESNext 通常没问题，uniapp 编译时会处理兼容性。
		"module": "ESNext", // 指定模块代码生成标准。与 Vite/Vue3 配合良好。
		"strict": true, // 启用所有严格类型检查选项。强烈推荐，有助于编写更健壮的代码，初期迁移时可按需暂时关闭个别严格检查（如 noImplicitAny），但目标应是完全启用。
		"jsx": "preserve", // 在 .tsx 文件里支持 JSX：'preserve'（保留给 Vue 处理）、'react-native' 或 'react'。uniapp+Vue 需要 'preserve'。
		"importHelpers": true, // 从 tslib 导入辅助工具函数（如 __extends, __rest）。

		/* 模块解析选项 */
		"moduleResolution": "node", // 模块解析策略：'node' (Node.js) 或 'classic' (TypeScript pre-1.6)。uniapp 项目用 'node'。
		"resolveJsonModule": true, // 允许导入 .json 文件。
		"esModuleInterop": true, // 通过为所有导入创建命名空间对象，实现 CommonJS 和 ES 模块之间的互操作性。简化导入非 ES 模块的包。
		"allowSyntheticDefaultImports": true, // 允许从没有设置默认导出的模块中默认导入。许多库需要此项与 esModuleInterop 配合。
		"baseUrl": ".", // 解析非相对模块名的基准目录。设置为 "."。
		"paths": {
			// 模块名到基于 baseUrl 的路径映射。必须与 vite.config.js 或 vue.config.js 中的别名配置保持一致！
			"@/*": ["./*"] // 例如，将 @/ 开头的路径映射到 src/ 目录下
		},

		/* 源文件映射选项 */
		// "sourceMap": true, // 是否生成 .map 文件。（通常由 uniapp CLI 控制）
		// "outDir": "./dist", // 输出目录。（由 uniapp CLI 控制）

		/* JavaScript 支持 */
		"allowJs": true, // 允许编译 JavaScript 文件。**这是渐进式迁移的关键，允许 JS 和 TS 文件共存。**
		"checkJs": false, // 是否在 .js 文件中报告错误。迁移初期建议关闭，避免大量现有 JS 文件报错。可按需为特定 JS 文件顶部添加 // @ts-check 开启检查。

		/* 类型检查选项 */
		"noImplicitAny": true, // 在表达式和声明上有隐含 'any' 类型时报错。（strict:true 包含此项）
		"strictNullChecks": true, // 启用严格的 null 检查。（strict:true 包含此项）
		"strictFunctionTypes": true, // 启用严格的函数类型检查。（strict:true 包含此项）
		"strictBindCallApply": true, // 启用更严格的 bind、call 和 apply 方法检查。（strict:true 包含此项）
		"strictPropertyInitialization": true, // 确保类实例的非 undefined 属性在构造函数中被初始化。（strict:true 包含此项）
		// "noUnusedLocals": true, // 报告未使用的局部变量错误。（可根据团队规范开启）
		// "noUnusedParameters": true, // 报告未使用的参数错误。（可根据团队规范开启）
		// "noImplicitReturns": true, // 报告函数中不是所有代码路径都有返回值错误。（可根据团队规范开启）
		// "noFallthroughCasesInSwitch": true, // 报告 switch 语句的 fallthrough 情况。（可根据团队规范开启）

		/* 实验性选项 */
		// "experimentalDecorators": true, // 启用实验性的装饰器特性。
		// "emitDecoratorMetadata": true, // 为装饰器提供元数据支持。

		/* 高级选项 */
		"skipLibCheck": true, // 跳过所有声明文件（ *.d.ts）的类型检查。可以加快编译速度，但可能会隐藏类型定义问题。通常是安全的。
		"forceConsistentCasingInFileNames": true, // 禁止对同一个文件使用不同大小写的引用。有助于跨平台开发。
		"noEmit": true, // **重要：** 不生成输出文件（JavaScript）。因为编译和打包由 uniapp CLI（Vite/Webpack）负责，tsc/vue-tsc 只用于类型检查。
		"isolatedModules": true, // 将每个文件作为单独的模块（类似于 Babel 的行为）。Vite 等现代构建工具通常需要。

		/* 类型定义 */
		"lib": ["ESNext", "DOM"], // 编译过程中需要引入的库文件的列表，例如 'DOM' 用于访问 document 等浏览器 API，'ESNext' 支持最新 JS 特性。
		"types": [
			// 需要包含的类型定义包的名称。
			"node", // 如果你在代码中用到 Node.js 的 API (如在某些脚本里)
			"@dcloudio/types", // uniapp 官方核心类型
			"@uni-helper/uni-app-types",
			"vue"
			// "@uni-helper/uni-app-types/vue3" // 社区维护的 uniapp 类型补充(根据你使用的 Vue 版本选择 vue2 或 vue3)，可能更全，可选
			// 添加其他你可能使用的库的类型，例如 pinia (@pinia/testing) 等
		]
		// "typeRoots": [
		// 	// 指定包含类型声明的文件夹路径。默认是 node_modules/@types
		// 	"./node_modules/@types"
		// 	// 如果你有自定义的 .d.ts 文件放在特定目录，也可以加进来
		// 	// "./src/types"
		// ]
	},
	"files": ["types/uniapp.d.ts"],
	/* 文件包含/排除 */
	"include": [
		// 指定需要被 TypeScript 编译器处理的文件或目录
		"pages/**/*.ts", // 包含 src 目录下所有的 .ts 文件
		"pages/**/*.d.ts", // 包含 src 目录下所有的 .d.ts 声明文件
		"pages/**/*.tsx", // 包含 src 目录下所有的 .tsx 文件
		"pages/**/*.vue", // **重要：** 包含 src 目录下所有的 .vue 文件，这样 vue-tsc 才能检查它们
		"components/**/*.ts", // 包含 src 目录下所有的 .ts 文件
		"components/**/*.d.ts", // 包含 src 目录下所有的 .d.ts 声明文件
		"components/**/*.tsx", // 包含 src 目录下所有的 .tsx 文件
		"components/**/*.vue", // **重要：** 包含 src 目录下所有的 .vue 文件，这样 vue-tsc 才能检查它们
		"utils/**/*.ts", // 包含 src 目录下所有的 .ts 文件
		"utils/**/*.d.ts", // 包含 src 目录下所有的 .d.ts 声明文件
		"service/**/*.ts", // 包含 src 目录下所有的 .ts 文件
		"service/**/*.d.ts", // 包含 src 目录下所有的 .d.ts 声明文件
		"types/**/*.d.ts", // 如果你有单独的 types 目录存放声明文件
		"types/uniapp.d.ts", // 如果你有单独的 types 目录存放声明文件
		"vite.config.ts", // 如果你的 vite 配置文件是 ts
		"*.ts" // 包含根目录下的ts文件，比如vite.config.ts
	],
	"exclude": [
		// 指定需要从编译过程中排除的文件或目录
		"node_modules",
		"dist", // uniapp 的编译输出目录
		"unpackage", // HBuilderX 可能使用的目录
		"**/__tests__/*", // 排除测试文件（如果测试框架有自己的 TS 配置）
		"**/node_modules/**"
	],
	"vueCompilerOptions": {
		// Vue 特定的 TypeScript 选项 (由 Volar/vue-tsc 读取)
		// "experimentalDisableTemplateSupport": true, // 如果你不需要模板内的类型检查，可以开启此项加速
		// 如果使用 Volar Take Over Mode，可能需要以下配置
		// "extensions": [".vue"],
		"target": 3
	}
}
