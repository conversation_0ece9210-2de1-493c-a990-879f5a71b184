import { HTTP } from '@/utils/http.js'
import type { ICommentQuery, ICommentData, IAddComments } from '@/types/comment'
import { PageResponse } from '@/types/base.d'

const baseUrl = '/village/api/v1/comments'

class CommentService extends HTTP {
	getComentList(query: ICommentQuery): Promise<PageResponse<ICommentData>> {
		return this.request({
			url: `${baseUrl}/list`,
			method: 'POST',
			data: query
		})
	}

	addComments(query: IAddComments): Promise<PageResponse<null>> {
		return this.request({
			url: `${baseUrl}`,
			method: 'POST',
			data: query
		})
	}

	likesComment(query: { id: number }): Promise<PageResponse<string>> {
		return this.request({
			url: `${baseUrl}/likes`,
			method: 'POST',
			data: query
		})
	}

	unlikesComment(query: { id: number }): Promise<PageResponse<string>> {
		return this.request({
			url: `${baseUrl}/unlikes`,
			method: 'POST',
			data: query
		})
	}
}

export default new CommentService()
