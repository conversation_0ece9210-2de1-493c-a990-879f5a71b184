import { HTTP } from '@/utils/http'
import {
	ActivityListParams,
	ActivityListResponse,
	ActivityDetailResponse,
	ActivityRegisterParams,
	ActivityCreateParams,
	ActivityUpdateParams
} from '@/types/activity.d'
import { PageResponse } from '@/types/base.d'
const baseUrl = '/village/api/v1/activity'

class ActivityService extends HTTP {
	getDetailedInfo(id: number): Promise<ActivityDetailResponse> {
		return this.request({
			url: `${baseUrl}/${id}`,
			method: 'GET'
		})
	}

	getActivityList(params: ActivityListParams): Promise<ActivityListResponse> {
		return this.request({
			url: `${baseUrl}/all`,
			method: 'POST',
			data: params
		})
	}

	postRegisterActivity(params: ActivityRegisterParams): Promise<PageResponse<object>> {
		return this.request({
			url: `${baseUrl}/register`,
			method: 'POST',
			data: params
		})
	}

	createActivitiy(params: ActivityCreateParams): Promise<PageResponse<{ id: number }>> {
		return this.request({
			url: `${baseUrl}`,
			method: 'POST',
			data: params
		})
	}

	updateActivitiy(params: ActivityUpdateParams): Promise<PageResponse<object>> {
		return this.request({
			url: `${baseUrl}`,
			method: 'PUT',
			data: params
		})
	}

	deleteActivitiy(id: number): Promise<PageResponse<object>> {
		return this.request({
			url: `${baseUrl}/${id}`,
			method: 'DELETE'
		})
	}
}

export default new ActivityService()
