import { HTTP } from '@/utils/http.js'
import {
	GuardMaintainParams,
	GuardIssueParams,
	GuardIssueRecordListParams,
	PageQueryParams,
	GuardRecordsResponse,
	GuardNewsResponse,
	GuardIssueRecordResponse,
	ScoreHistoryResponse,
	GuardIssueResponseData,
	GuardApplyResponseData,
	GuardMemberResponseData,
	GuardHoofprintEventData,
	GuardIdCardResponseData
} from '@/types/guard.d'
import { PageResponse, type PageParams } from '@/types/base.d'
const baseVisualUrl = '/village/api/v1/guard'

class GuardService extends HTTP {
	/**
	 * @description:  submitGuardRecord
	 * @param {Object} data
	 * @param {string} data.title 标题
	 * @param {string} data.content 文章内容
	 * @param {number} data.hoofprintId 蹄窝id
	 * @param {string=} data.latitude 纬度
	 * @param {string=} data.location 所在位置
	 * @param {string=} data.longitude 经度
	 * @param {string[]=} data.urls 图片URL
	 * @return {Promise}
	 */
	deleteGuardRecord(params: { id: number }): Promise<PageResponse<string>> {
		return this.request({
			url: `${baseVisualUrl}`,
			method: 'DELETE',
			data: params
		})
	}
	guardLikes(params: { id: number }): Promise<PageResponse<number>> {
		return this.request({
			url: `${baseVisualUrl}/likes`,
			method: 'POST',
			data: params
		})
	}
	guardUnLikes(params: { id: number }): Promise<PageResponse<number>> {
		return this.request({
			url: `${baseVisualUrl}/unlikes`,
			method: 'POST',
			data: params
		})
	}
	submitGuardRecord(params: GuardIssueParams): Promise<PageResponse<GuardIssueResponseData>> {
		return this.request({
			url: `${baseVisualUrl}/issue`,
			method: 'POST',
			data: params
		})
	}
	getGuardApply(): Promise<PageResponse<GuardApplyResponseData>> {
		return this.request({
			url: `${baseVisualUrl}/apply`,
			method: 'POST'
		})
	}
	getGuardSignin(params: { hoofprintId: number }): Promise<PageResponse<string>> {
		return this.request({
			url: `${baseVisualUrl}/signin`,
			method: 'POST',
			data: params
		})
	}
	getGuardMaintain(params: GuardMaintainParams): Promise<PageResponse<{ points: number }>> {
		return this.request({
			url: `${baseVisualUrl}/maintain`,
			method: 'POST',
			data: params
		})
	}
	getGuardHoofprint(): Promise<PageResponse<GuardHoofprintEventData>> {
		return this.request({
			url: `${baseVisualUrl}/hoofprint/event`,
			method: 'GET'
		})
	}
	getGuardMemberList(hoofprintId: number): Promise<PageResponse<GuardMemberResponseData>> {
		return this.request({
			url: `${baseVisualUrl}/members/${hoofprintId}`,
			method: 'GET'
		})
	}
	getGuardRecordList(): Promise<PageResponse<GuardRecordsResponse>> {
		return this.request({
			url: `${baseVisualUrl}/records`,
			method: 'GET'
		})
	}

	getGuardIssueRecordList(
		params: GuardIssueRecordListParams
	): Promise<PageResponse<GuardIssueRecordResponse>> {
		return this.request({
			url: `${baseVisualUrl}/issue/records`,
			method: 'POST',
			data: params
		})
	}

	getGuardIssue(params: GuardIssueParams): Promise<PageResponse<GuardIssueResponseData>> {
		return this.request({
			url: `${baseVisualUrl}/issue`,
			method: 'POST',
			data: params
		})
	}

	getCheckApplied(): Promise<PageResponse<string>> {
		return this.request({
			url: `${baseVisualUrl}/check_applied`,
			method: 'GET'
		})
	}

	getCheckVolunteer(): Promise<PageResponse<string>> {
		return this.request({
			url: `${baseVisualUrl}/volunteer`,
			method: 'POST'
		})
	}

	getVideoNews(params: PageQueryParams): Promise<PageResponse<GuardNewsResponse>> {
		return this.request({
			url: `${baseVisualUrl}/news`,
			method: 'POST',
			data: params
		})
	}

	getUserIdCard(): Promise<PageResponse<GuardIdCardResponseData>> {
		return this.request({
			url: `${baseVisualUrl}/id/card`,
			method: 'GET'
		})
	}

	getGuardShare(): Promise<PageResponse<string>> {
		return this.request({
			url: `${baseVisualUrl}/share`,
			method: 'GET'
		})
	}

	setGuardArticleToTop(params: { id: number }): Promise<PageResponse<number>> {
		return this.request({
			url: `${baseVisualUrl}/article/pinned`,
			method: 'POST',
			data: params
		})
	}

	cancleGuardArticleToTop(params: { id: number }): Promise<PageResponse<number>> {
		return this.request({
			url: `${baseVisualUrl}/article/pinned`,
			method: 'DELETE',
			data: params
		})
	}

	setGuardArticleFeatured(params: { id: number }): Promise<PageResponse<number>> {
		return this.request({
			url: `${baseVisualUrl}/article/featured`,
			method: 'POST',
			data: params
		})
	}

	cancelGuardArticleFeatured(params: { id: number }): Promise<PageResponse<number>> {
		return this.request({
			url: `${baseVisualUrl}/article/featured`,
			method: 'DELETE',
			data: params
		})
	}

	getScoreHistory(params: PageParams): Promise<PageResponse<ScoreHistoryResponse>> {
		return this.request({
			url: `${baseVisualUrl}/table`,
			method: 'POST',
			data: params
		})
	}
}

export default new GuardService()
