import { HTTP } from '@/utils/http'
import { ScenicListResponse } from '@/types/scenic.d'
const baseUrl = '/village/api/v1/scenic'

class ScenicService extends HTTP {
	getPoiList(): Promise<ScenicListResponse> {
		return this.request({
			url: `${baseUrl}/poi`,
			method: 'GET',
			data: {}
		})
	}

	getMuseumList(): Promise<ScenicListResponse> {
		return this.request({
			url: `${baseUrl}/museum`,
			method: 'GET',
			data: {}
		})
	}
}

export default new ScenicService()
