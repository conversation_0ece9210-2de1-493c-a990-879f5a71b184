import { HTTP } from '@/utils/http.js'
import { RoamingParams, RoamingResponseData, AssetResponseData } from '@/types/vps.d'
import { PageResponse } from '@/types/base.d'

const baseArUrl = '/xr/api/v1/ar'

class VpsService extends HTTP {
	vpsRoam({
		params,
		contentType
	}: {
		params: RoamingParams
		contentType: string
	}): Promise<PageResponse<RoamingResponseData>> {
		return this.formdataRequest({
			url: `${baseArUrl}/roaming`,
			method: 'POST',
			data: params,
			contentType
		})
	}

	getAssetList(projectId: number): Promise<PageResponse<AssetResponseData[]>> {
		return this.request({
			url: `${baseArUrl}/project/asset/${projectId}`,
			method: 'GET'
		})
	}
}

export default new VpsService()
