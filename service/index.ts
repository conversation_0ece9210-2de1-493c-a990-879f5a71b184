import { HTTP } from '@/utils/http.js'
import { IndexCardResponseData, IndexTrendsResponseData } from '@/types/index.d'
import { PageResponse } from '@/types/base.d'

const baseUrl = '/village/api/v1/index'
class IndexService extends HTTP {
	getUserCardInfo(): Promise<PageResponse<IndexCardResponseData>> {
		return this.request({
			url: `${baseUrl}/card`,
			method: 'GET'
		})
	}
	getTrendsList(): Promise<PageResponse<IndexTrendsResponseData>> {
		return this.request({
			url: `${baseUrl}/trends`,
			method: 'GET'
		})
	}
}
export default new IndexService()
