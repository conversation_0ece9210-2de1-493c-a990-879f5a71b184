const xr = wx.getXrFrameSystem();

var radianToAngle = function(radian) {
  return radian * 180 / Math.PI
}

// pose2为queryPose, pose1为vpsPose
var calculateTransformationMatrix = function(pose2, pose1) {
  try {
    const matrixVps = xr.Matrix4.composeTQS(pose1.position, pose1.quaternion, xr.Vector3.ONE)

    //右手系转左手
    const matrixSlam = convertLeftToRightMatrixY(pose2)

    const transformationMatrix = matrixSlam.multiply(matrixVps.inverse())
    const transformationMatrixArray = transformationMatrix.toArray()
    console.log('transformationMatrixArray: '+transformationMatrixArray)
    return transformationMatrixArray
  } catch (err) {
    console.error('[calculateTransformationMatrix error]: '+err)
  }
}

var matrixLeftToRightMatrixY = function(matrix){
  let leftToRight = new xr.Matrix4()
	leftToRight.setRow(xr.Vector4.createFromNumber(1,0,0,0), 0)
	leftToRight.setRow(xr.Vector4.createFromNumber(0,-1,0,0), 1)
	leftToRight.setRow(xr.Vector4.createFromNumber(0,0,1,0), 2)
	leftToRight.setRow(xr.Vector4.createFromNumber(0,0,0,1), 3)

	let convertedMatrix = leftToRight.multiply(matrix).multiply(leftToRight)
  return convertedMatrix
}

var convertLeftToRightMatrixY = function(pose) {
  let vpsMatrix = xr.Matrix4.composeTQS(pose.position, pose.quaternion, xr.Vector3.ONE)
  console.log('vpsMatrix left: '+JSON.stringify(vpsMatrix))

  let leftToRight = new xr.Matrix4()
	leftToRight.setRow(xr.Vector4.createFromNumber(1,0,0,0), 0)
	leftToRight.setRow(xr.Vector4.createFromNumber(0,-1,0,0), 1)
	leftToRight.setRow(xr.Vector4.createFromNumber(0,0,1,0), 2)
	leftToRight.setRow(xr.Vector4.createFromNumber(0,0,0,1), 3)

	let convertedMatrix = leftToRight.multiply(vpsMatrix).multiply(leftToRight)
  console.log('vps right :'+JSON.stringify(convertedMatrix))
  return convertedMatrix
}

//旋转矩阵【0，-270，180】的逆
var composeRotationMappingMatrix = function() {
  let rotationMappingMatrix = xr.Matrix4.composeTQS(xr.Vector3.ZERO, xr.Quaternion.fromEulerAngles(xr.Vector3.createFromNumber(0, -3*Math.PI/2, Math.PI)), xr.Vector3.ONE)
  rotationMappingMatrix = rotationMappingMatrix.inverse()
  const rotationMappingMatixArray = rotationMappingMatrix.toArray()
  console.log('rotationMappingMatixArray: '+rotationMappingMatixArray)
  return rotationMappingMatixArray
}

var getTransformedPose = function(transformMatrixArray, pose) {
  let slamQuaternion = pose.quaternion
  let slamMatrix = xr.Matrix4.composeTQS(pose.position, slamQuaternion, xr.Vector3.ONE)
  //console.log('transformMatrixArray: '+(JSON.stringify(transformMatrixArray))+ transformMatrixArray.length)
  const transformMatrix = xr.Matrix4.createFromArray(transformMatrixArray)
  let vpsMatrix = transformMatrix.multiply(slamMatrix)
  let correctedPose = matrixToPose(vpsMatrix)
  return correctedPose
}

var matrixToPose = function(matrix) {
  const posColumnArray = matrix.getColumn(3).toArray()
  let pos = xr.Vector3.createFromNumber(posColumnArray[0], posColumnArray[1], posColumnArray[2])
  let quat = xr.Quaternion.createFromMatrix4(matrix)
  return {position: pos, quaternion: quat}
}

export {
  calculateTransformationMatrix,
  getTransformedPose,
  radianToAngle,
  composeRotationMappingMatrix,
  matrixLeftToRightMatrixY,
  matrixToPose
}
