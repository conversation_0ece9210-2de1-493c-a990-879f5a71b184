const loadWasmFromAssets = async () => {
  return new Promise((resolve, reject) => {
    const fs = wx.getFileSystemManager();
    const wasmPath = `/static/yuv_to_rgb_O2.wasm`;

    // 将本地文件复制到临时路径
    fs.access({
      path: wasmPath,
      success: () => {
        console.log('Wasm file already exists:', wasmPath);
        instantiateWasm(wasmPath, resolve, reject);
      },
      fail: () => {
        console.error('Failed to load Wasm file:', err);
        reject(err);
      },
    });
  });
};

const width = 1920
const height = 1440
const memorySize = width * height * 4;

// 实例化 WebAssembly 模块
const instantiateWasm = (wasmPath, resolve, reject) => {
  wx.getFileSystemManager().readFile({
    filePath: wasmPath,
    success: (res) => {
      WXWebAssembly.instantiate(wasmPath, {
        env: {
          memory: new WXWebAssembly.Memory({initial: Math.ceil(memorySize / 65536), maximum: Math.ceil(memorySize / 65536)+1})
        }
      })
        .then((wasmInstance) => {
          console.log('Wasm loaded successfully!');
          resolve(wasmInstance.instance.exports);
        })
        .catch((err) => {
          console.error('Error instantiating Wasm:', err);
          reject(err);
        });
    },
    fail: (err) => {
      console.error('Failed to read Wasm file:', err);
      reject(err);
    },
  });
};

export { loadWasmFromAssets }
