/**
 * 设备能力检测工具类
 * 用于检测设备的AR支持和深度剔除能力
 */

class DeviceCapability {
  constructor() {
    this.systemInfo = null;
    this.capabilities = {
      isARSupported: false,
      isDepthMaskSupported: false
    };
  }

  /**
   * 初始化设备信息
   */
  init() {
    try {
      this.systemInfo = wx.getSystemInfoSync();
      console.log('设备系统信息:', this.systemInfo);
      return true;
    } catch (error) {
      console.error('获取系统信息失败:', error);
      return false;
    }
  }

  /**
   * 检测所有设备能力
   * @returns {Object} 包含AR支持和深度剔除支持的对象
   */
  detectCapabilities() {
    if (!this.systemInfo && !this.init()) {
      return this.capabilities;
    }

    const isARSupported = this.checkARSupport();
    const isDepthMaskSupported = this.checkDepthMaskSupport();

    this.capabilities = {
      isARSupported,
      isDepthMaskSupported
    };

    console.log('设备能力检测结果:', this.capabilities);
    return this.capabilities;
  }

  /**
   * 检测AR支持
   * @returns {boolean} 是否支持AR
   */
  checkARSupport() {
    if (!this.systemInfo) return false;

    const { platform, system, model, brand } = this.systemInfo;

    // iOS设备AR支持检测
    if (platform === 'ios') {
      const iosVersion = this.getIOSVersion(system);
      return iosVersion >= 11;
    }

    // Android设备AR支持检测
    if (platform === 'android') {
      const androidVersion = this.getAndroidVersion(system);
      if (androidVersion >= 7.0) {
        return this.checkARCoreSupport(brand, model);
      }
    }

    return false;
  }

  /**
   * 检测深度剔除支持
   * @returns {boolean} 是否支持深度剔除
   */
  checkDepthMaskSupport() {
    if (!this.systemInfo) return false;

    const { platform, system, model, brand } = this.systemInfo;

    // iOS设备深度剔除支持检测
    if (platform === 'ios') {
      const iosVersion = this.getIOSVersion(system);
      if (iosVersion >= 14) {
        return this.checkLiDARSupport(model);
      }
    }

    // Android设备深度剔除支持检测
    if (platform === 'android') {
      const androidVersion = this.getAndroidVersion(system);
      if (androidVersion >= 8.0) {
        return this.checkDepthAPISupport(brand, model);
      }
    }

    return false;
  }

  /**
   * 生成AR系统配置字符串
   * @param {boolean} isARSupported 是否支持AR
   * @param {boolean} isDepthMaskSupported 是否支持深度剔除
   * @returns {string} AR系统配置字符串
   */
  generateARSystemConfig(isARSupported = null, isDepthMaskSupported = null) {
    // 如果没有传入参数，使用当前检测结果
    const arSupported = isARSupported !== null ? isARSupported : this.capabilities.isARSupported;
    const depthSupported = isDepthMaskSupported !== null ? isDepthMaskSupported : this.capabilities.isDepthMaskSupported;

    if (!arSupported) {
      return "modes:Plane; planeMode: 1;";
    }

    if (depthSupported) {
      return "modes:Plane; planeMode: 1; depthMask: true; depthNear: 0.1; depthFar: 100;";
    } else {
      return "modes:Plane; planeMode: 1;";
    }
  }

  /**
   * 获取完整的设备能力报告
   * @returns {Object} 完整的设备能力信息
   */
  getCapabilityReport() {
    const capabilities = this.detectCapabilities();
    const arSystemConfig = this.generateARSystemConfig();

    return {
      systemInfo: this.systemInfo,
      capabilities,
      arSystemConfig,
      deviceDetails: {
        platform: this.systemInfo?.platform,
        system: this.systemInfo?.system,
        model: this.systemInfo?.model,
        brand: this.systemInfo?.brand
      }
    };
  }

  /**
   * 获取iOS版本号
   * @param {string} system 系统信息字符串
   * @returns {number} iOS主版本号
   */
  getIOSVersion(system) {
    try {
      const match = system.match(/iOS (\d+)\.(\d+)/);
      return match ? parseInt(match[1]) : 0;
    } catch (error) {
      console.error('解析iOS版本失败:', error);
      return 0;
    }
  }

  /**
   * 获取Android版本号
   * @param {string} system 系统信息字符串
   * @returns {number} Android版本号
   */
  getAndroidVersion(system) {
    try {
      const match = system.match(/Android (\d+)/);
      return match ? parseInt(match[1], 10) : 0;
    } catch (error) {
      console.error('解析Android版本失败:', error);
      return 0;
    }
  }

  /**
   * 检查微信小程序AR支持（基于官方支持列表）
   * @param {string} brand 设备品牌
   * @param {string} model 设备型号（内部代码）
   * @returns {boolean} 是否支持微信小程序AR
   *
   * 注意：此方法检测的是微信小程序AR支持，不是通用ARCore支持
   * 参考：https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/visionkit/plane.html
   *
   * V1平面AR要求：
   * - iOS: iPhone 6s及以上
   * - Android: Android 7.0及以上 + 官方支持列表中的机型
   *
   * V2平面AR要求：
   * - iOS: iPhone 7及以上
   * - Android: 仅官方列表中的特定机型
   */
  checkARCoreSupport(brand, model) {
    const brandLower = brand.toLowerCase();

    // 基于品牌的判断（仅微信官方支持的品牌）
    if (this.isBrandSupportingAR(brandLower)) {
      return this.isKnownARCoreSupportedModelCode(model, brandLower);
    }

    // 检查子品牌
    if (this.isSubBrand(brandLower, model)) {
      const parentBrand = this.getParentBrand(brandLower);
      return this.isBrandSupportingAR(parentBrand) ? this.isKnownARCoreSupportedModelCode(model, parentBrand) : false;
    }

    return false;
  }

  /**
   * 检查是否为子品牌
   * @param {string} brand 品牌名（小写）
   * @param {string} model 设备型号（预留参数，用于将来更精确的判断）
   * @returns {boolean} 是否为子品牌
   */
  isSubBrand(brand, model) {
    // 预留model参数用于将来更精确的子品牌判断
    void model; // 避免未使用参数警告

    const subBrands = {
      'iqoo': 'vivo',
      'redmi': 'xiaomi',
      'poco': 'xiaomi',
      'honor': 'huawei',
      'realme': 'oppo',
      'oneplus': 'oppo', // OnePlus现在是OPPO子品牌
      'nothing': 'oneplus' // Nothing由OnePlus创始人创立
    };

    return brand in subBrands;
  }

  /**
   * 获取父品牌
   * @param {string} brand 子品牌名（小写）
   * @returns {string} 父品牌名
   */
  getParentBrand(brand) {
    const subBrands = {
      'iqoo': 'vivo',
      'redmi': 'xiaomi',
      'poco': 'xiaomi',
      'honor': 'huawei',
      'realme': 'oppo',
      'oneplus': 'oppo',
      'nothing': 'oneplus'
    };

    return subBrands[brand] || brand;
  }

  /**
   * 检查LiDAR支持
   * @param {string} model 设备型号（可能包含可读名称和内部标识符）
   * @returns {boolean} 是否支持LiDAR
   */
  checkLiDARSupport(model) {
    // iOS设备的LiDAR支持基于内部型号代码
    const lidarModelCodes = [
      // iPhone 12 Pro系列
      'iPhone13,3', 'iPhone13,4', // iPhone 12 Pro, iPhone 12 Pro Max

      // iPhone 13 Pro系列
      'iPhone14,3', 'iPhone14,4', // iPhone 13 Pro, iPhone 13 Pro Max

      // iPhone 14 Pro系列
      'iPhone15,3', 'iPhone15,4', // iPhone 14 Pro, iPhone 14 Pro Max

      // iPhone 15 Pro系列
      'iPhone16,1', 'iPhone16,2', // iPhone 15 Pro, iPhone 15 Pro Max

      // iPhone 16 Pro系列
      'iPhone17,1', 'iPhone17,2', // iPhone 16 Pro, iPhone 16 Pro Max

      // iPad Pro系列（支持LiDAR的型号）
      'iPad13,1', 'iPad13,2', // iPad Pro 12.9" (5th gen)
      'iPad13,4', 'iPad13,5', 'iPad13,6', 'iPad13,7', // iPad Pro 11" (3rd gen)
      'iPad13,8', 'iPad13,9', 'iPad13,10', 'iPad13,11', // iPad Pro 12.9" (5th gen)
      'iPad14,3', 'iPad14,4', // iPad Pro 11" (4th gen)
      'iPad14,5', 'iPad14,6', // iPad Pro 12.9" (6th gen)
    ];

    // 提取内部标识符进行匹配
    const internalIdentifier = this.extractiOSInternalIdentifier(model);
    if (internalIdentifier) {
      return lidarModelCodes.includes(internalIdentifier);
    }

    // 如果没有内部标识符，直接用model进行匹配（向后兼容）
    return lidarModelCodes.includes(model);
  }

  /**
   * 从iOS设备model中提取内部标识符
   * @param {string} model 设备型号（如 "iPhone 15 pro<iPhone16,1>"）
   * @returns {string|null} 内部标识符（如 "iPhone16,1"）或null
   *
   * 支持的格式：
   * - "iPhone 15 pro<iPhone16,1>" -> "iPhone16,1"
   * - "iPad Pro<iPad14,3>" -> "iPad14,3"
   * - "iPhone16,1" -> "iPhone16,1" (直接返回)
   */
  extractiOSInternalIdentifier(model) {
    if (!model) return null;

    // 匹配尖括号中的内容：<iPhone16,1>
    const match = model.match(/<([^>]+)>/);
    if (match) {
      return match[1];
    }

    // 如果没有尖括号，检查是否已经是内部标识符格式
    // 内部标识符格式：iPhone/iPad + 数字 + 逗号 + 数字
    if (/^(iPhone|iPad)\d+,\d+$/.test(model)) {
      return model;
    }

    return null;
  }

  /**
   * 检查深度API支持
   * @param {string} brand 设备品牌
   * @param {string} model 设备型号（内部代码）
   * @returns {boolean} 是否支持深度API
   */
  checkDepthAPISupport(brand, model) {
    const brandLower = brand.toLowerCase();

    // 基于品牌的保守判断（只有确认支持的品牌）
    if (this.isBrandSupportingDepthAPI(brandLower)) {
      return this.isKnownDepthSupportedModelCode(model, brandLower);
    }

    // 检查子品牌的已知支持设备
    if (this.isSubBrand(brandLower, model)) {
      const parentBrand = this.getParentBrand(brandLower);
      return this.isBrandSupportingDepthAPI(parentBrand) ? this.isKnownDepthSupportedModelCode(model, parentBrand) : false;
    }

    return false;
  }

  /**
   * 基于品牌判断是否支持AR（基于微信小程序官方支持列表）
   * @param {string} brandLower 设备品牌（小写）
   * @returns {boolean} 是否支持AR
   */
  isBrandSupportingAR(brandLower) {
    // 基于微信小程序官方AR支持列表的品牌
    // 参考：https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/visionkit/plane.html
    const wechatARSupportedBrands = [
      // V2平面AR官方支持的品牌
      'oppo', 'vivo', 'iqoo', 'realme', 'samsung', 'huawei', 'honor',
      'xiaomi', 'redmi', 'oneplus', 'rog', 'nubia', 'lenovo', 'blackshark'
    ];

    return wechatARSupportedBrands.includes(brandLower);
  }

  /**
   * 基于品牌判断是否支持深度API（不依赖model内容）
   * @param {string} brandLower 设备品牌（小写）
   * @returns {boolean} 是否支持深度API
   */
  isBrandSupportingDepthAPI(brandLower) {
    // 基于实际测试经验的保守判断
    // 只有确认支持深度剔除效果好的品牌才返回true
    const confirmedDepthSupportedBrands = [
      'samsung', 'google', 'vivo', 'iqoo', 'oneplus', 'nothing'
    ];

    return confirmedDepthSupportedBrands.includes(brandLower);
  }

  /**
   * 检查是否为已知的高端设备型号代码
   * @param {string} model 设备型号（内部代码）
   * @param {string} brandLower 设备品牌（小写）
   * @returns {boolean} 是否为已知高端设备
   */
  isKnownHighEndModelCode(model, brandLower) {
    // 基于已知的高端设备型号代码进行精确匹配
    const knownHighEndCodes = this.getKnownHighEndModelCodes(brandLower);
    return knownHighEndCodes.some(code => model.includes(code));
  }

  /**
   * 检查是否为已知支持深度剔除的设备型号代码
   * @param {string} model 设备型号（内部代码）
   * @param {string} brandLower 设备品牌（小写）
   * @returns {boolean} 是否为已知支持深度剔除的设备
   */
  isKnownDepthSupportedModelCode(model, brandLower) {
    // 基于实际测试验证的深度剔除支持设备
    const knownDepthSupportedCodes = this.getKnownDepthSupportedModelCodes(brandLower);
    return knownDepthSupportedCodes.some(code => model.includes(code));
  }

  /**
   * 检查是否为已知支持ARCore的设备型号代码
   * @param {string} model 设备型号（内部代码）
   * @param {string} brandLower 设备品牌（小写）
   * @returns {boolean} 是否为已知支持ARCore的设备
   */
  isKnownARCoreSupportedModelCode(model, brandLower) {
    // 基于官方ARCore支持列表和实际测试验证的ARCore支持设备
    const knownARCoreSupportedCodes = this.getKnownARCoreSupportedModelCodes(brandLower);
    return knownARCoreSupportedCodes.some(code => model.includes(code));
  }

  /**
   * 获取已知的高端设备型号代码
   * @param {string} brandLower 设备品牌（小写）
   * @returns {Array<string>} 高端设备型号代码列表
   */
  getKnownHighEndModelCodes(brandLower) {
    switch (brandLower) {
      case 'xiaomi':
      case 'redmi':
        return [
          // 小米13系列
          '2211133C', '2210132C', '2211133G',
          // 小米12系列
          '2201123C', '2201122C', '2201123G',
          // 小米11系列
          'M2011K2C', 'M2102K1C', 'M2011K2G',
          // Redmi K系列
          '21121210C', '22011211C', '22041211AC'
        ];

      case 'vivo':
      case 'iqoo':
        return [
          // iQOO设备型号代码
          'V2217A', 'V2218A', 'V2219A', 'V2220A', // iQOO 10系列
          'V2171A', 'V2172A', 'V2173A', 'V2174A', // iQOO 9系列
          'V2231A', 'V2232A', 'V2233A', 'V2234A', // iQOO Neo7系列
          // Vivo X系列
          'V2158A', 'V2159A', 'V2160A'
        ];

      case 'samsung':
        return [
          // Galaxy S系列
          'SM-G991', 'SM-G996', 'SM-G998', // S21系列
          'SM-G981', 'SM-G985', 'SM-G988', // S20系列
          // Galaxy Note系列
          'SM-N981', 'SM-N985', 'SM-N986'
        ];

      case 'huawei':
      case 'honor':
        return [
          // Mate系列
          'LIO-', 'VOG-', 'TAS-', 'OCE-',
          // P系列
          'ELE-', 'VOG-', 'ANA-', 'ALT-'
        ];

      case 'oppo':
      case 'oneplus':
      case 'realme':
        return [
          // OnePlus
          'LE2100', 'LE2110', 'LE2120', // OnePlus 9系列
          'IN2010', 'IN2020', 'IN2025', // OnePlus 8系列
          // OPPO Find系列
          'CPH2023', 'CPH2025', 'CPH2207'
        ];

      default:
        return [];
    }
  }

  /**
   * 获取已知支持深度剔除的设备型号代码（基于实际测试）
   * @param {string} brandLower 设备品牌（小写）
   * @returns {Array<string>} 支持深度剔除的设备型号代码列表
   */
  getKnownDepthSupportedModelCodes(brandLower) {
    switch (brandLower) {
      case 'vivo':
      case 'iqoo':
        return [
          // iQOO设备型号代码（实测支持深度剔除）
          'V2217A', 'V2218A', // iQOO 10
          'V2219A', 'V2220A', // iQOO 10 Pro
          'V2171A', 'V2172A', // iQOO 9
          'V2173A', 'V2174A', // iQOO 9 Pro
          'V2231A', 'V2232A', // iQOO Neo7
          'V2233A', 'V2234A', // iQOO Neo7 SE/竞速版
          // 其他已验证的iQOO型号
          'V2141A', 'V2142A', // iQOO 8
          'V2143A', 'V2144A', // iQOO 8 Pro
        ];

      case 'samsung':
        return [
          // Galaxy S系列（高端机型通常支持）
          'SM-G991', 'SM-G996', 'SM-G998', // S21系列
          'SM-G981', 'SM-G985', 'SM-G988', // S20系列
          'SM-G973', 'SM-G975', 'SM-G977', // S10系列
        ];

      case 'google':
        return [
          // Pixel系列（Google官方支持）
          'flame', 'coral', // Pixel 4系列
          'redfin', 'bramble', // Pixel 5系列
          'oriole', 'raven', // Pixel 6系列
          'bluejay', 'panther', 'cheetah', // Pixel 7系列
        ];

      case 'oneplus':
        return [
          // OnePlus高端机型
          'LE2100', 'LE2110', 'LE2120', // OnePlus 9系列
          'IN2010', 'IN2020', 'IN2025', // OnePlus 8系列
        ];

      // 小米设备暂时不包含，因为实测小米13不支持深度剔除
      // case 'xiaomi':
      // case 'redmi':
      //   return [];

      default:
        return [];
    }
  }

  /**
   * 获取微信小程序官方支持AR的设备型号代码
   * @param {string} brandLower 设备品牌（小写）
   * @returns {Array<string>} 微信小程序官方支持AR的设备型号代码列表
   */
  getKnownARCoreSupportedModelCodes(brandLower) {
    // 基于微信小程序官方AR支持列表
    // 参考：https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/visionkit/plane.html

    switch (brandLower) {
      case 'xiaomi':
      case 'redmi':
        return [
          // 官方列表中的小米设备型号代码（需要根据实际型号补充）
          '2211133C', '2210132C', '2211133G', // Xiaomi 13系列
          '2201123C', '2201122C', '2201123G', // 小米12系列
          'M2011K2C', 'M2102K1C', 'M2011K2G', // 小米11系列
          'M2007J3SY', 'M2007J17C', 'M2012K11AC', // 小米10系列
          'M2101K9C', 'M2101K9AG', 'M2101K6G', // 小米11系列
          // Redmi K系列
          '21121210C', '22011211C', '22041211AC', // Redmi K20/K30/K40系列
          '22081212C', '22041211AC', '2201116SC', // Redmi K50/K60系列
        ];

      case 'vivo':
      case 'iqoo':
        return [
          // 官方列表中的iQOO和Vivo设备
          'V2217A', 'V2218A', 'V2219A', 'V2220A', // iQOO 10系列
          'V2171A', 'V2172A', 'V2173A', 'V2174A', // iQOO 9系列
          'V2231A', 'V2232A', 'V2233A', 'V2234A', // iQOO Neo7系列
          'V2141A', 'V2142A', 'V2143A', 'V2144A', // iQOO 8系列
          'V2049A', 'V2050A', 'V2051A', 'V2052A', // iQOO 7系列
          'V2196A', 'V2197A', 'V2198A', // iQOO Neo6系列
          'V2118A', 'V2119A', 'V2120A', // iQOO Z5系列
          'V2073A', 'V2074A', // iQOO Z3系列
          // Vivo设备
          'V2158A', 'V2159A', 'V2160A', // Vivo X系列
          'V2072A', 'V2080A', 'V2145A', // Vivo S系列
        ];

      case 'oppo':
      case 'realme':
        return [
          // 官方列表中的OPPO设备
          'CPH2023', 'CPH2025', 'CPH2207', 'CPH2173', // OPPO Find系列
          'CPH2035', 'CPH2037', 'CPH2043', // OPPO Reno系列
          'CPH2127', 'CPH2131', 'CPH2135', // OPPO K系列
          'CPH1931', 'CPH1969', 'CPH2015', // OPPO A系列
          // Realme设备
          'RMX2202', 'RMX3085', 'RMX3031', 'RMX3142', // Realme GT系列
          'RMX3161', 'RMX3171', 'RMX3191', // Realme Q系列
        ];

      case 'oneplus':
        return [
          // 官方列表中的OnePlus设备
          'LE2100', 'LE2110', 'LE2120', // OnePlus 9系列
          'IN2010', 'IN2020', 'IN2025', // OnePlus 8系列
          'HD1900', 'HD1901', 'HD1903', // OnePlus 7系列
          'GM1900', 'GM1901', 'GM1903', // OnePlus 7 Pro系列
          'KB2000', 'KB2001', 'KB2003', // OnePlus 8T系列
        ];

      case 'samsung':
        return [
          // 官方列表中的Samsung设备
          'SM-G991', 'SM-G996', 'SM-G998', // Galaxy S21系列
          'SM-G981', 'SM-G985', 'SM-G988', // Galaxy S20系列
          'SM-G973', 'SM-G975', 'SM-G977', // Galaxy S10系列
          'SM-N981', 'SM-N985', 'SM-N986', // Galaxy Note20系列
        ];

      case 'huawei':
      case 'honor':
        return [
          // 官方列表中的华为/荣耀设备
          // Mate系列
          'LIO-', 'VOG-', 'TAS-', 'OCE-', 'ANA-',
          // P系列
          'ELE-', 'CLT-', 'LYA-', 'VOG-', 'ANA-', 'ALT-',
          // Nova系列
          'PAR-', 'SEA-', 'WLZ-', 'JEF-', 'JSC-',
          // 荣耀系列
          'YAL-', 'BMH-', 'BKL-', 'PCT-', 'RKY-',
        ];

      case 'rog':
        return [
          // ROG游戏手机
          'ASUS_I001D', 'ASUS_I001DA', // ROG Phone 2
        ];

      case 'nubia':
      case 'blackshark':
        return [
          // 努比亚红魔系列
          'NX629J', 'NX659J', 'NX679J', // 红魔3/6系列
          // 黑鲨游戏手机
          'SKR-A0', 'SKR-H0', 'DLT-A0', // 黑鲨2/3/4系列
        ];

      case 'lenovo':
        return [
          // 联想设备
          'L70081', 'L71091', 'L79031', // 联想拯救者系列
          'TB-J606F', 'TB-J607F', // 联想平板
        ];

      default:
        return [];
    }
  }


}

// 创建单例实例
const deviceCapability = new DeviceCapability();

// 导出便捷方法
export default {
  /**
   * 检测设备能力并返回结果
   * @returns {Object} 设备能力检测结果
   */
  detect() {
    return deviceCapability.detectCapabilities();
  },

  /**
   * 生成AR系统配置
   * @param {boolean} isARSupported 可选，是否支持AR
   * @param {boolean} isDepthMaskSupported 可选，是否支持深度剔除
   * @returns {string} AR系统配置字符串
   */
  generateConfig(isARSupported, isDepthMaskSupported) {
    return deviceCapability.generateARSystemConfig(isARSupported, isDepthMaskSupported);
  },

  /**
   * 获取完整的设备能力报告
   * @returns {Object} 完整报告
   */
  getReport() {
    return deviceCapability.getCapabilityReport();
  },

  /**
   * 获取设备能力实例（用于高级用法）
   * @returns {DeviceCapability} 设备能力检测实例
   */
  getInstance() {
    return deviceCapability;
  }
};
