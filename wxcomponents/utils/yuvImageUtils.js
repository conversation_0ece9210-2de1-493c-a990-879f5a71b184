import { loadWasmFromAssets } from './wasmLoader';
let wasmExports = null;
let memoryBuffer = null;
let memBuffer = null;
let startTimeStamp

const initWasmExports = async () => {
  try {
    wasmExports = await loadWasmFromAssets();
  } catch (err) {
    console.error('Failed to use Wasm:', err);
  }
}

const initMemory = (memory, totalSize) => {
  // 初始化或复用线性内存缓冲区
  if (!memBuffer || memBuffer.length < totalSize) {
    memBuffer = new Uint8Array(memory.buffer);
  }
};

const convertYuvToRgba = async (yBuffer, uvBuffer, width, height) => {
  try {
    startTimeStamp = Date.now()
    console.log('start convertYuvToRgba')
    if (!wasmExports) {
      await initWasmExports()
    }

    const memory = wasmExports.memory;
    const totalSize = yBuffer.byteLength + uvBuffer.byteLength + 4 * width * height;

    // 初始化内存
    initMemory(memory, totalSize);

    // 定义偏移位置
    const yOffset = 0;
    const uvOffset = yOffset + yBuffer.byteLength;
    const rgbaOffset = uvOffset + uvBuffer.byteLength;

    // 写入 YUV 数据到线性内存
    memBuffer.set(new Uint8Array(yBuffer), yOffset);
    memBuffer.set(new Uint8Array(uvBuffer), uvOffset);

    // 调用 Wasm 函数
    wasmExports.yuv_to_rgb(yOffset, uvOffset, rgbaOffset, width, height);

    // 获取转换后的数据（避免重复拷贝）
    const rgbaView = new Uint8Array(memBuffer.buffer, rgbaOffset, width * height * 4);

    console.log('end convertYuvToRgba: ' + (Date.now() - startTimeStamp) / 1000 + 's')
    return rgbaView;
  } catch (err) {
    console.error('Failed to use Wasm:', err);
  }
};

const drawToCanvas = (rgba, width, height) => {
  const startTimeStamp = Date.now();
  console.log('start drawToCanvas');

  // 创建离屏画布
  const canvas = wx.createOffscreenCanvas({
    type: '2d'
  });
  const ctx = canvas.getContext('2d');

  // 旋转后宽高互换
  // canvas.width = height;
  // canvas.height = width;
  canvas.width = width;
  canvas.height = height;

  const imageData = ctx.createImageData(width, height);
  imageData.data.set(rgba);

  ctx.putImageData(imageData, 0, 0);

  // // 创建临时画布并绘制原始 RGBA 数据
  // const tempCanvas = wx.createOffscreenCanvas({
  //   type: '2d'
  // });
  // const tempCtx = tempCanvas.getContext('2d');

  // // 临时画布的宽高设置为原始图像大小
  // tempCanvas.width = width;
  // tempCanvas.height = height;

  // // 创建 ImageData 并填充 RGBA 数据
  // const imageData = tempCtx.createImageData(width, height);
  // imageData.data.set(rgba);

  // // 将 RGBA 数据放入临时画布
  // tempCtx.putImageData(imageData, 0, 0);

  // 旋转并绘制到目标画布
  // ctx.translate(height, 0); // 将绘图原点移到目标位置
  // ctx.rotate(Math.PI / 2); // 顺时针旋转 90 度

  // 将图像绘制到目标画布，宽高互换
  //ctx.drawImage(tempCanvas, 0, 0);

  console.log('end drawToCanvas: ' + (Date.now() - startTimeStamp) / 1000 + 's');

  return canvas;
};

const saveBase64ToFile = (base64) => {
    startTimeStamp = Date.now()
    const fileManager = wx.getFileSystemManager();
    const filePath = `${wx.env.USER_DATA_PATH}/xrframe_${Date.now()}.jpg`;

    fileManager.writeFile({
      filePath,
      data: base64, // 去掉前缀
      encoding: 'base64',
      success: () => {
        console.log('File saved successfully:', filePath);
        console.log('end saveFile: ' + (Date.now() - startTimeStamp) / 1000 + 's')
        saveToAlbum(filePath)
      },
      fail: (err) => {
        console.error('Failed to save file:', err);
      },
    });
}

const saveBufferToFile = (buffer) => {
  startTimeStamp = Date.now()
  const fileManager = wx.getFileSystemManager();
  const filePath = `${wx.env.USER_DATA_PATH}/xrframe_${Date.now()}.jpg`;

  fileManager.writeFile({
      filePath,
      data: buffer, // 去掉前缀
      encoding: 'binary',
      success: () => {
          console.log('File saved successfully:', filePath);
          console.log('end saveFile: '+(Date.now()-startTimeStamp)/1000+'s')
          saveToAlbum(filePath)
      },
      fail: (err) => {
          console.error('Failed to save file:', err);
      },
  });
}

const saveToAlbum = (filePath) => {
  startTimeStamp = Date.now()
  wx.saveImageToPhotosAlbum({
      filePath,
      success: () => {
          wx.showToast({ title: "Saved to album!", icon: "success" });
          console.log('end saveFile: '+(Date.now()-startTimeStamp)/1000+'s')
      },
      fail: (err) => {
          console.error("Failed to save to album:", err);
          wx.showToast({ title: "Save failed!", icon: "error" });
      },
  });
}

const yuvToImage = async (yBuffer, uvBuffer, width, height) => {
  try {
    const rgba = await convertYuvToRgba(yBuffer, uvBuffer, width, height); // YUV 转 RGB
    const canvas = drawToCanvas(rgba, width, height); // 绘制到 Canvas
    //const imageInfo = await saveBase64ToFile(canvas, width)
    const imageBase64 = canvas.toDataURL('image/jpg').split(',')[1]
    const imageBuffer = wx.base64ToArrayBuffer(imageBase64)
    //saveBufferToFile(imageBuffer)
    return imageBuffer
  } catch (err) {
    console.error("Error processing YUV data", err);
  }
}

/**
 * 按比例裁剪 base64 格式的图片
 * @param {string} base64Image - base64 格式的图片数据（可以包含或不包含 data:image 前缀）
 * @param {number} aspectRatio - 目标宽高比（宽/高）
 * @returns {Promise<string>} - 返回裁剪后的 base64 图片
 */
const cropBase64ImageByRatio = (base64Image, aspectRatio) => {
  return new Promise((resolve, reject) => {
    startTimeStamp = Date.now()
    console.log('start cropBase64ImageByRatio')

    // 确保 base64 字符串格式正确（移除可能的前缀）
    const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '')

    // 创建离屏 canvas
    const canvas = wx.createOffscreenCanvas({
      type: '2d'
    })
    const ctx = canvas.getContext('2d')

    // 创建图片对象
    const img = canvas.createImage()

    img.onload = () => {
      const { width, height } = img
      console.log(`Original image size: ${width}x${height}`)

      // 计算裁剪区域
      let cropX = 0
      let cropY = 0
      let cropWidth = width
      let cropHeight = height

      const originalRatio = width / height

      if (originalRatio > aspectRatio) {
        // 原图过宽，需要裁剪宽度
        cropWidth = height * aspectRatio
        cropX = (width - cropWidth) / 2
      } else if (originalRatio < aspectRatio) {
        // 原图过高，需要裁剪高度
        cropHeight = width / aspectRatio
        cropY = (height - cropHeight) / 2
      }

      // 设置 canvas 尺寸为裁剪后的尺寸
      canvas.width = cropWidth
      canvas.height = cropHeight

      // 绘制裁剪后的图片
      ctx.drawImage(
        img,
        cropX, cropY, cropWidth, cropHeight,
        0, 0, cropWidth, cropHeight
      )

      // 转换为 base64
      const croppedBase64 = canvas.toDataURL('image/jpeg', 0.9)

      console.log(`Cropped image size: ${cropWidth}x${cropHeight}`)
      console.log('end cropBase64ImageByRatio: ' + (Date.now() - startTimeStamp) / 1000 + 's')

      resolve(croppedBase64)
    }

    img.onerror = (err) => {
      console.error('Failed to load image:', err)
      reject(new Error('Failed to load image'))
    }

    // 设置图片源为 base64 数据
    img.src = `data:image/jpeg;base64,${base64Data}`
  })
}

const arrayBufferToBase64 = (buffer) => {
  let binary = ''
  const bytes = new Uint8Array(buffer)
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return wx.arrayBufferToBase64(buffer)
}


/**
 * 按比例裁剪图片，支持 ArrayBuffer 或 base64 输入
 * @param {ArrayBuffer|string} image - 图片数据，可以是 ArrayBuffer 或 base64 字符串
 * @param {number} aspectRatio - 目标宽高比（宽/高）
 * @param {string} [outputFormat='base64'] - 输出格式，'base64' 或 'buffer'
 * @returns {Promise<string|ArrayBuffer>} - 返回裁剪后的图片
 */
const cropImageByRatio = (image, aspectRatio, outputFormat = 'base64') => {
  // 判断输入类型
  const isBase64 = typeof image === 'string'

  if (isBase64) {
    return cropBase64ImageByRatio(image, aspectRatio)
      .then(base64Result => {
        if (outputFormat === 'buffer') {
          // 如果需要 buffer 输出，转换 base64 为 ArrayBuffer
          return wx.base64ToArrayBuffer(base64Result.replace(/^data:image\/\w+;base64,/, ''))
        }
        return base64Result
      })
  } else {
    // 输入是 ArrayBuffer，先转为 base64
    const base64Image = `data:image/jpeg;base64,${arrayBufferToBase64(image)}`
    return cropBase64ImageByRatio(base64Image, aspectRatio)
      .then(base64Result => {
        if (outputFormat === 'buffer') {
          // 如果需要 buffer 输出，转换 base64 为 ArrayBuffer
          return wx.base64ToArrayBuffer(base64Result.replace(/^data:image\/\w+;base64,/, ''))
        }
        return base64Result
      })
  }
}



export {
  yuvToImage,
  saveBase64ToFile,
  saveBufferToFile,
  saveToAlbum,
  cropImageByRatio
}
