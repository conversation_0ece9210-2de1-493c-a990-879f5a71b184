const xr = wx.getXrFrameSystem()
import {
	composeRotationMappingMatrix,
	getTransformedPose,
	radianToAngle
} from '../utils/transformUtils'
import CameraStateManager from './camera-state'

// 临时解决方案：本地定义函数以防导入失败
const localComposeRotationMappingMatrix = function() {
  let rotationMappingMatrix = xr.Matrix4.composeTQS(xr.Vector3.ZERO, xr.Quaternion.fromEulerAngles(xr.Vector3.createFromNumber(0, -3*Math.PI/2, Math.PI)), xr.Vector3.ONE)
  rotationMappingMatrix = rotationMappingMatrix.inverse()
  const rotationMappingMatixArray = rotationMappingMatrix.toArray()
  console.log('rotationMappingMatixArray: '+rotationMappingMatixArray)
  return rotationMappingMatixArray
}

const localRadianToAngle = function(radian) {
  return radian * 180 / Math.PI
}

const localMatrixToPose = function(matrix) {
  const posColumnArray = matrix.getColumn(3).toArray()
  let pos = xr.Vector3.createFromNumber(posColumnArray[0], posColumnArray[1], posColumnArray[2])
  let quat = xr.Quaternion.createFromMatrix4(matrix)
  return {position: pos, quaternion: quat}
}

const localGetTransformedPose = function(transformMatrixArray, pose) {
  let slamQuaternion = pose.quaternion
  let slamMatrix = xr.Matrix4.composeTQS(pose.position, slamQuaternion, xr.Vector3.ONE)
  const transformMatrix = xr.Matrix4.createFromArray(transformMatrixArray)
  let vpsMatrix = transformMatrix.multiply(slamMatrix)
  let correctedPose = localMatrixToPose(vpsMatrix)
  return correctedPose
}

let prevPos = null

Component({
	properties: {
		initialVpsTracked: Boolean,
		transformMatrix: null,
		onArReady: Function,
		onArInit: Function,
		onArLost: Function,
		onArTracked: Function,
		onAssetListLoaded: Function,
		onCameraPoseTick: Function,
		assetList: Array
	},
	data: {
		loaded: true,
		arReady: false,
		arSystemConfig: 'modes:Plane; planeMode: 1;', // 默认基础配置
		isARSupported: false,
		isDepthMaskSupported: false,
		activeAnchorList: [],
		isArCoordSystemInit: false,
		isTracking: false, // 是否正在实时进行用户位置追踪
		cameraTrackingState: -1,
		assetListLoaded: false
	},
	cameraTrs: null,
	arRawData: null,
	camera: null,
	cameraStateManager: null,
	renderController: null,
	assetMap: null,
	lifetimes: {
		attached() {
			console.log('data', this.data)
			// 检测设备能力并配置AR系统
			// this.detectDeviceCapabilities();
		},
		detached() {
			this.pauseTracking()
			this.unloadAssetList()
			if (this.renderController) {
				this.renderController.clear()
				this.renderController = null
			}
		}
	},
	observers: {
		assetList: async function (assetList) {
			if (!assetList || assetList.length === 0) {
				console.log('assetList is empty')
				return
			}
			if (this.data.assetListLoaded) {
				console.log('assetList has been loaded')
				return
			}
			try {
				await this.loadAssetList(assetList)
				console.log('资产列表加载完成')
			} catch (err) {
				console.error('资产列表加载失败:', err)
			}
		}
	},
	methods: {
		/**
		 * 检测设备能力
		 */
		detectDeviceCapabilities() {
			try {
				// 使用设备能力检测工具
				const report = DeviceCapability.getReport()
				const { capabilities, arSystemConfig } = report

				this.setData({
					isARSupported: capabilities.isARSupported,
					isDepthMaskSupported: capabilities.isDepthMaskSupported,
					arSystemConfig: arSystemConfig
				})

				console.log('设备能力检测报告:', report)
			} catch (error) {
				console.error('设备能力检测失败:', error)
				// 使用默认配置
				this.setData({
					arSystemConfig: 'modes:Plane; planeMode: 1;'
				})
			}
		},
		handleReady: async function ({ detail }) {
			this.scene = detail.value
			this.mat = new (wx.getXrFrameSystem().Matrix4)()
			try {
			  await this.loadAssetList(this.data.assetList)
			} catch (err) {
			  console.error('资产列表加载失败:', err)
			}
		},
		handleARReady: function ({ detail }) {
			console.log('arReady', this.scene.ar.arVersion)
			this.setData({ arReady: true })
			this.cameraTrs = this.scene.getElementById('camera').getComponent(xr.Transform)
			this.camera = this.scene.getElementById('camera').getComponent(xr.Camera)
			//this.planeTracker = this.scene.getElementById('plane').getComponent(xr.ARTracker)
			this.arRawData = this.scene.ar.getARRawData()
			this.cameraStateManager = new CameraStateManager()
			// this.renderController = new RenderController(this.cameraTrs, 20.0);
			this.resumeTracking()
			this.data.onArReady()
		},
		handleOnTick(deltaTime) {
			try {
				let worldPos = this.cameraTrs.position
				let worldQuat = this.cameraTrs.quaternion
				let originalPose = {
					position: xr.Vector3.createFromNumber(worldPos.x, worldPos.y, worldPos.z),
					quaternion: xr.Quaternion.fromEulerAngles(
						xr.Vector3.createFromNumber(
							this.cameraTrs.rotation.x,
							this.cameraTrs.rotation.y,
							this.cameraTrs.rotation.z
						)
					)
				}
				if (!prevPos) {
					prevPos = xr.Vector3.createFromNumber(worldPos.x, worldPos.y, worldPos.z)
				}
				const currCameraTrackingState = this.cameraStateManager.getCameraState(prevPos, worldPos)
				if (!this.data.isArCoordSystemInit) {
					if (currCameraTrackingState === 1) {
						console.log('isArActive: ar init')
						this.setData({
							isArCoordSystemInit: true,
							cameraTrackingState: 1
						})
						const worldEuler = worldQuat.toEulerAngles()
						console.warn('func: ', composeRotationMappingMatrix)
						console.log(
							'--------worldPose的值是------: position: x: ' +
								originalPose.position.x +
								', y: ' +
								originalPose.position.y +
								', z: ' +
								originalPose.position.z +
								', rotation: x: ' +
								(radianToAngle || localRadianToAngle)(worldEuler.x) +
								', y: ' +
								(radianToAngle || localRadianToAngle)(worldEuler.y) +
								', z: ' +
								(radianToAngle || localRadianToAngle)(worldEuler.z)
						)
						// 使用本地函数作为备用
						this.arSystemTransformMatrix = composeRotationMappingMatrix || localComposeRotationMappingMatrix()
						this.data.onArInit()
					}
				} else {
					if (this.data.cameraTrackingState === 1 && currCameraTrackingState === 2) {
						this.setData({
							cameraTrackingState: 2
						})
						this.data.onArLost()
						return
					}

					if (this.data.cameraTrackingState === 2 && currCameraTrackingState === 1) {
						this.setData({
							cameraTrackingState: 1
						})
						this.data.onArTracked()
					}
					// console.log("prevPos: x: "+prevPos.x+", y: "+prevPos.y+", z: "+prevPos.z)
					// console.log("worldPos: x: "+worldPos.x+", y: "+worldPos.y+", z: "+worldPos.z)
					prevPos.setValue(worldPos.x, worldPos.y, worldPos.z)
					let arSystemCorrectedPose = {}
					if (this.arSystemTransformMatrix) {
						arSystemCorrectedPose = (getTransformedPose || localGetTransformedPose)(
							Object.values(this.arSystemTransformMatrix),
							originalPose
						)
						this.updateCameraPose(arSystemCorrectedPose)
					}
					// this.triggerEvent('cameraPoseTick', {
					//   cameraPos: worldPos,
					//   cameraQuat: worldQuat,
					//   arRawData: this.arRawData
					// })
					this.data.onCameraPoseTick({
						cameraPos: arSystemCorrectedPose?.position,
						cameraQuat: arSystemCorrectedPose?.quaternion,
						arRawData: this.arRawData
					})
				}
				if (this.data.initialVpsTracked) {
					if (this.renderController) {
						this.renderController.update()
					}
				}
				//console.log('cameraState: '+currCameraTrackingState)
			} catch (err) {
				console.log('[onTick] error: ', err)
			}
		},
		async loadAssetList(assetList) {
			if (!assetList?.length || assetList.length === 0) {
				console.warn('No assets to load.')
				return
			}

			// 将 list 转为 Map，便于索引或后续管理
			const assetMap = new Map(assetList.map((asset) => [asset.id, asset]))

			try {
				const assetPromises = Array.from(assetMap.entries()).map(([id, asset]) =>
					this.loadGLTFAsset(asset.id, asset.src).then((result) => {
						if (!result) throw new Error(`Asset ${id} failed to load`)
						return result
					})
				)
				await Promise.all(assetPromises)
				this.assetMap = assetMap
				this.setData({ assetListLoaded: true })
				this.data.onAssetListLoaded()
				console.log('assetList loaded')
			} catch (err) {
				console.error('Failed to load one or more assets:', err)
				wx.showToast({
					title: '加载项目资产失败',
					icon: 'none',
					duration: 2000
				})
				throw err
			}
		},
		unloadAssetList() {
			if (this.assetMap) {
				Array.from(this.assetMap.values()).forEach((asset) => {
					this.scene.assets.releaseAsset(asset.type, asset.id)
				})
				this.assetMap = null
			}
		},
		updateCameraPose(correctedPose) {
			try {
				if (!correctedPose || !correctedPose.position || !correctedPose.quaternion) {
					throw new Error('缺少位置或旋转数据')
				}
				this.cameraTrs.position.set(
					xr.Vector3.createFromNumber(
						correctedPose.position.x,
						correctedPose.position.y,
						correctedPose.position.z
					)
				)
				this.cameraTrs.quaternion.set(correctedPose.quaternion)
			} catch (err) {
				console.log('[updateCameraPose]: ' + err)
			}
		},
		pauseTracking() {
			if (this.data.isTracking) {
				this.scene.event.clear('tick')
				this.onTickCallback = null // 清空回调引用
				this.setData({
					isTracking: false
				})
			}
		},
		resumeTracking() {
			if (!this.data.isTracking) {
				if (!this.cameraTrs) {
					console.error('Cannot find cameraTrs')
					return
				}
				if (!this.arRawData) {
					console.error('Cannot find arRawData')
					return
				}

				this.onTickCallback = (deltaTime) => this.handleOnTick(deltaTime)
				this.scene.event.add('tick', this.onTickCallback)
				this.setData({
					isTracking: true
				})
			}
		},
		spawnCameraPoseMesh() {
			const cameraEuler = this.cameraTrs.quaternion.toEulerAngles()
			const cameraPos = this.cameraTrs.position

			const meshNode = this.scene.createElement(xr.XRNode, {
				id: 'meshNode-' + Math.random(),
				rotation: `${(cameraEuler.x * 180) / Math.PI} ${(cameraEuler.y * 180) / Math.PI} ${(cameraEuler.z * 180) / Math.PI}`,
				position: `${cameraPos.x} ${cameraPos.y} ${cameraPos.z}`
			})

			const meshX = this.scene.createElement(xr.XRMesh, {
				position: `0.05 0 0`,
				scale: `0.1 0.02 0.02`,
				geometry: 'cube',
				uniforms: 'u_baseColorFactor:0.7 0.3 0.3 1'
			})
			const meshY = this.scene.createElement(xr.XRMesh, {
				position: `0 0.05 0`,
				scale: `0.02 0.1 0.02`,
				geometry: 'cube',
				uniforms: 'u_baseColorFactor:0.3 0.7 0.3 1'
			})
			const meshZ = this.scene.createElement(xr.XRMesh, {
				position: `0 0 0.05`,
				scale: `0.02 0.02 0.1`,
				geometry: 'cube',
				uniforms: 'u_baseColorFactor:0.3 0.3 0.7 1'
			})
			const root = this.scene.getElementById('root')
			if (!root) {
				console.error('Root element not found')
				return
			}
			meshNode.addChild(meshX)
			meshNode.addChild(meshY)
			meshNode.addChild(meshZ)
			root.addChild(meshNode)
			if (this.renderController) {
				this.renderController.register(meshNode)
			}
			console.log(
				'生成queryPose位姿: position x: ' +
					cameraPos.x +
					', y: ' +
					cameraPos.y +
					', z: ' +
					cameraPos.z +
					', rotation x: ' +
					(radianToAngle || localRadianToAngle)(cameraEuler.x) +
					', y: ' +
					(radianToAngle || localRadianToAngle)(cameraEuler.y) +
					', z: ' +
					(radianToAngle || localRadianToAngle)(cameraEuler.z)
			)
		},
		async spawnAnchorItem(anchor) {
			if (!anchor || !anchor.position || !anchor.rotation || !anchor.scale) {
				console.error('Invalid anchor data')
				return
			}
			const root = this.scene.getElementById('root')
			if (!root) {
				console.error('Root element not found')
				return
			}

			// const mesh = this.scene.createElement(xr.XRMesh, {
			//   rotation: "-90 0 0",
			//   position: "0 0 0",
			//   scale: `${anchor.scale.x*0.21} ${anchor.scale.y} ${anchor.scale.z*0.297}`,
			//   geometry: "plane",
			//   material: "standard-mat",
			//   uniforms: "u_baseColorMap: planeTexture",
			//   states: "cullOn: false"
			// });

			try {
				let meshNode = root.getChildByName(anchor.name)
				if (meshNode) {
					const meshNodeTrs = meshNode.getComponent(xr.Transform)
					meshNodeTrs.position.setValue(anchor.position.x, anchor.position.y, anchor.position.z)
					meshNodeTrs.rotation.setValue(
						(anchor.rotation.x * Math.PI) / 180,
						(anchor.rotation.y * Math.PI) / 180,
						(anchor.rotation.z * Math.PI) / 180
					)
					const gltfTrs = meshNode.getChildByName(`gltf-${anchor.id}`).getComponent(xr.Transform)
					gltfTrs.scale.setValue(anchor.scale.x, anchor.scale.y, anchor.scale.z)
				} else {
					meshNode = this.scene.createElement(xr.XRNode, {
						id: anchor.id,
						name: anchor.name,
						position: `${anchor.position.x} ${anchor.position.y} ${anchor.position.z}`,
						rotation: `${anchor.rotation.x} ${anchor.rotation.y} ${anchor.rotation.z}`
					})

					// Load or fetch GLTF asset
					let gltfAsset = await this.loadGLTFAsset(anchor.assetId, anchor.url)
					if (!gltfAsset) {
						console.error(`Failed to load GLTF asset for: ${anchor.assetId}`)
						return
					}

					// Add GLTF instances for each world pose
					const gltf = this.scene.createElement(xr.XRGLTF, {
						name: `gltf-${anchor.id}`,
						position: `0 0 0`,
						rotation: `0 0 180`,
						scale: `${anchor.scale.x} ${anchor.scale.y} ${anchor.scale.z}`,
						'anim-autoplay': ''
					})

					gltf.getComponent(xr.GLTF).setData({ model: gltfAsset.value })

					meshNode.addChild(gltf)
					root.addChild(meshNode)

					console.log(`GLTF asset loaded and added to scene: ${anchor.assetId}`)
				}
				if (this.renderController) {
					this.renderController.register(meshNode)
				}
			} catch (error) {
				console.error(`Error loading GLTF item ${anchor.id}:`, error)
			}
		},
		async loadGLTFAsset(assetId, url) {
			const scene = this.scene

			if (!scene) {
				console.log('scene is undefined')
				return
			}
			// Check if asset already exists
			let gltfAsset = scene.assets.getAssetWithState('gltf', assetId)
			console.log(
				assetId + ' gltfAsset is null: ' + !gltfAsset.value + ', state: ' + gltfAsset.state
			)
			if (!gltfAsset || !gltfAsset.value) {
				try {
					console.log('loadGLTFAsset id: ' + assetId)
					// Load asset if not found
					gltfAsset = await scene.assets.loadAsset({
						type: 'gltf',
						assetId,
						src: url
					})
				} catch (err) {
					console.error('Asset loading error:', err)
					return null
				}
			}

			return gltfAsset
		},
		async takeScreenshot(saveType) {
			// 判断当前客户端是否支持分享系统
			const supported = this.scene.share.supported

			if (!supported) {
				console.log('客户端不支持分享系统')
			}

			// 截取配置，可选`type`为`jpg`或`png`，在为`jpg`时，可配置`0~1`的`quality`
			// 以下是默认值
			const options = {
				type: 'jpg',
				quality: 0.8
			}

			// const buffer = await this.scene.share.captureToArrayBufferAsync(options);
			// return buffer
			const base64 = await this.scene.share.captureToDataURLAsync(options)
			return base64
		}
	}
})
