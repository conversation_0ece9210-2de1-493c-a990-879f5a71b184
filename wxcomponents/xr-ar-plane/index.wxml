<xr-scene ar-system="modes:Plane; planeMode: 1;" bind:ready="handleReady" bind:ar-ready="handleARReady">
  <!-- vio + marker 模式下 planeMode 需设置为 1 (只允许水平面识别) -->
  <xr-assets>
    <xr-asset-load type="env-data" asset-id="outdoor" src="https://mmbizwxaminiprogram-1258344707.cos.ap-guangzhou.myqcloud.com/xr-frame/demo/env-test.bin"/>
  </xr-assets>
  <xr-env env-data="outdoor" />
  <xr-node>

    <!-- plane -->
<!--    <xr-ar-tracker id='plane' mode="Plane">
      <xr-gltf model="anchor"></xr-gltf>
    </xr-ar-tracker> -->

<!--  <xr-node id="meshOrigin">
    <xr-mesh id="mesh-x" node-id="mesh-x" position="0.25 0 0"  scale="0.5 0.02 0.02" geometry="cube" uniforms="u_baseColorFactor:0.7 0.3 0.3 1" ></xr-mesh>
    <xr-mesh id="mesh-y" node-id="mesh-y" position="0 0.25 0"  scale="0.02 0.5 0.02" geometry="cube" uniforms="u_baseColorFactor:0.3 0.7 0.3 1"></xr-mesh>
    <xr-mesh id="mesh-z" node-id="mesh-z" position="0 0 0.25"  scale="0.02 0.02 0.5" geometry="cube" uniforms="u_baseColorFactor:0.3 0.3 0.7 1"></xr-mesh>
  </xr-node> -->

  <xr-node>
    <xr-shadow id="root"></xr-shadow>
  </xr-node>

    <xr-node node-id='ar-camera'>
      <xr-camera
      id="camera" clear-color="0.925 0.925 0.925 1"
      background="ar" is-ar-camera
    >
    </xr-camera>
    </xr-node>
  </xr-node>
  <xr-node node-id="lights">
    <xr-light type="ambient" color="1 1 1" intensity="1" />
    <!-- <xr-light type="directional" rotation="180 0 0" color="1 1 1" intensity="3" /> -->
  </xr-node>
</xr-scene>
