<script setup>
import { useStore } from 'vuex'
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
// import { setLogged } from './utils/loginPromise.js'
// import { AppModel } from './api/app.js'

// const appModel = new AppModel()
const store = useStore()

const setNavbarInfoStore = (info) => store.dispatch('setNavbarInfo', info)

// Set Navbar Function
function getNavBarInfo() {
	const systemInfo = wx.getSystemInfoSync()
	const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
	const statusBarHeight = systemInfo.statusBarHeight
	const navHeight = menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2
	const barHeight = navHeight + statusBarHeight
	setNavbarInfoStore({
		statusBarHeight,
		navHeight,
		barHeight
	})
}

onLaunch((options) => {
	console.log('App Launch')
	getNavBarInfo()
	// TODO：目前1154 默认分享行为，后续增加内容则添加page字段判断
	if (options.scene === 1154) {
		store.dispatch('setSharePageFlag', true)
	}
	if (options.scene === 1155) {
		uni.redirectTo({
			url: 'pages/startup/index'
		})
	}
})

onShow((options) => {
	console.log('App Show')
	// TODO：目前1154 默认分享行为，后续增加内容则添加page字段判断
	if (options.scene === 1154) {
		store.dispatch('setSharePageFlag', true)
	}
	if (options.scene === 1155) {
		uni.redirectTo({
			url: 'pages/startup/index'
		})
	}
})

onHide(() => {
	console.log('App Hide')
})
</script>

<style>
/*每个页面公共css */
</style>
